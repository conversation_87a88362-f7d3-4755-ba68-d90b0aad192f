/**
 * 数据库连接模块
 * 使用mysql2连接MariaDB数据库
 */

const mysql = require('mysql2/promise');
const { getConfig } = require('../config');

// 数据库连接配置
const dbConfig = {
    host: getConfig('database.host'),
    user: getConfig('database.user'),
    password: getConfig('database.password'),
    database: getConfig('database.database'),
    waitForConnections: true,
    connectionLimit: getConfig('database.connectionLimit'),
    charset: getConfig('database.charset')
};

// 创建连接池
const db = mysql.createPool(dbConfig);

// 测试数据库连接
async function testConnection() {
    try {
        const [rows] = await db.query('SELECT 1 as test');
        console.log('✅ 数据库连接成功');
        return true;
    } catch (error) {
        console.error('❌ 数据库连接失败:', error.message);
        return false;
    }
}

// 获取连接池状态
function getPoolStatus() {
    return {
        totalConnections: db.pool._allConnections.length,
        freeConnections: db.pool._freeConnections.length,
        acquiringConnections: db.pool._acquiringConnections.length
    };
}

// 优雅关闭连接池
async function closePool() {
    try {
        await db.end();
        console.log('✅ 数据库连接池已关闭');
    } catch (error) {
        console.error('❌ 关闭数据库连接池失败:', error.message);
    }
}

module.exports = {
    db,
    testConnection,
    getPoolStatus,
    closePool
};
