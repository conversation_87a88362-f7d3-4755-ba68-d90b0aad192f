{"name": "sinon", "description": "JavaScript test spies, stubs and mocks.", "keywords": ["sinon", "test", "testing", "unit", "stub", "spy", "fake", "time", "clock", "mock", "xhr", "assert"], "version": "17.0.1", "homepage": "https://sinonjs.org/", "author": "<PERSON>", "repository": {"type": "git", "url": "http://github.com/sinonjs/sinon.git"}, "bugs": {"url": "http://github.com/sinonjs/sinon/issues"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/sinon"}, "license": "BSD-3-<PERSON><PERSON>", "scripts": {"test-node": "mocha --recursive -R dot \"test/**/*-test.js\"", "test-dev": "npm run test-node -- --watch -R min", "test-headless": "mochify --no-detect-globals --recursive -R dot --grep <PERSON> --invert  \"test/**/*-test.js\"", "test-coverage": "nyc npm run test-headless -- --transform [ babelify --ignore [ test ] --plugins [ babel-plugin-istanbul ] ]", "test-cloud": "npm run test-headless -- --wd", "test-webworker": "mochify --no-detect-globals --https-server 0 --no-request-interception test/webworker/webworker-support-assessment.js", "test-esm-support": "mocha test/es2015/module-support-assessment-test.mjs", "check-esm-bundle-runs-in-browser": "node test/es2015/check-esm-bundle-is-runnable.js", "test-docker-image": "docker-compose up", "test-runnable-examples": "docs/release-source/release/examples/run-test.sh", "test": "npm run test-node && npm run test-headless && npm run test-webworker", "check-dependencies": "dependency-check package.json --no-dev --ignore-module esm", "build": "node ./build.cjs", "dev-docs": "cd docs; rsync -r --delete release-source/ releases/dev; npm run serve-docs", "build-docs": "cd docs; bundle exec jekyll build", "serve-docs": "cd docs; bundle exec jekyll serve --incremental --verbose --livereload", "lint": "eslint --max-warnings 0 '**/*.{js,cjs,mjs}'", "unimported": "unimported .", "pretest-webworker": "npm run build", "prebuild": "rimraf pkg && npm run check-dependencies", "postbuild": "npm run test-esm-support && npm run check-esm-bundle-runs-in-browser", "prebuild-docs": "./scripts/update-compatibility.js", "prepublishOnly": "npm run build", "prettier:check": "prettier --check '**/*.{js,css,md}'", "prettier:write": "prettier --write '**/*.{js,css,md}'", "preversion": "./scripts/preversion.sh", "version": "./scripts/version.sh", "postversion": "./scripts/postversion.sh"}, "nyc": {"instrument": false, "temp-dir": "coverage/.nyc_output", "reporter": ["text", "lcovonly"]}, "lint-staged": {"**/*.{js,css,md}": "prettier --write", "*.js": "eslint --quiet", "*.mjs": "eslint --quiet --ext mjs --parser-options=sourceType:module"}, "dependencies": {"@sinonjs/commons": "^3.0.0", "@sinonjs/fake-timers": "^11.2.2", "@sinonjs/samsam": "^8.0.0", "diff": "^5.1.0", "nise": "^5.1.5", "supports-color": "^7.2.0"}, "devDependencies": {"@babel/core": "^7.23.2", "@sinonjs/eslint-config": "^4.1.0", "@sinonjs/eslint-plugin-no-prototype-methods": "^0.1.1", "@sinonjs/referee": "^10.0.1", "@studio/changes": "^2.2.0", "babel-plugin-istanbul": "^6.1.1", "babelify": "^10.0.0", "browserify": "^16.5.2", "debug": "^4.3.4", "dependency-check": "^4.1.0", "lint-staged": "^15.0.2", "mocha": "^10.2.0", "mochify": "^9.2.0", "nyc": "^15.1.0", "prettier": "^3.0.3", "puppeteer": "^21.4.0", "rimraf": "^5.0.5", "semver": "^7.5.4", "shelljs": "^0.8.5", "unimported": "^1.30.0"}, "files": ["lib", "pkg", "scripts/support-sinon.js", "AUTHORS", "CONTRIBUTING.md", "CHANGELOG.md", "LICENSE", "README.md"], "browser": "./lib/sinon.js", "main": "./lib/sinon.js", "module": "./pkg/sinon-esm.js", "exports": {".": {"browser": "./pkg/sinon-esm.js", "require": "./lib/sinon.js", "import": "./pkg/sinon-esm.js"}, "./*": "./*"}, "type": "module", "cdn": "./pkg/sinon.js", "jsdelivr": "./pkg/sinon.js", "esm": {"cjs": {"mutableNamespace": false, "cache": true}, "mode": "auto"}}