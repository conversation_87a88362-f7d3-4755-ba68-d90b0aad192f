<h1 align=center>
    <a href="https://sinonjs.org" title="Sinon.JS">
        <img alt="Sinon.JS" src="https://sinonjs.org/assets/images/logo.png">
    </a>
    <br>
    Sinon.JS
</h1>

<p align=center>
    Standalone and test framework agnostic JavaScript test spies, stubs and mocks (pronounced "sigh-non", named after <a href="https://en.wikipedia.org/wiki/Sinon"><PERSON><PERSON>, the warrior</a>).
</p>

<p align=center>
<a href="https://www.npmjs.com/package/sinon"><img src="https://img.shields.io/npm/v/sinon.svg?style=flat" alt="npm version"></a>
<a href="https://saucelabs.com/u/sinonjs"><img src="https://saucelabs.com/buildstatus/sinonjs" alt="Sauce Test Status"/></a>
<a href="https://codecov.io/gh/sinonjs/sinon"><img src="https://codecov.io/gh/sinonjs/sinon/branch/master/graph/badge.svg" alt="Codecov status"></a>
<a href="#backers"><img src="https://opencollective.com/sinon/backers/badge.svg" alt="OpenCollective"></a>
<a href="#sponsors"><img src="https://opencollective.com/sinon/sponsors/badge.svg" alt="OpenCollective"></a>
<a href="https://www.npmjs.com/package/sinon" target="_blank"><img src="https://img.shields.io/npm/dm/sinon.svg" alt="npm downloads per month"></a>
<a href="https://cdnjs.com/libraries/sinon.js" target="_blank"><img src="https://img.shields.io/cdnjs/v/sinon.js.svg" alt="CDNJS version"></a>
<a href="CODE_OF_CONDUCT.md"><img src="https://img.shields.io/badge/Contributor%20Covenant-v2.0%20adopted-ff69b4.svg" alt="Contributor Covenant" /></a>
</p>

<!-- Shows an outdated badge. Will not be fixed until https://github.com/sinonjs/fake-timers/pull/395 ships.
<p align=center>
<a href="https://app.saucelabs.com/u/sinonjs">
  <img src="https://app.saucelabs.com/browser-matrix/sinonjs.svg" alt="Sauce Test Status"/>
</a>
</p>
-->

## Compatibility

For details on compatibility and browser support, please see [`COMPATIBILITY.md`](COMPATIBILITY.md)

## Installation

via [npm](https://github.com/npm/npm)

    $ npm install sinon

or via sinon's browser builds available for download on the [homepage](https://sinonjs.org/releases/). There are also [npm based CDNs](https://sinonjs.org/releases#npm-cdns) one can use.

## Usage

See the [sinon project homepage](https://sinonjs.org/) for documentation on usage.

If you have questions that are not covered by the documentation, you can [check out the `sinon` tag on Stack Overflow](https://stackoverflow.com/questions/tagged/sinon) or drop by <a href="irc://irc.freenode.net:6667/sinon.js">#sinon.js on irc.freenode.net:6667</a>.

You can also search through the [Sinon.JS mailing list archives](http://groups.google.com/group/sinonjs).

## Goals

- No global pollution
- Easy to use
- Require minimal “integration”
- Easy to embed seamlessly with any testing framework
- Easily fake any interface
- Ship with ready-to-use fakes for XMLHttpRequest, timers and more

## Contribute?

See [CONTRIBUTING.md](CONTRIBUTING.md) for details on how you can contribute to Sinon.JS

## Backers

Thank you to all our backers! 🙏 [[Become a backer](https://opencollective.com/sinon#backer)]

<a href="https://opencollective.com/sinon#backers" target="_blank"><img src="https://opencollective.com/sinon/backers.svg?width=890"></a>

## Sponsors

Become a sponsor and get your logo on our README on GitHub with a link to your site. [[Become a sponsor](https://opencollective.com/sinon#sponsor)]

<a href="https://opencollective.com/sinon/sponsor/0/website" target="_blank"><img src="https://opencollective.com/sinon/sponsor/0/avatar.svg"></a>
<a href="https://opencollective.com/sinon/sponsor/1/website" target="_blank"><img src="https://opencollective.com/sinon/sponsor/1/avatar.svg"></a>
<a href="https://opencollective.com/sinon/sponsor/2/website" target="_blank"><img src="https://opencollective.com/sinon/sponsor/2/avatar.svg"></a>
<a href="https://opencollective.com/sinon/sponsor/3/website" target="_blank"><img src="https://opencollective.com/sinon/sponsor/3/avatar.svg"></a>
<a href="https://opencollective.com/sinon/sponsor/4/website" target="_blank"><img src="https://opencollective.com/sinon/sponsor/4/avatar.svg"></a>
<a href="https://opencollective.com/sinon/sponsor/5/website" target="_blank"><img src="https://opencollective.com/sinon/sponsor/5/avatar.svg"></a>
<a href="https://opencollective.com/sinon/sponsor/6/website" target="_blank"><img src="https://opencollective.com/sinon/sponsor/6/avatar.svg"></a>
<a href="https://opencollective.com/sinon/sponsor/7/website" target="_blank"><img src="https://opencollective.com/sinon/sponsor/7/avatar.svg"></a>
<a href="https://opencollective.com/sinon/sponsor/8/website" target="_blank"><img src="https://opencollective.com/sinon/sponsor/8/avatar.svg"></a>
<a href="https://opencollective.com/sinon/sponsor/9/website" target="_blank"><img src="https://opencollective.com/sinon/sponsor/9/avatar.svg"></a>
<a href="https://opencollective.com/sinon/sponsor/10/website" target="_blank"><img src="https://opencollective.com/sinon/sponsor/10/avatar.svg"></a>

## Licence

Sinon.js was released under [BSD-3](LICENSE)
