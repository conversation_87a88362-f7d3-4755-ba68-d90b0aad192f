{"name": "typed-function", "version": "4.2.1", "description": "Type checking for JavaScript functions", "author": "<PERSON><PERSON> <<EMAIL>> (https://github.com/josdejong)", "contributors": ["<PERSON> (https://github.com/gwhitney)", "<PERSON> (https://github.com/luke-gumbley)"], "homepage": "https://github.com/josde<PERSON>/typed-function", "repository": {"type": "git", "url": "https://github.com/josde<PERSON>/typed-function.git"}, "keywords": ["typed", "function", "arguments", "compose", "types"], "type": "module", "main": "lib/umd/typed-function.js", "module": "lib/esm/typed-function.mjs", "browser": "lib/umd/typed-function.js", "scripts": {"test": "mocha test --recursive", "test:lib": "mocha test test-lib --recursive", "build": "npm-run-all build:**", "build:clean": "del-cli lib", "build:esm": "babel src --out-dir lib/esm --out-file-extension .mjs --source-maps --config-file ./babel.config.json", "build:umd": "rollup lib/esm/typed-function.mjs --format umd --name 'typed' --sourcemap --output.file lib/umd/typed-function.js && cpy tools/cjs/package.json lib/umd --flat", "build-and-test": "npm run lint && npm run build && npm run test:lib", "lint": "eslint --cache src/**/*.mjs test/**/*.mjs test-lib/**/*.mjs", "format": "npm run lint -- --fix", "coverage": "c8 --reporter=lcov --reporter=text-summary mocha test --recursive && echo \"\nCoverage report is available at ./coverage/lcov-report/index.html\"", "prepublishOnly": "npm run build-and-test"}, "engines": {"node": ">= 18"}, "devDependencies": {"@babel/cli": "7.24.6", "@babel/preset-env": "7.24.6", "benchmark": "2.1.4", "c8": "9.1.0", "cpy-cli": "5.0.0", "del-cli": "5.1.0", "eslint": "8.56.0", "eslint-config-standard": "17.1.0", "eslint-plugin-import": "2.29.1", "eslint-plugin-n": "16.6.2", "eslint-plugin-promise": "6.2.0", "mocha": "10.4.0", "npm-run-all": "4.1.5", "pad-right": "0.2.2", "rollup": "4.18.0"}, "files": ["README.md", "LICENSE.md", "lib"], "license": "MIT"}