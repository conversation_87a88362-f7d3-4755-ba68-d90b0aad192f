{"version": 3, "file": "typed-function.mjs", "names": ["ok", "notOk", "undef", "undefined", "NOT_TYPED_FUNCTION", "create", "isPlainObject", "x", "constructor", "Object", "_types", "name", "test", "Array", "isArray", "Date", "RegExp", "anyType", "isAny", "typeMap", "typeList", "nConversions", "typed", "createCount", "findType", "typeName", "type", "get", "message", "toLowerCase", "otherName", "TypeError", "addTypes", "types", "beforeSpec", "arguments", "length", "beforeIndex", "index", "newTypes", "i", "has", "push", "set", "conversionsTo", "affectedTypes", "slice", "concat", "clear", "Map", "clearConversions", "findTypeNames", "value", "matches", "filter", "isTypedFunction", "entity", "findSignature", "fn", "signature", "options", "exact", "stringSignature", "join", "params", "parseSignature", "canonicalSignature", "stringifyParams", "signatures", "match", "_typedFunctionData", "signatureMap", "nParams", "remainingSignatures", "want", "filteredSignatures", "possibility", "have", "getParamAtIndex", "restParam", "hasAny", "haveTypes", "paramTypeSet", "some", "wtype", "candidate", "find", "implementation", "convert", "conversions", "Error", "fromType", "from", "separator", "map", "p", "parseParam", "param", "indexOf", "typeDefs", "split", "s", "trim", "paramName", "exactTypes", "typeIndex", "conversion", "conversionIndex", "hasConversion", "expandParam", "typeNames", "t", "matchingConversions", "availableConversions", "newName", "convertibleTypes", "typeSet", "Set", "for<PERSON>ach", "add", "rawSignature", "rawParams", "parsed<PERSON><PERSON><PERSON>", "SyntaxError", "hasRestParam", "last", "compileTest", "test0", "test1", "or", "tests", "compileTests", "initial", "varIndex", "lastTest", "testRestParam", "args", "testArgs", "getTypeSetAtIndex", "isExactType", "mergeExpectedParams", "paramSet", "createError", "err", "expected", "_name", "matchingSignatures", "nextMatchingDefs", "actualTypes", "data", "category", "actual", "lengths", "Infinity", "Math", "min", "apply", "max<PERSON><PERSON><PERSON>", "max", "<PERSON><PERSON><PERSON><PERSON>", "argTypes", "getLowestTypeIndex", "getLowestConversionIndex", "compareParams", "param1", "param2", "typeDiff", "convDiff", "compareSignatures", "signature1", "signature2", "pars1", "pars2", "last1", "last2", "hasRest1", "hasRest2", "any1", "conv1", "par", "any2", "conv2", "lengthCriterion", "comparisons", "tc", "thisComparison", "c", "sort", "t1", "t2", "knownTypes", "newMatch", "compileArgsPreprocessing", "fnConvert", "compiledConversions", "compileArgConversion", "convertArgs", "fnPreprocess", "offset", "preprocessRestParams", "conversion0", "conversion1", "convertArg", "arg", "splitParams", "_splitParams", "paramsSoFar", "resultingParams", "flatMap", "nextParam", "conflicting", "params1", "params2", "ii", "typeSet1", "typeSet2", "overlap", "len1", "len2", "restParam1", "restParam2", "clearResolutions", "functionList", "isReferToSelf", "referToSelf", "callback", "isReferTo", "makeReferTo", "referTo", "references", "collectResolutions", "resolvedReferences", "reference", "resolution", "resolveReferences", "self", "resolvedFunctions", "isResolved", "fill", "leftUnresolved", "nothingResolved", "validateDeprecatedThis", "signaturesMap", "deprecatedThisRegex", "keys", "toString", "createTypedFunction", "rawSignaturesMap", "warnAgainstDeprecatedThis", "parsedParams", "originalFunctions", "preliminarySignatures", "prototype", "hasOwnProperty", "call", "pp", "functionIndex", "conversionParams", "sp", "spName", "every", "theTypedFn", "internalSignatureMap", "ok0", "ok1", "ok2", "ok3", "ok4", "ok5", "allOk", "test00", "test10", "test20", "test30", "test40", "test50", "test01", "test11", "test21", "test31", "test41", "test51", "fn0", "fn1", "fn2", "fn3", "fn4", "fn5", "len0", "len3", "len4", "len5", "iStart", "iEnd", "fns", "generic", "onMismatch", "arg0", "arg1", "defineProperty", "_onMismatch", "arr", "start", "end", "findInArray", "objectOrFn", "checkName", "nameSoFar", "getObjectName", "obj", "key", "mergeSignatures", "dest", "source", "sourceFunction", "destFunction", "saveTyped", "maybe<PERSON><PERSON>", "named", "allSignatures", "item", "theseSignatures", "thisName", "argument", "throwMismatchError", "_findType", "addType", "beforeObjectTest", "before", "_validateConversion", "to", "addConversion", "override", "existing", "other", "removeConversion", "addConversions", "existingConversion", "splice", "resolve", "tf", "argList", "sigs"], "sources": ["../../src/typed-function.mjs"], "sourcesContent": ["function ok () {\n  return true\n}\n\nfunction notOk () {\n  return false\n}\n\nfunction undef () {\n  return undefined\n}\n\nconst NOT_TYPED_FUNCTION = 'Argument is not a typed-function.'\n\n/**\n * @typedef {{\n *   params: Param[],\n *   fn: function,\n *   test: function,\n *   implementation: function\n * }} Signature\n *\n * @typedef {{\n *   types: Type[],\n *   hasAny: boolean,\n *   hasConversion: boolean,\n *   restParam: boolean\n * }} Param\n *\n * @typedef {{\n *   name: string,\n *   typeIndex: number,\n *   test: function,\n *   isAny: boolean,\n *   conversion?: ConversionDef,\n *   conversionIndex: number,\n * }} Type\n *\n * @typedef {{\n *   from: string,\n *   to: string,\n *   convert: function (*) : *\n * }} ConversionDef\n *\n * @typedef {{\n *   name: string,\n *   test: function(*) : boolean,\n *   isAny?: boolean\n * }} TypeDef\n */\n\n/**\n * @returns {() => function}\n */\nfunction create () {\n  // data type tests\n\n  /**\n   * Returns true if the argument is a non-null \"plain\" object\n   */\n  function isPlainObject (x) {\n    return typeof x === 'object' && x !== null && x.constructor === Object\n  }\n\n  const _types = [\n    { name: 'number', test: function (x) { return typeof x === 'number' } },\n    { name: 'string', test: function (x) { return typeof x === 'string' } },\n    { name: 'boolean', test: function (x) { return typeof x === 'boolean' } },\n    { name: 'Function', test: function (x) { return typeof x === 'function' } },\n    { name: 'Array', test: Array.isArray },\n    { name: 'Date', test: function (x) { return x instanceof Date } },\n    { name: 'RegExp', test: function (x) { return x instanceof RegExp } },\n    { name: 'Object', test: isPlainObject },\n    { name: 'null', test: function (x) { return x === null } },\n    { name: 'undefined', test: function (x) { return x === undefined } }\n  ]\n\n  const anyType = {\n    name: 'any',\n    test: ok,\n    isAny: true\n  }\n\n  // Data structures to track the types. As these are local variables in\n  // create(), each typed universe will get its own copy, but the variables\n  // will only be accessible through the (closures of the) functions supplied\n  // as properties of the typed object, not directly.\n  // These will be initialized in clear() below\n  let typeMap // primary store of all types\n  let typeList // Array of just type names, for the sake of ordering\n\n  // And similar data structures for the type conversions:\n  let nConversions = 0\n  // the actual conversions are stored on a property of the destination types\n\n  // This is a temporary object, will be replaced with a function at the end\n  let typed = { createCount: 0 }\n\n  /**\n   * Takes a type name and returns the corresponding official type object\n   * for that type.\n   *\n   * @param {string} typeName\n   * @returns {TypeDef} type\n   */\n  function findType (typeName) {\n    const type = typeMap.get(typeName)\n    if (type) {\n      return type\n    }\n    // Remainder is error handling\n    let message = 'Unknown type \"' + typeName + '\"'\n    const name = typeName.toLowerCase()\n    let otherName\n    for (otherName of typeList) {\n      if (otherName.toLowerCase() === name) {\n        message += '. Did you mean \"' + otherName + '\" ?'\n        break\n      }\n    }\n    throw new TypeError(message)\n  }\n\n  /**\n   * Adds an array `types` of type definitions to this typed instance.\n   * Each type definition should be an object with properties:\n   * 'name' - a string giving the name of the type; 'test' - function\n   * returning a boolean that tests membership in the type; and optionally\n   * 'isAny' - true only for the 'any' type.\n   *\n   * The second optional argument, `before`, gives the name of a type that\n   * these types should be added before. The new types are added in the\n   * order specified.\n   * @param {TypeDef[]} types\n   * @param {string | boolean} [beforeSpec='any'] before\n   */\n  function addTypes (types, beforeSpec = 'any') {\n    const beforeIndex = beforeSpec\n      ? findType(beforeSpec).index\n      : typeList.length\n    const newTypes = []\n    for (let i = 0; i < types.length; ++i) {\n      if (!types[i] || typeof types[i].name !== 'string' ||\n        typeof types[i].test !== 'function') {\n        throw new TypeError('Object with properties {name: string, test: function} expected')\n      }\n      const typeName = types[i].name\n      if (typeMap.has(typeName)) {\n        throw new TypeError('Duplicate type name \"' + typeName + '\"')\n      }\n      newTypes.push(typeName)\n      typeMap.set(typeName, {\n        name: typeName,\n        test: types[i].test,\n        isAny: types[i].isAny,\n        index: beforeIndex + i,\n        conversionsTo: [] // Newly added type can't have any conversions to it\n      })\n    }\n    // update the typeList\n    const affectedTypes = typeList.slice(beforeIndex)\n    typeList =\n      typeList.slice(0, beforeIndex).concat(newTypes).concat(affectedTypes)\n    // Fix the indices\n    for (let i = beforeIndex + newTypes.length; i < typeList.length; ++i) {\n      typeMap.get(typeList[i]).index = i\n    }\n  }\n\n  /**\n   * Removes all types and conversions from this typed instance.\n   * May cause previously constructed typed-functions to throw\n   * strange errors when they are called with types that do not\n   * match any of their signatures.\n   */\n  function clear () {\n    typeMap = new Map()\n    typeList = []\n    nConversions = 0\n    addTypes([anyType], false)\n  }\n\n  // initialize the types to the default list\n  clear()\n  addTypes(_types)\n\n  /**\n   * Removes all conversions, leaving the types alone.\n   */\n  function clearConversions () {\n    let typeName\n    for (typeName of typeList) {\n      typeMap.get(typeName).conversionsTo = []\n    }\n    nConversions = 0\n  }\n\n  /**\n   * Find the type names that match a value.\n   * @param {*} value\n   * @return {string[]} Array of names of types for which\n   *                  the type test matches the value.\n   */\n  function findTypeNames (value) {\n    const matches = typeList.filter(name => {\n      const type = typeMap.get(name)\n      return !type.isAny && type.test(value)\n    })\n    if (matches.length) {\n      return matches\n    }\n    return ['any']\n  }\n\n  /**\n   * Check if an entity is a typed function created by any instance\n   * @param {any} entity\n   * @returns {boolean}\n   */\n  function isTypedFunction (entity) {\n    return entity && typeof entity === 'function' &&\n      '_typedFunctionData' in entity\n  }\n\n  /**\n   * Find a specific signature from a (composed) typed function, for example:\n   *\n   *   typed.findSignature(fn, ['number', 'string'])\n   *   typed.findSignature(fn, 'number, string')\n   *   typed.findSignature(fn, 'number,string', {exact: true})\n   *\n   * This function findSignature will by default return the best match to\n   * the given signature, possibly employing type conversions.\n   *\n   * The (optional) third argument is a plain object giving options\n   * controlling the signature search. Currently the only implemented\n   * option is `exact`: if specified as true (default is false), only\n   * exact matches will be returned (i.e. signatures for which `fn` was\n   * directly defined). Note that a (possibly different) type matching\n   * `any`, or one or more instances of TYPE matching `...TYPE` are\n   * considered exact matches in this regard, as no conversions are used.\n   *\n   * This function returns a \"signature\" object, as does `typed.resolve()`,\n   * which is a plain object with four keys: `params` (the array of parameters\n   * for this signature), `fn` (the originally supplied function for this\n   * signature), `test` (a generated function that determines if an argument\n   * list matches this signature, and `implementation` (the function to call\n   * on a matching argument list, that performs conversions if necessary and\n   * then calls the originally supplied function).\n   *\n   * @param {Function} fn                   A typed-function\n   * @param {string | string[]} signature\n   *     Signature to be found, can be an array or a comma separated string.\n   * @param {object} options  Controls the signature search as documented\n   * @return {{ params: Param[], fn: function, test: function, implementation: function }}\n   *     Returns the matching signature, or throws an error when no signature\n   *     is found.\n   */\n  function findSignature (fn, signature, options) {\n    if (!isTypedFunction(fn)) {\n      throw new TypeError(NOT_TYPED_FUNCTION)\n    }\n\n    // Canonicalize input\n    const exact = options && options.exact\n    const stringSignature = Array.isArray(signature)\n      ? signature.join(',')\n      : signature\n    const params = parseSignature(stringSignature)\n    const canonicalSignature = stringifyParams(params)\n\n    // First hope we get lucky and exactly match a signature\n    if (!exact || canonicalSignature in fn.signatures) {\n      // OK, we can check the internal signatures\n      const match =\n        fn._typedFunctionData.signatureMap.get(canonicalSignature)\n      if (match) {\n        return match\n      }\n    }\n\n    // Oh well, we did not; so we have to go back and check the parameters\n    // one by one, in order to catch things like `any` and rest params.\n    // Note here we can assume there is at least one parameter, because\n    // the empty signature would have matched successfully above.\n    const nParams = params.length\n    let remainingSignatures\n    if (exact) {\n      remainingSignatures = []\n      let name\n      for (name in fn.signatures) {\n        remainingSignatures.push(fn._typedFunctionData.signatureMap.get(name))\n      }\n    } else {\n      remainingSignatures = fn._typedFunctionData.signatures\n    }\n    for (let i = 0; i < nParams; ++i) {\n      const want = params[i]\n      const filteredSignatures = []\n      let possibility\n      for (possibility of remainingSignatures) {\n        const have = getParamAtIndex(possibility.params, i)\n        if (!have || (want.restParam && !have.restParam)) {\n          continue\n        }\n        if (!have.hasAny) {\n          // have to check all of the wanted types are available\n          const haveTypes = paramTypeSet(have)\n          if (want.types.some(wtype => !haveTypes.has(wtype.name))) {\n            continue\n          }\n        }\n        // OK, this looks good\n        filteredSignatures.push(possibility)\n      }\n      remainingSignatures = filteredSignatures\n      if (remainingSignatures.length === 0) break\n    }\n    // Return the first remaining signature that was totally matched:\n    let candidate\n    for (candidate of remainingSignatures) {\n      if (candidate.params.length <= nParams) {\n        return candidate\n      }\n    }\n\n    throw new TypeError('Signature not found (signature: ' + (fn.name || 'unnamed') + '(' + stringifyParams(params, ', ') + '))')\n  }\n\n  /**\n   * Find the proper function to call for a specific signature from\n   * a (composed) typed function, for example:\n   *\n   *   typed.find(fn, ['number', 'string'])\n   *   typed.find(fn, 'number, string')\n   *   typed.find(fn, 'number,string', {exact: true})\n   *\n   * This function find will by default return the best match to\n   * the given signature, possibly employing type conversions (and returning\n   * a function that will perform those conversions as needed). The\n   * (optional) third argument is a plain object giving options contolling\n   * the signature search. Currently only the option `exact` is implemented,\n   * which defaults to \"false\". If `exact` is specified as true, then only\n   * exact matches will be returned (i.e. signatures for which `fn` was\n   * directly defined). Uses of `any` and `...TYPE` are considered exact if\n   * no conversions are necessary to apply the corresponding function.\n   *\n   * @param {Function} fn                   A typed-function\n   * @param {string | string[]} signature\n   *     Signature to be found, can be an array or a comma separated string.\n   * @param {object} options  Controls the signature match as documented\n   * @return {function}\n   *     Returns the function to call for the given signature, or throws an\n   *     error if no match is found.\n   */\n  function find (fn, signature, options) {\n    return findSignature(fn, signature, options).implementation\n  }\n\n  /**\n   * Convert a given value to another data type, specified by type name.\n   *\n   * @param {*} value\n   * @param {string} typeName\n   */\n  function convert (value, typeName) {\n    // check conversion is needed\n    const type = findType(typeName)\n    if (type.test(value)) {\n      return value\n    }\n    const conversions = type.conversionsTo\n    if (conversions.length === 0) {\n      throw new Error(\n        'There are no conversions to ' + typeName + ' defined.')\n    }\n    for (let i = 0; i < conversions.length; i++) {\n      const fromType = findType(conversions[i].from)\n      if (fromType.test(value)) {\n        return conversions[i].convert(value)\n      }\n    }\n\n    throw new Error('Cannot convert ' + value + ' to ' + typeName)\n  }\n\n  /**\n   * Stringify parameters in a normalized way\n   * @param {Param[]} params\n   * @param {string} [','] separator\n   * @return {string}\n   */\n  function stringifyParams (params, separator = ',') {\n    return params.map(p => p.name).join(separator)\n  }\n\n  /**\n   * Parse a parameter, like \"...number | boolean\"\n   * @param {string} param\n   * @return {Param} param\n   */\n  function parseParam (param) {\n    const restParam = param.indexOf('...') === 0\n    const types = (!restParam)\n      ? param\n      : (param.length > 3)\n          ? param.slice(3)\n          : 'any'\n\n    const typeDefs = types.split('|').map(s => findType(s.trim()))\n\n    let hasAny = false\n    let paramName = restParam ? '...' : ''\n\n    const exactTypes = typeDefs.map(function (type) {\n      hasAny = type.isAny || hasAny\n      paramName += type.name + '|'\n\n      return {\n        name: type.name,\n        typeIndex: type.index,\n        test: type.test,\n        isAny: type.isAny,\n        conversion: null,\n        conversionIndex: -1\n      }\n    })\n\n    return {\n      types: exactTypes,\n      name: paramName.slice(0, -1), // remove trailing '|' from above\n      hasAny,\n      hasConversion: false,\n      restParam\n    }\n  }\n\n  /**\n   * Expands a parsed parameter with the types available from currently\n   * defined conversions.\n   * @param {Param} param\n   * @return {Param} param\n   */\n  function expandParam (param) {\n    const typeNames = param.types.map(t => t.name)\n    const matchingConversions = availableConversions(typeNames)\n    let hasAny = param.hasAny\n    let newName = param.name\n\n    const convertibleTypes = matchingConversions.map(function (conversion) {\n      const type = findType(conversion.from)\n      hasAny = type.isAny || hasAny\n      newName += '|' + conversion.from\n\n      return {\n        name: conversion.from,\n        typeIndex: type.index,\n        test: type.test,\n        isAny: type.isAny,\n        conversion,\n        conversionIndex: conversion.index\n      }\n    })\n\n    return {\n      types: param.types.concat(convertibleTypes),\n      name: newName,\n      hasAny,\n      hasConversion: convertibleTypes.length > 0,\n      restParam: param.restParam\n    }\n  }\n\n  /**\n   * Return the set of type names in a parameter.\n   * Caches the result for efficiency\n   *\n   * @param {Param} param\n   * @return {Set<string>} typenames\n   */\n  function paramTypeSet (param) {\n    if (!param.typeSet) {\n      param.typeSet = new Set()\n      param.types.forEach(type => param.typeSet.add(type.name))\n    }\n    return param.typeSet\n  }\n\n  /**\n   * Parse a signature with comma separated parameters,\n   * like \"number | boolean, ...string\"\n   *\n   * @param {string} signature\n   * @return {Param[]} params\n   */\n  function parseSignature (rawSignature) {\n    const params = []\n    if (typeof rawSignature !== 'string') {\n      throw new TypeError('Signatures must be strings')\n    }\n    const signature = rawSignature.trim()\n    if (signature === '') {\n      return params\n    }\n\n    const rawParams = signature.split(',')\n    for (let i = 0; i < rawParams.length; ++i) {\n      const parsedParam = parseParam(rawParams[i].trim())\n      if (parsedParam.restParam && (i !== rawParams.length - 1)) {\n        throw new SyntaxError(\n          'Unexpected rest parameter \"' + rawParams[i] + '\": ' +\n          'only allowed for the last parameter')\n      }\n      // if invalid, short-circuit (all the types may have been filtered)\n      if (parsedParam.types.length === 0) {\n        return null\n      }\n      params.push(parsedParam)\n    }\n\n    return params\n  }\n\n  /**\n   * Test whether a set of params contains a restParam\n   * @param {Param[]} params\n   * @return {boolean} Returns true when the last parameter is a restParam\n   */\n  function hasRestParam (params) {\n    const param = last(params)\n    return param ? param.restParam : false\n  }\n\n  /**\n   * Create a type test for a single parameter, which can have one or multiple\n   * types.\n   * @param {Param} param\n   * @return {function(x: *) : boolean} Returns a test function\n   */\n  function compileTest (param) {\n    if (!param || param.types.length === 0) {\n      // nothing to do\n      return ok\n    } else if (param.types.length === 1) {\n      return findType(param.types[0].name).test\n    } else if (param.types.length === 2) {\n      const test0 = findType(param.types[0].name).test\n      const test1 = findType(param.types[1].name).test\n      return function or (x) {\n        return test0(x) || test1(x)\n      }\n    } else { // param.types.length > 2\n      const tests = param.types.map(function (type) {\n        return findType(type.name).test\n      })\n      return function or (x) {\n        for (let i = 0; i < tests.length; i++) {\n          if (tests[i](x)) {\n            return true\n          }\n        }\n        return false\n      }\n    }\n  }\n\n  /**\n   * Create a test for all parameters of a signature\n   * @param {Param[]} params\n   * @return {function(args: Array<*>) : boolean}\n   */\n  function compileTests (params) {\n    let tests, test0, test1\n\n    if (hasRestParam(params)) {\n      // variable arguments like '...number'\n      tests = initial(params).map(compileTest)\n      const varIndex = tests.length\n      const lastTest = compileTest(last(params))\n      const testRestParam = function (args) {\n        for (let i = varIndex; i < args.length; i++) {\n          if (!lastTest(args[i])) {\n            return false\n          }\n        }\n        return true\n      }\n\n      return function testArgs (args) {\n        for (let i = 0; i < tests.length; i++) {\n          if (!tests[i](args[i])) {\n            return false\n          }\n        }\n        return testRestParam(args) && (args.length >= varIndex + 1)\n      }\n    } else {\n      // no variable arguments\n      if (params.length === 0) {\n        return function testArgs (args) {\n          return args.length === 0\n        }\n      } else if (params.length === 1) {\n        test0 = compileTest(params[0])\n        return function testArgs (args) {\n          return test0(args[0]) && args.length === 1\n        }\n      } else if (params.length === 2) {\n        test0 = compileTest(params[0])\n        test1 = compileTest(params[1])\n        return function testArgs (args) {\n          return test0(args[0]) && test1(args[1]) && args.length === 2\n        }\n      } else { // arguments.length > 2\n        tests = params.map(compileTest)\n        return function testArgs (args) {\n          for (let i = 0; i < tests.length; i++) {\n            if (!tests[i](args[i])) {\n              return false\n            }\n          }\n          return args.length === tests.length\n        }\n      }\n    }\n  }\n\n  /**\n   * Find the parameter at a specific index of a Params list.\n   * Handles rest parameters.\n   * @param {Param[]} params\n   * @param {number} index\n   * @return {Param | null} Returns the matching parameter when found,\n   *                        null otherwise.\n   */\n  function getParamAtIndex (params, index) {\n    return index < params.length\n      ? params[index]\n      : hasRestParam(params) ? last(params) : null\n  }\n\n  /**\n   * Get all type names of a parameter\n   * @param {Params[]} params\n   * @param {number} index\n   * @return {string[]} Returns an array with type names\n   */\n  function getTypeSetAtIndex (params, index) {\n    const param = getParamAtIndex(params, index)\n    if (!param) {\n      return new Set()\n    }\n    return paramTypeSet(param)\n  }\n\n  /**\n   * Test whether a type is an exact type or conversion\n   * @param {Type} type\n   * @return {boolean} Returns true when\n   */\n  function isExactType (type) {\n    return type.conversion === null || type.conversion === undefined\n  }\n\n  /**\n   * Helper function for creating error messages: create an array with\n   * all available types on a specific argument index.\n   * @param {Signature[]} signatures\n   * @param {number} index\n   * @return {string[]} Returns an array with available types\n   */\n  function mergeExpectedParams (signatures, index) {\n    const typeSet = new Set()\n    signatures.forEach(signature => {\n      const paramSet = getTypeSetAtIndex(signature.params, index)\n      let name\n      for (name of paramSet) {\n        typeSet.add(name)\n      }\n    })\n\n    return typeSet.has('any') ? ['any'] : Array.from(typeSet)\n  }\n\n  /**\n   * Create\n   * @param {string} name             The name of the function\n   * @param {array.<*>} args          The actual arguments passed to the function\n   * @param {Signature[]} signatures  A list with available signatures\n   * @return {TypeError} Returns a type error with additional data\n   *                     attached to it in the property `data`\n   */\n  function createError (name, args, signatures) {\n    let err, expected\n    const _name = name || 'unnamed'\n\n    // test for wrong type at some index\n    let matchingSignatures = signatures\n    let index\n    for (index = 0; index < args.length; index++) {\n      const nextMatchingDefs = []\n      matchingSignatures.forEach(signature => {\n        const param = getParamAtIndex(signature.params, index)\n        const test = compileTest(param)\n        if ((index < signature.params.length ||\n          hasRestParam(signature.params)) &&\n          test(args[index])) {\n          nextMatchingDefs.push(signature)\n        }\n      })\n\n      if (nextMatchingDefs.length === 0) {\n        // no matching signatures anymore, throw error \"wrong type\"\n        expected = mergeExpectedParams(matchingSignatures, index)\n        if (expected.length > 0) {\n          const actualTypes = findTypeNames(args[index])\n\n          err = new TypeError('Unexpected type of argument in function ' + _name +\n            ' (expected: ' + expected.join(' or ') +\n            ', actual: ' + actualTypes.join(' | ') + ', index: ' + index + ')')\n          err.data = {\n            category: 'wrongType',\n            fn: _name,\n            index,\n            actual: actualTypes,\n            expected\n          }\n          return err\n        }\n      } else {\n        matchingSignatures = nextMatchingDefs\n      }\n    }\n\n    // test for too few arguments\n    const lengths = matchingSignatures.map(function (signature) {\n      return hasRestParam(signature.params)\n        ? Infinity\n        : signature.params.length\n    })\n    if (args.length < Math.min.apply(null, lengths)) {\n      expected = mergeExpectedParams(matchingSignatures, index)\n      err = new TypeError('Too few arguments in function ' + _name +\n        ' (expected: ' + expected.join(' or ') +\n        ', index: ' + args.length + ')')\n      err.data = {\n        category: 'tooFewArgs',\n        fn: _name,\n        index: args.length,\n        expected\n      }\n      return err\n    }\n\n    // test for too many arguments\n    const maxLength = Math.max.apply(null, lengths)\n    if (args.length > maxLength) {\n      err = new TypeError('Too many arguments in function ' + _name +\n        ' (expected: ' + maxLength + ', actual: ' + args.length + ')')\n      err.data = {\n        category: 'tooManyArgs',\n        fn: _name,\n        index: args.length,\n        expectedLength: maxLength\n      }\n      return err\n    }\n\n    // Generic error\n    const argTypes = []\n    for (let i = 0; i < args.length; ++i) {\n      argTypes.push(findTypeNames(args[i]).join('|'))\n    }\n    err = new TypeError('Arguments of type \"' + argTypes.join(', ') +\n      '\" do not match any of the defined signatures of function ' + _name + '.')\n    err.data = {\n      category: 'mismatch',\n      actual: argTypes\n    }\n    return err\n  }\n\n  /**\n   * Find the lowest index of all exact types of a parameter (no conversions)\n   * @param {Param} param\n   * @return {number} Returns the index of the lowest type in typed.types\n   */\n  function getLowestTypeIndex (param) {\n    let min = typeList.length + 1\n\n    for (let i = 0; i < param.types.length; i++) {\n      if (isExactType(param.types[i])) {\n        min = Math.min(min, param.types[i].typeIndex)\n      }\n    }\n\n    return min\n  }\n\n  /**\n   * Find the lowest index of the conversion of all types of the parameter\n   * having a conversion\n   * @param {Param} param\n   * @return {number} Returns the lowest index of the conversions of this type\n   */\n  function getLowestConversionIndex (param) {\n    let min = nConversions + 1\n\n    for (let i = 0; i < param.types.length; i++) {\n      if (!isExactType(param.types[i])) {\n        min = Math.min(min, param.types[i].conversionIndex)\n      }\n    }\n\n    return min\n  }\n\n  /**\n   * Compare two params\n   * @param {Param} param1\n   * @param {Param} param2\n   * @return {number} returns -1 when param1 must get a lower\n   *                  index than param2, 1 when the opposite,\n   *                  or zero when both are equal\n   */\n  function compareParams (param1, param2) {\n    // We compare a number of metrics on a param in turn:\n    // 1) 'any' parameters are the least preferred\n    if (param1.hasAny) {\n      if (!param2.hasAny) {\n        return 1\n      }\n    } else if (param2.hasAny) {\n      return -1\n    }\n\n    // 2) Prefer non-rest to rest parameters\n    if (param1.restParam) {\n      if (!param2.restParam) {\n        return 1\n      }\n    } else if (param2.restParam) {\n      return -1\n    }\n\n    // 3) Prefer exact type match to conversions\n    if (param1.hasConversion) {\n      if (!param2.hasConversion) {\n        return 1\n      }\n    } else if (param2.hasConversion) {\n      return -1\n    }\n\n    // 4) Prefer lower type index:\n    const typeDiff = getLowestTypeIndex(param1) - getLowestTypeIndex(param2)\n    if (typeDiff < 0) {\n      return -1\n    }\n    if (typeDiff > 0) {\n      return 1\n    }\n\n    // 5) Prefer lower conversion index\n    const convDiff =\n      getLowestConversionIndex(param1) - getLowestConversionIndex(param2)\n    if (convDiff < 0) {\n      return -1\n    }\n    if (convDiff > 0) {\n      return 1\n    }\n\n    // Don't have a basis for preference\n    return 0\n  }\n\n  /**\n   * Compare two signatures\n   * @param {Signature} signature1\n   * @param {Signature} signature2\n   * @return {number} returns a negative number when param1 must get a lower\n   *                  index than param2, a positive number when the opposite,\n   *                  or zero when both are equal\n   */\n  function compareSignatures (signature1, signature2) {\n    const pars1 = signature1.params\n    const pars2 = signature2.params\n    const last1 = last(pars1)\n    const last2 = last(pars2)\n    const hasRest1 = hasRestParam(pars1)\n    const hasRest2 = hasRestParam(pars2)\n    // We compare a number of metrics on signatures in turn:\n    // 1) An \"any rest param\" is least preferred\n    if (hasRest1 && last1.hasAny) {\n      if (!hasRest2 || !last2.hasAny) {\n        return 1\n      }\n    } else if (hasRest2 && last2.hasAny) {\n      return -1\n    }\n\n    // 2) Minimize the number of 'any' parameters\n    let any1 = 0\n    let conv1 = 0\n    let par\n    for (par of pars1) {\n      if (par.hasAny) ++any1\n      if (par.hasConversion) ++conv1\n    }\n    let any2 = 0\n    let conv2 = 0\n    for (par of pars2) {\n      if (par.hasAny) ++any2\n      if (par.hasConversion) ++conv2\n    }\n    if (any1 !== any2) {\n      return any1 - any2\n    }\n\n    // 3) A conversion rest param is less preferred\n    if (hasRest1 && last1.hasConversion) {\n      if (!hasRest2 || !last2.hasConversion) {\n        return 1\n      }\n    } else if (hasRest2 && last2.hasConversion) {\n      return -1\n    }\n\n    // 4) Minimize the number of conversions\n    if (conv1 !== conv2) {\n      return conv1 - conv2\n    }\n\n    // 5) Prefer no rest param\n    if (hasRest1) {\n      if (!hasRest2) {\n        return 1\n      }\n    } else if (hasRest2) {\n      return -1\n    }\n\n    // 6) Prefer shorter with rest param, longer without\n    const lengthCriterion =\n      (pars1.length - pars2.length) * (hasRest1 ? -1 : 1)\n    if (lengthCriterion !== 0) {\n      return lengthCriterion\n    }\n\n    // Signatures are identical in each of the above metrics.\n    // In particular, they are the same length.\n    // We can therefore compare the parameters one by one.\n    // First we count which signature has more preferred parameters.\n    const comparisons = []\n    let tc = 0\n    for (let i = 0; i < pars1.length; ++i) {\n      const thisComparison = compareParams(pars1[i], pars2[i])\n      comparisons.push(thisComparison)\n      tc += thisComparison\n    }\n    if (tc !== 0) {\n      return tc\n    }\n\n    // They have the same number of preferred parameters, so go by the\n    // earliest parameter in which we have a preference.\n    // In other words, dispatch is driven somewhat more by earlier\n    // parameters than later ones.\n    let c\n    for (c of comparisons) {\n      if (c !== 0) {\n        return c\n      }\n    }\n\n    // It's a tossup:\n    return 0\n  }\n\n  /**\n   * Produce a list of all conversions from distinct types to one of\n   * the given types.\n   *\n   * @param {string[]} typeNames\n   * @return {ConversionDef[]} Returns the conversions that are available\n   *                        resulting in any given type (if any)\n   */\n  function availableConversions (typeNames) {\n    if (typeNames.length === 0) {\n      return []\n    }\n    const types = typeNames.map(findType)\n    if (typeNames.length > 1) {\n      types.sort((t1, t2) => t1.index - t2.index)\n    }\n    let matches = types[0].conversionsTo\n    if (typeNames.length === 1) {\n      return matches\n    }\n\n    matches = matches.concat([]) // shallow copy the matches\n    // Since the types are now in index order, we just want the first\n    // occurrence of any from type:\n    const knownTypes = new Set(typeNames)\n    for (let i = 1; i < types.length; ++i) {\n      let newMatch\n      for (newMatch of types[i].conversionsTo) {\n        if (!knownTypes.has(newMatch.from)) {\n          matches.push(newMatch)\n          knownTypes.add(newMatch.from)\n        }\n      }\n    }\n\n    return matches\n  }\n\n  /**\n   * Preprocess arguments before calling the original function:\n   * - if needed convert the parameters\n   * - in case of rest parameters, move the rest parameters into an Array\n   * @param {Param[]} params\n   * @param {function} fn\n   * @return {function} Returns a wrapped function\n   */\n  function compileArgsPreprocessing (params, fn) {\n    let fnConvert = fn\n\n    // TODO: can we make this wrapper function smarter/simpler?\n\n    if (params.some(p => p.hasConversion)) {\n      const restParam = hasRestParam(params)\n      const compiledConversions = params.map(compileArgConversion)\n\n      fnConvert = function convertArgs () {\n        const args = []\n        const last = restParam ? arguments.length - 1 : arguments.length\n        for (let i = 0; i < last; i++) {\n          args[i] = compiledConversions[i](arguments[i])\n        }\n        if (restParam) {\n          args[last] = arguments[last].map(compiledConversions[last])\n        }\n\n        return fn.apply(this, args)\n      }\n    }\n\n    let fnPreprocess = fnConvert\n    if (hasRestParam(params)) {\n      const offset = params.length - 1\n\n      fnPreprocess = function preprocessRestParams () {\n        return fnConvert.apply(this,\n          slice(arguments, 0, offset).concat([slice(arguments, offset)]))\n      }\n    }\n\n    return fnPreprocess\n  }\n\n  /**\n   * Compile conversion for a parameter to the right type\n   * @param {Param} param\n   * @return {function} Returns the wrapped function that will convert arguments\n   *\n   */\n  function compileArgConversion (param) {\n    let test0, test1, conversion0, conversion1\n    const tests = []\n    const conversions = []\n\n    param.types.forEach(function (type) {\n      if (type.conversion) {\n        tests.push(findType(type.conversion.from).test)\n        conversions.push(type.conversion.convert)\n      }\n    })\n\n    // create optimized conversion functions depending on the number of conversions\n    switch (conversions.length) {\n      case 0:\n        return function convertArg (arg) {\n          return arg\n        }\n\n      case 1:\n        test0 = tests[0]\n        conversion0 = conversions[0]\n        return function convertArg (arg) {\n          if (test0(arg)) {\n            return conversion0(arg)\n          }\n          return arg\n        }\n\n      case 2:\n        test0 = tests[0]\n        test1 = tests[1]\n        conversion0 = conversions[0]\n        conversion1 = conversions[1]\n        return function convertArg (arg) {\n          if (test0(arg)) {\n            return conversion0(arg)\n          }\n          if (test1(arg)) {\n            return conversion1(arg)\n          }\n          return arg\n        }\n\n      default:\n        return function convertArg (arg) {\n          for (let i = 0; i < conversions.length; i++) {\n            if (tests[i](arg)) {\n              return conversions[i](arg)\n            }\n          }\n          return arg\n        }\n    }\n  }\n\n  /**\n   * Split params with union types in to separate params.\n   *\n   * For example:\n   *\n   *     splitParams([['Array', 'Object'], ['string', 'RegExp'])\n   *     // returns:\n   *     // [\n   *     //   ['Array', 'string'],\n   *     //   ['Array', 'RegExp'],\n   *     //   ['Object', 'string'],\n   *     //   ['Object', 'RegExp']\n   *     // ]\n   *\n   * @param {Param[]} params\n   * @return {Param[]}\n   */\n  function splitParams (params) {\n    function _splitParams (params, index, paramsSoFar) {\n      if (index < params.length) {\n        const param = params[index]\n        let resultingParams = []\n\n        if (param.restParam) {\n          // split the types of a rest parameter in two:\n          // one with only exact types, and one with exact types and conversions\n          const exactTypes = param.types.filter(isExactType)\n          if (exactTypes.length < param.types.length) {\n            resultingParams.push({\n              types: exactTypes,\n              name: '...' + exactTypes.map(t => t.name).join('|'),\n              hasAny: exactTypes.some(t => t.isAny),\n              hasConversion: false,\n              restParam: true\n            })\n          }\n          resultingParams.push(param)\n        } else {\n          // split all the types of a regular parameter into one type per param\n          resultingParams = param.types.map(function (type) {\n            return {\n              types: [type],\n              name: type.name,\n              hasAny: type.isAny,\n              hasConversion: type.conversion,\n              restParam: false\n            }\n          })\n        }\n\n        // recurse over the groups with types\n        return flatMap(resultingParams, function (nextParam) {\n          return _splitParams(params, index + 1, paramsSoFar.concat([nextParam]))\n        })\n      } else {\n        // we've reached the end of the parameters.\n        return [paramsSoFar]\n      }\n    }\n\n    return _splitParams(params, 0, [])\n  }\n\n  /**\n   * Test whether two param lists represent conflicting signatures\n   * @param {Param[]} params1\n   * @param {Param[]} params2\n   * @return {boolean} Returns true when the signatures conflict, false otherwise.\n   */\n  function conflicting (params1, params2) {\n    const ii = Math.max(params1.length, params2.length)\n\n    for (let i = 0; i < ii; i++) {\n      const typeSet1 = getTypeSetAtIndex(params1, i)\n      const typeSet2 = getTypeSetAtIndex(params2, i)\n      let overlap = false\n      let name\n      for (name of typeSet2) {\n        if (typeSet1.has(name)) {\n          overlap = true\n          break\n        }\n      }\n      if (!overlap) {\n        return false\n      }\n    }\n\n    const len1 = params1.length\n    const len2 = params2.length\n    const restParam1 = hasRestParam(params1)\n    const restParam2 = hasRestParam(params2)\n\n    return restParam1\n      ? restParam2 ? (len1 === len2) : (len2 >= len1)\n      : restParam2 ? (len1 >= len2) : (len1 === len2)\n  }\n\n  /**\n   * Helper function for `resolveReferences` that returns a copy of\n   * functionList wihe any prior resolutions cleared out, in case we are\n   * recycling signatures from a prior typed function construction.\n   *\n   * @param {Array.<function|typed-reference>} functionList\n   * @return {Array.<function|typed-reference>}\n   */\n  function clearResolutions (functionList) {\n    return functionList.map(fn => {\n      if (isReferToSelf(fn)) {\n        return referToSelf(fn.referToSelf.callback)\n      }\n      if (isReferTo(fn)) {\n        return makeReferTo(fn.referTo.references, fn.referTo.callback)\n      }\n      return fn\n    })\n  }\n\n  /**\n   * Take a list of references, a list of functions functionList, and a\n   * signatureMap indexing signatures into functionList, and return\n   * the list of resolutions, or a false-y value if they don't all\n   * resolve in a valid way (yet).\n   *\n   * @param {string[]} references\n   * @param {Array<function|typed-reference} functionList\n   * @param {Object.<string, integer>} signatureMap\n   * @return {function[] | false} resolutions\n   */\n  function collectResolutions (references, functionList, signatureMap) {\n    const resolvedReferences = []\n    let reference\n    for (reference of references) {\n      let resolution = signatureMap[reference]\n      if (typeof resolution !== 'number') {\n        throw new TypeError(\n          'No definition for referenced signature \"' + reference + '\"')\n      }\n      resolution = functionList[resolution]\n      if (typeof resolution !== 'function') {\n        return false\n      }\n      resolvedReferences.push(resolution)\n    }\n    return resolvedReferences\n  }\n\n  /**\n   * Resolve any references in the functionList for the typed function\n   * itself. The signatureMap tells which index in the functionList a\n   * given signature should be mapped to (for use in resolving typed.referTo)\n   * and self provides the destions of a typed.referToSelf.\n   *\n   * @param {Array<function | typed-reference-object>} functionList\n   * @param {Object.<string, function>} signatureMap\n   * @param {function} self  The typed-function itself\n   * @return {Array<function>} The list of resolved functions\n   */\n  function resolveReferences (functionList, signatureMap, self) {\n    const resolvedFunctions = clearResolutions(functionList)\n    const isResolved = new Array(resolvedFunctions.length).fill(false)\n    let leftUnresolved = true\n    while (leftUnresolved) {\n      leftUnresolved = false\n      let nothingResolved = true\n      for (let i = 0; i < resolvedFunctions.length; ++i) {\n        if (isResolved[i]) continue\n        const fn = resolvedFunctions[i]\n\n        if (isReferToSelf(fn)) {\n          resolvedFunctions[i] = fn.referToSelf.callback(self)\n          // Preserve reference in case signature is reused someday:\n          resolvedFunctions[i].referToSelf = fn.referToSelf\n          isResolved[i] = true\n          nothingResolved = false\n        } else if (isReferTo(fn)) {\n          const resolvedReferences = collectResolutions(\n            fn.referTo.references, resolvedFunctions, signatureMap)\n          if (resolvedReferences) {\n            resolvedFunctions[i] =\n              fn.referTo.callback.apply(this, resolvedReferences)\n            // Preserve reference in case signature is reused someday:\n            resolvedFunctions[i].referTo = fn.referTo\n            isResolved[i] = true\n            nothingResolved = false\n          } else {\n            leftUnresolved = true\n          }\n        }\n      }\n\n      if (nothingResolved && leftUnresolved) {\n        throw new SyntaxError(\n          'Circular reference detected in resolving typed.referTo')\n      }\n    }\n\n    return resolvedFunctions\n  }\n\n  /**\n   * Validate whether any of the function bodies contains a self-reference\n   * usage like `this(...)` or `this.signatures`. This self-referencing is\n   * deprecated since typed-function v3. It has been replaced with\n   * the functions typed.referTo and typed.referToSelf.\n   * @param {Object.<string, function>} signaturesMap\n   */\n  function validateDeprecatedThis (signaturesMap) {\n    // TODO: remove this deprecation warning logic some day (it's introduced in v3)\n\n    // match occurrences like 'this(' and 'this.signatures'\n    const deprecatedThisRegex = /\\bthis(\\(|\\.signatures\\b)/\n\n    Object.keys(signaturesMap).forEach(signature => {\n      const fn = signaturesMap[signature]\n\n      if (deprecatedThisRegex.test(fn.toString())) {\n        throw new SyntaxError('Using `this` to self-reference a function ' +\n          'is deprecated since typed-function@3. ' +\n          'Use typed.referTo and typed.referToSelf instead.')\n      }\n    })\n  }\n\n  /**\n   * Create a typed function\n   * @param {String} name               The name for the typed function\n   * @param {Object.<string, function>} rawSignaturesMap\n   *                                    An object with one or\n   *                                    multiple signatures as key, and the\n   *                                    function corresponding to the\n   *                                    signature as value.\n   * @return {function}  Returns the created typed function.\n   */\n  function createTypedFunction (name, rawSignaturesMap) {\n    typed.createCount++\n\n    if (Object.keys(rawSignaturesMap).length === 0) {\n      throw new SyntaxError('No signatures provided')\n    }\n\n    if (typed.warnAgainstDeprecatedThis) {\n      validateDeprecatedThis(rawSignaturesMap)\n    }\n\n    // Main processing loop for signatures\n    const parsedParams = []\n    const originalFunctions = []\n    const signaturesMap = {}\n    const preliminarySignatures = [] // may have duplicates from conversions\n    let signature\n    for (signature in rawSignaturesMap) {\n      // A) Protect against polluted Object prototype:\n      if (!Object.prototype.hasOwnProperty.call(rawSignaturesMap, signature)) {\n        continue\n      }\n      // B) Parse the signature\n      const params = parseSignature(signature)\n      if (!params) continue\n      // C) Check for conflicts\n      parsedParams.forEach(function (pp) {\n        if (conflicting(pp, params)) {\n          throw new TypeError('Conflicting signatures \"' +\n            stringifyParams(pp) + '\" and \"' +\n            stringifyParams(params) + '\".')\n        }\n      })\n      parsedParams.push(params)\n      // D) Store the provided function and add conversions\n      const functionIndex = originalFunctions.length\n      originalFunctions.push(rawSignaturesMap[signature])\n      const conversionParams = params.map(expandParam)\n      // E) Split the signatures and collect them up\n      let sp\n      for (sp of splitParams(conversionParams)) {\n        const spName = stringifyParams(sp)\n        preliminarySignatures.push(\n          { params: sp, name: spName, fn: functionIndex })\n        if (sp.every(p => !p.hasConversion)) {\n          signaturesMap[spName] = functionIndex\n        }\n      }\n    }\n\n    preliminarySignatures.sort(compareSignatures)\n\n    // Note the forward reference to theTypedFn\n    const resolvedFunctions =\n      resolveReferences(originalFunctions, signaturesMap, theTypedFn)\n\n    // Fill in the proper function for each signature\n    let s\n    for (s in signaturesMap) {\n      if (Object.prototype.hasOwnProperty.call(signaturesMap, s)) {\n        signaturesMap[s] = resolvedFunctions[signaturesMap[s]]\n      }\n    }\n    const signatures = []\n    const internalSignatureMap = new Map() // benchmarks faster than object\n    for (s of preliminarySignatures) {\n      // Note it's only safe to eliminate duplicates like this\n      // _after_ the signature sorting step above; otherwise we might\n      // remove the wrong one.\n      if (!internalSignatureMap.has(s.name)) {\n        s.fn = resolvedFunctions[s.fn]\n        signatures.push(s)\n        internalSignatureMap.set(s.name, s)\n      }\n    }\n\n    // we create a highly optimized checks for the first couple of signatures with max 2 arguments\n    const ok0 = signatures[0] && signatures[0].params.length <= 2 && !hasRestParam(signatures[0].params)\n    const ok1 = signatures[1] && signatures[1].params.length <= 2 && !hasRestParam(signatures[1].params)\n    const ok2 = signatures[2] && signatures[2].params.length <= 2 && !hasRestParam(signatures[2].params)\n    const ok3 = signatures[3] && signatures[3].params.length <= 2 && !hasRestParam(signatures[3].params)\n    const ok4 = signatures[4] && signatures[4].params.length <= 2 && !hasRestParam(signatures[4].params)\n    const ok5 = signatures[5] && signatures[5].params.length <= 2 && !hasRestParam(signatures[5].params)\n    const allOk = ok0 && ok1 && ok2 && ok3 && ok4 && ok5\n\n    // compile the tests\n    for (let i = 0; i < signatures.length; ++i) {\n      signatures[i].test = compileTests(signatures[i].params)\n    }\n\n    const test00 = ok0 ? compileTest(signatures[0].params[0]) : notOk\n    const test10 = ok1 ? compileTest(signatures[1].params[0]) : notOk\n    const test20 = ok2 ? compileTest(signatures[2].params[0]) : notOk\n    const test30 = ok3 ? compileTest(signatures[3].params[0]) : notOk\n    const test40 = ok4 ? compileTest(signatures[4].params[0]) : notOk\n    const test50 = ok5 ? compileTest(signatures[5].params[0]) : notOk\n\n    const test01 = ok0 ? compileTest(signatures[0].params[1]) : notOk\n    const test11 = ok1 ? compileTest(signatures[1].params[1]) : notOk\n    const test21 = ok2 ? compileTest(signatures[2].params[1]) : notOk\n    const test31 = ok3 ? compileTest(signatures[3].params[1]) : notOk\n    const test41 = ok4 ? compileTest(signatures[4].params[1]) : notOk\n    const test51 = ok5 ? compileTest(signatures[5].params[1]) : notOk\n\n    // compile the functions\n    for (let i = 0; i < signatures.length; ++i) {\n      signatures[i].implementation =\n        compileArgsPreprocessing(signatures[i].params, signatures[i].fn)\n    }\n\n    const fn0 = ok0 ? signatures[0].implementation : undef\n    const fn1 = ok1 ? signatures[1].implementation : undef\n    const fn2 = ok2 ? signatures[2].implementation : undef\n    const fn3 = ok3 ? signatures[3].implementation : undef\n    const fn4 = ok4 ? signatures[4].implementation : undef\n    const fn5 = ok5 ? signatures[5].implementation : undef\n\n    const len0 = ok0 ? signatures[0].params.length : -1\n    const len1 = ok1 ? signatures[1].params.length : -1\n    const len2 = ok2 ? signatures[2].params.length : -1\n    const len3 = ok3 ? signatures[3].params.length : -1\n    const len4 = ok4 ? signatures[4].params.length : -1\n    const len5 = ok5 ? signatures[5].params.length : -1\n\n    // simple and generic, but also slow\n    const iStart = allOk ? 6 : 0\n    const iEnd = signatures.length\n    // de-reference ahead for execution speed:\n    const tests = signatures.map(s => s.test)\n    const fns = signatures.map(s => s.implementation)\n    const generic = function generic () {\n      'use strict'\n\n      for (let i = iStart; i < iEnd; i++) {\n        if (tests[i](arguments)) {\n          return fns[i].apply(this, arguments)\n        }\n      }\n\n      return typed.onMismatch(name, arguments, signatures)\n    }\n\n    // create the typed function\n    // fast, specialized version. Falls back to the slower, generic one if needed\n    function theTypedFn (arg0, arg1) {\n      'use strict'\n\n      if (arguments.length === len0 && test00(arg0) && test01(arg1)) { return fn0.apply(this, arguments) }\n      if (arguments.length === len1 && test10(arg0) && test11(arg1)) { return fn1.apply(this, arguments) }\n      if (arguments.length === len2 && test20(arg0) && test21(arg1)) { return fn2.apply(this, arguments) }\n      if (arguments.length === len3 && test30(arg0) && test31(arg1)) { return fn3.apply(this, arguments) }\n      if (arguments.length === len4 && test40(arg0) && test41(arg1)) { return fn4.apply(this, arguments) }\n      if (arguments.length === len5 && test50(arg0) && test51(arg1)) { return fn5.apply(this, arguments) }\n\n      return generic.apply(this, arguments)\n    }\n\n    // attach name the typed function\n    try {\n      Object.defineProperty(theTypedFn, 'name', { value: name })\n    } catch (err) {\n      // old browsers do not support Object.defineProperty and some don't support setting the name property\n      // the function name is not essential for the functioning, it's mostly useful for debugging,\n      // so it's fine to have unnamed functions.\n    }\n\n    // attach signatures to the function.\n    // This property is close to the original collection of signatures\n    // used to create the typed-function, just with unions split:\n    theTypedFn.signatures = signaturesMap\n\n    // Store internal data for functions like resolve, find, etc.\n    // Also serves as the flag that this is a typed-function\n    theTypedFn._typedFunctionData = {\n      signatures,\n      signatureMap: internalSignatureMap\n    }\n\n    return theTypedFn\n  }\n\n  /**\n   * Action to take on mismatch\n   * @param {string} name      Name of function that was attempted to be called\n   * @param {Array} args       Actual arguments to the call\n   * @param {Array} signatures Known signatures of the named typed-function\n   */\n  function _onMismatch (name, args, signatures) {\n    throw createError(name, args, signatures)\n  }\n\n  /**\n   * Return all but the last items of an array or function Arguments\n   * @param {Array | Arguments} arr\n   * @return {Array}\n   */\n  function initial (arr) {\n    return slice(arr, 0, arr.length - 1)\n  }\n\n  /**\n   * return the last item of an array or function Arguments\n   * @param {Array | Arguments} arr\n   * @return {*}\n   */\n  function last (arr) {\n    return arr[arr.length - 1]\n  }\n\n  /**\n   * Slice an array or function Arguments\n   * @param {Array | Arguments | IArguments} arr\n   * @param {number} start\n   * @param {number} [end]\n   * @return {Array}\n   */\n  function slice (arr, start, end) {\n    return Array.prototype.slice.call(arr, start, end)\n  }\n\n  /**\n   * Return the first item from an array for which test(arr[i]) returns true\n   * @param {Array} arr\n   * @param {function} test\n   * @return {* | undefined} Returns the first matching item\n   *                         or undefined when there is no match\n   */\n  function findInArray (arr, test) {\n    for (let i = 0; i < arr.length; i++) {\n      if (test(arr[i])) {\n        return arr[i]\n      }\n    }\n    return undefined\n  }\n\n  /**\n   * Flat map the result invoking a callback for every item in an array.\n   * https://gist.github.com/samgiles/762ee337dff48623e729\n   * @param {Array} arr\n   * @param {function} callback\n   * @return {Array}\n   */\n  function flatMap (arr, callback) {\n    return Array.prototype.concat.apply([], arr.map(callback))\n  }\n\n  /**\n   * Create a reference callback to one or multiple signatures\n   *\n   * Syntax:\n   *\n   *     typed.referTo(signature1, signature2, ..., function callback(fn1, fn2, ...) {\n   *       // ...\n   *     })\n   *\n   * @returns {{referTo: {references: string[], callback}}}\n   */\n  function referTo () {\n    const references =\n      initial(arguments).map(s => stringifyParams(parseSignature(s)))\n    const callback = last(arguments)\n\n    if (typeof callback !== 'function') {\n      throw new TypeError('Callback function expected as last argument')\n    }\n\n    return makeReferTo(references, callback)\n  }\n\n  function makeReferTo (references, callback) {\n    return { referTo: { references, callback } }\n  }\n\n  /**\n   * Create a reference callback to the typed-function itself\n   *\n   * @param {(self: function) => function} callback\n   * @returns {{referToSelf: { callback: function }}}\n   */\n  function referToSelf (callback) {\n    if (typeof callback !== 'function') {\n      throw new TypeError('Callback function expected as first argument')\n    }\n\n    return { referToSelf: { callback } }\n  }\n\n  /**\n   * Test whether something is a referTo object, holding a list with reference\n   * signatures and a callback.\n   *\n   * @param {Object | function} objectOrFn\n   * @returns {boolean}\n   */\n  function isReferTo (objectOrFn) {\n    return objectOrFn &&\n      typeof objectOrFn.referTo === 'object' &&\n      Array.isArray(objectOrFn.referTo.references) &&\n      typeof objectOrFn.referTo.callback === 'function'\n  }\n\n  /**\n   * Test whether something is a referToSelf object, holding a callback where\n   * to pass `self`.\n   *\n   * @param {Object | function} objectOrFn\n   * @returns {boolean}\n   */\n  function isReferToSelf (objectOrFn) {\n    return objectOrFn &&\n      typeof objectOrFn.referToSelf === 'object' &&\n      typeof objectOrFn.referToSelf.callback === 'function'\n  }\n\n  /**\n   * Check if name is (A) new, (B) a match, or (C) a mismatch; and throw\n   * an error in case (C).\n   *\n   * @param { string | undefined } nameSoFar\n   * @param { string | undefined } newName\n   * @returns { string } updated name\n   */\n  function checkName (nameSoFar, newName) {\n    if (!nameSoFar) {\n      return newName\n    }\n    if (newName && newName !== nameSoFar) {\n      const err = new Error('Function names do not match (expected: ' +\n        nameSoFar + ', actual: ' + newName + ')')\n      err.data = { actual: newName, expected: nameSoFar }\n      throw err\n    }\n    return nameSoFar\n  }\n\n  /**\n   * Retrieve the implied name from an object with signature keys\n   * and function values, checking whether all value names match\n   *\n   * @param { {string: function} } obj\n   */\n  function getObjectName (obj) {\n    let name\n    for (const key in obj) {\n      // Only pay attention to own properties, and only if their values\n      // are typed functions or functions with a signature property\n      if (Object.prototype.hasOwnProperty.call(obj, key) &&\n        (isTypedFunction(obj[key]) ||\n          typeof obj[key].signature === 'string')) {\n        name = checkName(name, obj[key].name)\n      }\n    }\n    return name\n  }\n\n  /**\n   * Copy all of the signatures from the second argument into the first,\n   * which is modified by side effect, checking for conflicts\n   *\n   * @param {Object.<string, function|typed-reference>} dest\n   * @param {Object.<string, function|typed-reference>} source\n   */\n  function mergeSignatures (dest, source) {\n    let key\n    for (key in source) {\n      if (Object.prototype.hasOwnProperty.call(source, key)) {\n        if (key in dest) {\n          if (source[key] !== dest[key]) {\n            const err = new Error('Signature \"' + key + '\" is defined twice')\n            err.data = {\n              signature: key,\n              sourceFunction: source[key],\n              destFunction: dest[key]\n            }\n            throw err\n          }\n          // else: both signatures point to the same function, that's fine\n        }\n        dest[key] = source[key]\n      }\n    }\n  }\n\n  const saveTyped = typed\n\n  /**\n   * Originally the main function was a typed function itself, but then\n   * it might not be able to generate error messages if the client\n   * replaced the type system with different names.\n   *\n   * Main entry: typed([name], functions/objects with signatures...)\n   *\n   * Assembles and returns a new typed-function from the given items\n   * that provide signatures and implementations, each of which may be\n   * * a plain object mapping (string) signatures to implementing functions,\n   * * a previously constructed typed function, or\n   * * any other single function with a string-valued property `signature`.\n\n   * The name of the resulting typed-function will be given by the\n   * string-valued name argument if present, or if not, by the name\n   * of any of the arguments that have one, as long as any that do are\n   * consistent with each other. If no name is specified, the name will be\n   * an empty string.\n   *\n   * @param {string} maybeName [optional]\n   * @param {(function|object)[]} signature providers\n   * @returns {typed-function}\n   */\n  typed = function (maybeName) {\n    const named = typeof maybeName === 'string'\n    const start = named ? 1 : 0\n    let name = named ? maybeName : ''\n    const allSignatures = {}\n    for (let i = start; i < arguments.length; ++i) {\n      const item = arguments[i]\n      let theseSignatures = {}\n      let thisName\n      if (typeof item === 'function') {\n        thisName = item.name\n        if (typeof item.signature === 'string') {\n          // Case 1: Ordinary function with a string 'signature' property\n          theseSignatures[item.signature] = item\n        } else if (isTypedFunction(item)) {\n          // Case 2: Existing typed function\n          theseSignatures = item.signatures\n        }\n      } else if (isPlainObject(item)) {\n        // Case 3: Plain object, assume keys = signatures, values = functions\n        theseSignatures = item\n        if (!named) {\n          thisName = getObjectName(item)\n        }\n      }\n\n      if (Object.keys(theseSignatures).length === 0) {\n        const err = new TypeError(\n          'Argument to \\'typed\\' at index ' + i + ' is not a (typed) function, ' +\n          'nor an object with signatures as keys and functions as values.')\n        err.data = { index: i, argument: item }\n        throw err\n      }\n\n      if (!named) {\n        name = checkName(name, thisName)\n      }\n      mergeSignatures(allSignatures, theseSignatures)\n    }\n\n    return createTypedFunction(name || '', allSignatures)\n  }\n\n  typed.create = create\n  typed.createCount = saveTyped.createCount\n  typed.onMismatch = _onMismatch\n  typed.throwMismatchError = _onMismatch\n  typed.createError = createError\n  typed.clear = clear\n  typed.clearConversions = clearConversions\n  typed.addTypes = addTypes\n  typed._findType = findType // For unit testing only\n  typed.referTo = referTo\n  typed.referToSelf = referToSelf\n  typed.convert = convert\n  typed.findSignature = findSignature\n  typed.find = find\n  typed.isTypedFunction = isTypedFunction\n  typed.warnAgainstDeprecatedThis = true\n\n  /**\n   * add a type (convenience wrapper for typed.addTypes)\n   * @param {{name: string, test: function}} type\n   * @param {boolean} [beforeObjectTest=true]\n   *                          If true, the new test will be inserted before\n   *                          the test with name 'Object' (if any), since\n   *                          tests for Object match Array and classes too.\n   */\n  typed.addType = function (type, beforeObjectTest) {\n    let before = 'any'\n    if (beforeObjectTest !== false && typeMap.has('Object')) {\n      before = 'Object'\n    }\n    typed.addTypes([type], before)\n  }\n\n  /**\n   * Verify that the ConversionDef conversion has a valid format.\n   *\n   * @param {conversionDef} conversion\n   * @return {void}\n   * @throws {TypeError|SyntaxError}\n   */\n  function _validateConversion (conversion) {\n    if (!conversion ||\n      typeof conversion.from !== 'string' ||\n      typeof conversion.to !== 'string' ||\n      typeof conversion.convert !== 'function') {\n      throw new TypeError('Object with properties {from: string, to: string, convert: function} expected')\n    }\n    if (conversion.to === conversion.from) {\n      throw new SyntaxError(\n        'Illegal to define conversion from \"' + conversion.from +\n        '\" to itself.')\n    }\n  }\n\n  /**\n   * Add a conversion\n   *\n   * @param {ConversionDef} conversion\n   * @param {{override: boolean}} [options]\n   * @returns {void}\n   * @throws {TypeError}\n   */\n  typed.addConversion = function (conversion, options = { override: false }) {\n    _validateConversion(conversion)\n\n    const to = findType(conversion.to)\n    const existing = to.conversionsTo.find((other) => other.from === conversion.from)\n\n    if (existing) {\n      if (options && options.override) {\n        typed.removeConversion({ from: existing.from, to: conversion.to, convert: existing.convert })\n      } else {\n        throw new Error(\n          'There is already a conversion from \"' + conversion.from + '\" to \"' +\n          to.name + '\"')\n      }\n    }\n\n    to.conversionsTo.push({\n      from: conversion.from,\n      convert: conversion.convert,\n      index: nConversions++\n    })\n  }\n\n  /**\n   * Convenience wrapper to call addConversion on each conversion in a list.\n   *\n   * @param {ConversionDef[]} conversions\n   * @param {{override: boolean}} [options]\n   * @returns {void}\n   * @throws {TypeError}\n   */\n  typed.addConversions = function (conversions, options) {\n    conversions.forEach(conversion => typed.addConversion(conversion, options))\n  }\n\n  /**\n   * Remove the specified conversion. The format is the same as for\n   * addConversion, and the convert function must match or an error\n   * is thrown.\n   *\n   * @param {{from: string, to: string, convert: function}} conversion\n   * @returns {void}\n   * @throws {TypeError|SyntaxError|Error}\n   */\n  typed.removeConversion = function (conversion) {\n    _validateConversion(conversion)\n    const to = findType(conversion.to)\n    const existingConversion =\n      findInArray(to.conversionsTo, c => (c.from === conversion.from))\n    if (!existingConversion) {\n      throw new Error(\n        'Attempt to remove nonexistent conversion from ' + conversion.from +\n        ' to ' + conversion.to)\n    }\n    if (existingConversion.convert !== conversion.convert) {\n      throw new Error(\n        'Conversion to remove does not match existing conversion')\n    }\n    const index = to.conversionsTo.indexOf(existingConversion)\n    to.conversionsTo.splice(index, 1)\n  }\n\n  /**\n   * Produce the specific signature that a typed function\n   * will execute on the given arguments. Here, a \"signature\" is an\n   * object with properties 'params', 'test', 'fn', and 'implementation'.\n   * This last property is a function that converts params as necessary\n   * and then calls 'fn'. Returns null if there is no matching signature.\n   * @param {typed-function} tf\n   * @param {any[]} argList\n   * @returns {{params: string, test: function, fn: function, implementation: function}}\n   */\n  typed.resolve = function (tf, argList) {\n    if (!isTypedFunction(tf)) {\n      throw new TypeError(NOT_TYPED_FUNCTION)\n    }\n    const sigs = tf._typedFunctionData.signatures\n    for (let i = 0; i < sigs.length; ++i) {\n      if (sigs[i].test(argList)) {\n        return sigs[i]\n      }\n    }\n    return null\n  }\n\n  return typed\n}\n\nexport default create()\n"], "mappings": "AAAA,SAASA,EAAEA,CAAA,EAAI;EACb,OAAO,IAAI;AACb;AAEA,SAASC,KAAKA,CAAA,EAAI;EAChB,OAAO,KAAK;AACd;AAEA,SAASC,KAAKA,CAAA,EAAI;EAChB,OAAOC,SAAS;AAClB;AAEA,MAAMC,kBAAkB,GAAG,mCAAmC;;AAE9D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,SAASC,MAAMA,CAAA,EAAI;EACjB;;EAEA;AACF;AACA;EACE,SAASC,aAAaA,CAAEC,CAAC,EAAE;IACzB,OAAO,OAAOA,CAAC,KAAK,QAAQ,IAAIA,CAAC,KAAK,IAAI,IAAIA,CAAC,CAACC,WAAW,KAAKC,MAAM;EACxE;EAEA,MAAMC,MAAM,GAAG,CACb;IAAEC,IAAI,EAAE,QAAQ;IAAEC,IAAI,EAAE,SAAAA,CAAUL,CAAC,EAAE;MAAE,OAAO,OAAOA,CAAC,KAAK,QAAQ;IAAC;EAAE,CAAC,EACvE;IAAEI,IAAI,EAAE,QAAQ;IAAEC,IAAI,EAAE,SAAAA,CAAUL,CAAC,EAAE;MAAE,OAAO,OAAOA,CAAC,KAAK,QAAQ;IAAC;EAAE,CAAC,EACvE;IAAEI,IAAI,EAAE,SAAS;IAAEC,IAAI,EAAE,SAAAA,CAAUL,CAAC,EAAE;MAAE,OAAO,OAAOA,CAAC,KAAK,SAAS;IAAC;EAAE,CAAC,EACzE;IAAEI,IAAI,EAAE,UAAU;IAAEC,IAAI,EAAE,SAAAA,CAAUL,CAAC,EAAE;MAAE,OAAO,OAAOA,CAAC,KAAK,UAAU;IAAC;EAAE,CAAC,EAC3E;IAAEI,IAAI,EAAE,OAAO;IAAEC,IAAI,EAAEC,KAAK,CAACC;EAAQ,CAAC,EACtC;IAAEH,IAAI,EAAE,MAAM;IAAEC,IAAI,EAAE,SAAAA,CAAUL,CAAC,EAAE;MAAE,OAAOA,CAAC,YAAYQ,IAAI;IAAC;EAAE,CAAC,EACjE;IAAEJ,IAAI,EAAE,QAAQ;IAAEC,IAAI,EAAE,SAAAA,CAAUL,CAAC,EAAE;MAAE,OAAOA,CAAC,YAAYS,MAAM;IAAC;EAAE,CAAC,EACrE;IAAEL,IAAI,EAAE,QAAQ;IAAEC,IAAI,EAAEN;EAAc,CAAC,EACvC;IAAEK,IAAI,EAAE,MAAM;IAAEC,IAAI,EAAE,SAAAA,CAAUL,CAAC,EAAE;MAAE,OAAOA,CAAC,KAAK,IAAI;IAAC;EAAE,CAAC,EAC1D;IAAEI,IAAI,EAAE,WAAW;IAAEC,IAAI,EAAE,SAAAA,CAAUL,CAAC,EAAE;MAAE,OAAOA,CAAC,KAAKJ,SAAS;IAAC;EAAE,CAAC,CACrE;EAED,MAAMc,OAAO,GAAG;IACdN,IAAI,EAAE,KAAK;IACXC,IAAI,EAAEZ,EAAE;IACRkB,KAAK,EAAE;EACT,CAAC;;EAED;EACA;EACA;EACA;EACA;EACA,IAAIC,OAAO,EAAC;EACZ,IAAIC,QAAQ,EAAC;;EAEb;EACA,IAAIC,YAAY,GAAG,CAAC;EACpB;;EAEA;EACA,IAAIC,KAAK,GAAG;IAAEC,WAAW,EAAE;EAAE,CAAC;;EAE9B;AACF;AACA;AACA;AACA;AACA;AACA;EACE,SAASC,QAAQA,CAAEC,QAAQ,EAAE;IAC3B,MAAMC,IAAI,GAAGP,OAAO,CAACQ,GAAG,CAACF,QAAQ,CAAC;IAClC,IAAIC,IAAI,EAAE;MACR,OAAOA,IAAI;IACb;IACA;IACA,IAAIE,OAAO,GAAG,gBAAgB,GAAGH,QAAQ,GAAG,GAAG;IAC/C,MAAMd,IAAI,GAAGc,QAAQ,CAACI,WAAW,CAAC,CAAC;IACnC,IAAIC,SAAS;IACb,KAAKA,SAAS,IAAIV,QAAQ,EAAE;MAC1B,IAAIU,SAAS,CAACD,WAAW,CAAC,CAAC,KAAKlB,IAAI,EAAE;QACpCiB,OAAO,IAAI,kBAAkB,GAAGE,SAAS,GAAG,KAAK;QACjD;MACF;IACF;IACA,MAAM,IAAIC,SAAS,CAACH,OAAO,CAAC;EAC9B;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASI,QAAQA,CAAEC,KAAK,EAAsB;IAAA,IAApBC,UAAU,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAhC,SAAA,GAAAgC,SAAA,MAAG,KAAK;IAC1C,MAAME,WAAW,GAAGH,UAAU,GAC1BV,QAAQ,CAACU,UAAU,CAAC,CAACI,KAAK,GAC1BlB,QAAQ,CAACgB,MAAM;IACnB,MAAMG,QAAQ,GAAG,EAAE;IACnB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGP,KAAK,CAACG,MAAM,EAAE,EAAEI,CAAC,EAAE;MACrC,IAAI,CAACP,KAAK,CAACO,CAAC,CAAC,IAAI,OAAOP,KAAK,CAACO,CAAC,CAAC,CAAC7B,IAAI,KAAK,QAAQ,IAChD,OAAOsB,KAAK,CAACO,CAAC,CAAC,CAAC5B,IAAI,KAAK,UAAU,EAAE;QACrC,MAAM,IAAImB,SAAS,CAAC,gEAAgE,CAAC;MACvF;MACA,MAAMN,QAAQ,GAAGQ,KAAK,CAACO,CAAC,CAAC,CAAC7B,IAAI;MAC9B,IAAIQ,OAAO,CAACsB,GAAG,CAAChB,QAAQ,CAAC,EAAE;QACzB,MAAM,IAAIM,SAAS,CAAC,uBAAuB,GAAGN,QAAQ,GAAG,GAAG,CAAC;MAC/D;MACAc,QAAQ,CAACG,IAAI,CAACjB,QAAQ,CAAC;MACvBN,OAAO,CAACwB,GAAG,CAAClB,QAAQ,EAAE;QACpBd,IAAI,EAAEc,QAAQ;QACdb,IAAI,EAAEqB,KAAK,CAACO,CAAC,CAAC,CAAC5B,IAAI;QACnBM,KAAK,EAAEe,KAAK,CAACO,CAAC,CAAC,CAACtB,KAAK;QACrBoB,KAAK,EAAED,WAAW,GAAGG,CAAC;QACtBI,aAAa,EAAE,EAAE,CAAC;MACpB,CAAC,CAAC;IACJ;IACA;IACA,MAAMC,aAAa,GAAGzB,QAAQ,CAAC0B,KAAK,CAACT,WAAW,CAAC;IACjDjB,QAAQ,GACNA,QAAQ,CAAC0B,KAAK,CAAC,CAAC,EAAET,WAAW,CAAC,CAACU,MAAM,CAACR,QAAQ,CAAC,CAACQ,MAAM,CAACF,aAAa,CAAC;IACvE;IACA,KAAK,IAAIL,CAAC,GAAGH,WAAW,GAAGE,QAAQ,CAACH,MAAM,EAAEI,CAAC,GAAGpB,QAAQ,CAACgB,MAAM,EAAE,EAAEI,CAAC,EAAE;MACpErB,OAAO,CAACQ,GAAG,CAACP,QAAQ,CAACoB,CAAC,CAAC,CAAC,CAACF,KAAK,GAAGE,CAAC;IACpC;EACF;;EAEA;AACF;AACA;AACA;AACA;AACA;EACE,SAASQ,KAAKA,CAAA,EAAI;IAChB7B,OAAO,GAAG,IAAI8B,GAAG,CAAC,CAAC;IACnB7B,QAAQ,GAAG,EAAE;IACbC,YAAY,GAAG,CAAC;IAChBW,QAAQ,CAAC,CAACf,OAAO,CAAC,EAAE,KAAK,CAAC;EAC5B;;EAEA;EACA+B,KAAK,CAAC,CAAC;EACPhB,QAAQ,CAACtB,MAAM,CAAC;;EAEhB;AACF;AACA;EACE,SAASwC,gBAAgBA,CAAA,EAAI;IAC3B,IAAIzB,QAAQ;IACZ,KAAKA,QAAQ,IAAIL,QAAQ,EAAE;MACzBD,OAAO,CAACQ,GAAG,CAACF,QAAQ,CAAC,CAACmB,aAAa,GAAG,EAAE;IAC1C;IACAvB,YAAY,GAAG,CAAC;EAClB;;EAEA;AACF;AACA;AACA;AACA;AACA;EACE,SAAS8B,aAAaA,CAAEC,KAAK,EAAE;IAC7B,MAAMC,OAAO,GAAGjC,QAAQ,CAACkC,MAAM,CAAC3C,IAAI,IAAI;MACtC,MAAMe,IAAI,GAAGP,OAAO,CAACQ,GAAG,CAAChB,IAAI,CAAC;MAC9B,OAAO,CAACe,IAAI,CAACR,KAAK,IAAIQ,IAAI,CAACd,IAAI,CAACwC,KAAK,CAAC;IACxC,CAAC,CAAC;IACF,IAAIC,OAAO,CAACjB,MAAM,EAAE;MAClB,OAAOiB,OAAO;IAChB;IACA,OAAO,CAAC,KAAK,CAAC;EAChB;;EAEA;AACF;AACA;AACA;AACA;EACE,SAASE,eAAeA,CAAEC,MAAM,EAAE;IAChC,OAAOA,MAAM,IAAI,OAAOA,MAAM,KAAK,UAAU,IAC3C,oBAAoB,IAAIA,MAAM;EAClC;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASC,aAAaA,CAAEC,EAAE,EAAEC,SAAS,EAAEC,OAAO,EAAE;IAC9C,IAAI,CAACL,eAAe,CAACG,EAAE,CAAC,EAAE;MACxB,MAAM,IAAI3B,SAAS,CAAC3B,kBAAkB,CAAC;IACzC;;IAEA;IACA,MAAMyD,KAAK,GAAGD,OAAO,IAAIA,OAAO,CAACC,KAAK;IACtC,MAAMC,eAAe,GAAGjD,KAAK,CAACC,OAAO,CAAC6C,SAAS,CAAC,GAC5CA,SAAS,CAACI,IAAI,CAAC,GAAG,CAAC,GACnBJ,SAAS;IACb,MAAMK,MAAM,GAAGC,cAAc,CAACH,eAAe,CAAC;IAC9C,MAAMI,kBAAkB,GAAGC,eAAe,CAACH,MAAM,CAAC;;IAElD;IACA,IAAI,CAACH,KAAK,IAAIK,kBAAkB,IAAIR,EAAE,CAACU,UAAU,EAAE;MACjD;MACA,MAAMC,KAAK,GACTX,EAAE,CAACY,kBAAkB,CAACC,YAAY,CAAC5C,GAAG,CAACuC,kBAAkB,CAAC;MAC5D,IAAIG,KAAK,EAAE;QACT,OAAOA,KAAK;MACd;IACF;;IAEA;IACA;IACA;IACA;IACA,MAAMG,OAAO,GAAGR,MAAM,CAAC5B,MAAM;IAC7B,IAAIqC,mBAAmB;IACvB,IAAIZ,KAAK,EAAE;MACTY,mBAAmB,GAAG,EAAE;MACxB,IAAI9D,IAAI;MACR,KAAKA,IAAI,IAAI+C,EAAE,CAACU,UAAU,EAAE;QAC1BK,mBAAmB,CAAC/B,IAAI,CAACgB,EAAE,CAACY,kBAAkB,CAACC,YAAY,CAAC5C,GAAG,CAAChB,IAAI,CAAC,CAAC;MACxE;IACF,CAAC,MAAM;MACL8D,mBAAmB,GAAGf,EAAE,CAACY,kBAAkB,CAACF,UAAU;IACxD;IACA,KAAK,IAAI5B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGgC,OAAO,EAAE,EAAEhC,CAAC,EAAE;MAChC,MAAMkC,IAAI,GAAGV,MAAM,CAACxB,CAAC,CAAC;MACtB,MAAMmC,kBAAkB,GAAG,EAAE;MAC7B,IAAIC,WAAW;MACf,KAAKA,WAAW,IAAIH,mBAAmB,EAAE;QACvC,MAAMI,IAAI,GAAGC,eAAe,CAACF,WAAW,CAACZ,MAAM,EAAExB,CAAC,CAAC;QACnD,IAAI,CAACqC,IAAI,IAAKH,IAAI,CAACK,SAAS,IAAI,CAACF,IAAI,CAACE,SAAU,EAAE;UAChD;QACF;QACA,IAAI,CAACF,IAAI,CAACG,MAAM,EAAE;UAChB;UACA,MAAMC,SAAS,GAAGC,YAAY,CAACL,IAAI,CAAC;UACpC,IAAIH,IAAI,CAACzC,KAAK,CAACkD,IAAI,CAACC,KAAK,IAAI,CAACH,SAAS,CAACxC,GAAG,CAAC2C,KAAK,CAACzE,IAAI,CAAC,CAAC,EAAE;YACxD;UACF;QACF;QACA;QACAgE,kBAAkB,CAACjC,IAAI,CAACkC,WAAW,CAAC;MACtC;MACAH,mBAAmB,GAAGE,kBAAkB;MACxC,IAAIF,mBAAmB,CAACrC,MAAM,KAAK,CAAC,EAAE;IACxC;IACA;IACA,IAAIiD,SAAS;IACb,KAAKA,SAAS,IAAIZ,mBAAmB,EAAE;MACrC,IAAIY,SAAS,CAACrB,MAAM,CAAC5B,MAAM,IAAIoC,OAAO,EAAE;QACtC,OAAOa,SAAS;MAClB;IACF;IAEA,MAAM,IAAItD,SAAS,CAAC,kCAAkC,IAAI2B,EAAE,CAAC/C,IAAI,IAAI,SAAS,CAAC,GAAG,GAAG,GAAGwD,eAAe,CAACH,MAAM,EAAE,IAAI,CAAC,GAAG,IAAI,CAAC;EAC/H;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASsB,IAAIA,CAAE5B,EAAE,EAAEC,SAAS,EAAEC,OAAO,EAAE;IACrC,OAAOH,aAAa,CAACC,EAAE,EAAEC,SAAS,EAAEC,OAAO,CAAC,CAAC2B,cAAc;EAC7D;;EAEA;AACF;AACA;AACA;AACA;AACA;EACE,SAASC,OAAOA,CAAEpC,KAAK,EAAE3B,QAAQ,EAAE;IACjC;IACA,MAAMC,IAAI,GAAGF,QAAQ,CAACC,QAAQ,CAAC;IAC/B,IAAIC,IAAI,CAACd,IAAI,CAACwC,KAAK,CAAC,EAAE;MACpB,OAAOA,KAAK;IACd;IACA,MAAMqC,WAAW,GAAG/D,IAAI,CAACkB,aAAa;IACtC,IAAI6C,WAAW,CAACrD,MAAM,KAAK,CAAC,EAAE;MAC5B,MAAM,IAAIsD,KAAK,CACb,8BAA8B,GAAGjE,QAAQ,GAAG,WAAW,CAAC;IAC5D;IACA,KAAK,IAAIe,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGiD,WAAW,CAACrD,MAAM,EAAEI,CAAC,EAAE,EAAE;MAC3C,MAAMmD,QAAQ,GAAGnE,QAAQ,CAACiE,WAAW,CAACjD,CAAC,CAAC,CAACoD,IAAI,CAAC;MAC9C,IAAID,QAAQ,CAAC/E,IAAI,CAACwC,KAAK,CAAC,EAAE;QACxB,OAAOqC,WAAW,CAACjD,CAAC,CAAC,CAACgD,OAAO,CAACpC,KAAK,CAAC;MACtC;IACF;IAEA,MAAM,IAAIsC,KAAK,CAAC,iBAAiB,GAAGtC,KAAK,GAAG,MAAM,GAAG3B,QAAQ,CAAC;EAChE;;EAEA;AACF;AACA;AACA;AACA;AACA;EACE,SAAS0C,eAAeA,CAAEH,MAAM,EAAmB;IAAA,IAAjB6B,SAAS,GAAA1D,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAhC,SAAA,GAAAgC,SAAA,MAAG,GAAG;IAC/C,OAAO6B,MAAM,CAAC8B,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACpF,IAAI,CAAC,CAACoD,IAAI,CAAC8B,SAAS,CAAC;EAChD;;EAEA;AACF;AACA;AACA;AACA;EACE,SAASG,UAAUA,CAAEC,KAAK,EAAE;IAC1B,MAAMlB,SAAS,GAAGkB,KAAK,CAACC,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC;IAC5C,MAAMjE,KAAK,GAAI,CAAC8C,SAAS,GACrBkB,KAAK,GACJA,KAAK,CAAC7D,MAAM,GAAG,CAAC,GACb6D,KAAK,CAACnD,KAAK,CAAC,CAAC,CAAC,GACd,KAAK;IAEb,MAAMqD,QAAQ,GAAGlE,KAAK,CAACmE,KAAK,CAAC,GAAG,CAAC,CAACN,GAAG,CAACO,CAAC,IAAI7E,QAAQ,CAAC6E,CAAC,CAACC,IAAI,CAAC,CAAC,CAAC,CAAC;IAE9D,IAAItB,MAAM,GAAG,KAAK;IAClB,IAAIuB,SAAS,GAAGxB,SAAS,GAAG,KAAK,GAAG,EAAE;IAEtC,MAAMyB,UAAU,GAAGL,QAAQ,CAACL,GAAG,CAAC,UAAUpE,IAAI,EAAE;MAC9CsD,MAAM,GAAGtD,IAAI,CAACR,KAAK,IAAI8D,MAAM;MAC7BuB,SAAS,IAAI7E,IAAI,CAACf,IAAI,GAAG,GAAG;MAE5B,OAAO;QACLA,IAAI,EAAEe,IAAI,CAACf,IAAI;QACf8F,SAAS,EAAE/E,IAAI,CAACY,KAAK;QACrB1B,IAAI,EAAEc,IAAI,CAACd,IAAI;QACfM,KAAK,EAAEQ,IAAI,CAACR,KAAK;QACjBwF,UAAU,EAAE,IAAI;QAChBC,eAAe,EAAE,CAAC;MACpB,CAAC;IACH,CAAC,CAAC;IAEF,OAAO;MACL1E,KAAK,EAAEuE,UAAU;MACjB7F,IAAI,EAAE4F,SAAS,CAACzD,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MAAE;MAC9BkC,MAAM;MACN4B,aAAa,EAAE,KAAK;MACpB7B;IACF,CAAC;EACH;;EAEA;AACF;AACA;AACA;AACA;AACA;EACE,SAAS8B,WAAWA,CAAEZ,KAAK,EAAE;IAC3B,MAAMa,SAAS,GAAGb,KAAK,CAAChE,KAAK,CAAC6D,GAAG,CAACiB,CAAC,IAAIA,CAAC,CAACpG,IAAI,CAAC;IAC9C,MAAMqG,mBAAmB,GAAGC,oBAAoB,CAACH,SAAS,CAAC;IAC3D,IAAI9B,MAAM,GAAGiB,KAAK,CAACjB,MAAM;IACzB,IAAIkC,OAAO,GAAGjB,KAAK,CAACtF,IAAI;IAExB,MAAMwG,gBAAgB,GAAGH,mBAAmB,CAAClB,GAAG,CAAC,UAAUY,UAAU,EAAE;MACrE,MAAMhF,IAAI,GAAGF,QAAQ,CAACkF,UAAU,CAACd,IAAI,CAAC;MACtCZ,MAAM,GAAGtD,IAAI,CAACR,KAAK,IAAI8D,MAAM;MAC7BkC,OAAO,IAAI,GAAG,GAAGR,UAAU,CAACd,IAAI;MAEhC,OAAO;QACLjF,IAAI,EAAE+F,UAAU,CAACd,IAAI;QACrBa,SAAS,EAAE/E,IAAI,CAACY,KAAK;QACrB1B,IAAI,EAAEc,IAAI,CAACd,IAAI;QACfM,KAAK,EAAEQ,IAAI,CAACR,KAAK;QACjBwF,UAAU;QACVC,eAAe,EAAED,UAAU,CAACpE;MAC9B,CAAC;IACH,CAAC,CAAC;IAEF,OAAO;MACLL,KAAK,EAAEgE,KAAK,CAAChE,KAAK,CAACc,MAAM,CAACoE,gBAAgB,CAAC;MAC3CxG,IAAI,EAAEuG,OAAO;MACblC,MAAM;MACN4B,aAAa,EAAEO,gBAAgB,CAAC/E,MAAM,GAAG,CAAC;MAC1C2C,SAAS,EAAEkB,KAAK,CAAClB;IACnB,CAAC;EACH;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;EACE,SAASG,YAAYA,CAAEe,KAAK,EAAE;IAC5B,IAAI,CAACA,KAAK,CAACmB,OAAO,EAAE;MAClBnB,KAAK,CAACmB,OAAO,GAAG,IAAIC,GAAG,CAAC,CAAC;MACzBpB,KAAK,CAAChE,KAAK,CAACqF,OAAO,CAAC5F,IAAI,IAAIuE,KAAK,CAACmB,OAAO,CAACG,GAAG,CAAC7F,IAAI,CAACf,IAAI,CAAC,CAAC;IAC3D;IACA,OAAOsF,KAAK,CAACmB,OAAO;EACtB;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;EACE,SAASnD,cAAcA,CAAEuD,YAAY,EAAE;IACrC,MAAMxD,MAAM,GAAG,EAAE;IACjB,IAAI,OAAOwD,YAAY,KAAK,QAAQ,EAAE;MACpC,MAAM,IAAIzF,SAAS,CAAC,4BAA4B,CAAC;IACnD;IACA,MAAM4B,SAAS,GAAG6D,YAAY,CAAClB,IAAI,CAAC,CAAC;IACrC,IAAI3C,SAAS,KAAK,EAAE,EAAE;MACpB,OAAOK,MAAM;IACf;IAEA,MAAMyD,SAAS,GAAG9D,SAAS,CAACyC,KAAK,CAAC,GAAG,CAAC;IACtC,KAAK,IAAI5D,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGiF,SAAS,CAACrF,MAAM,EAAE,EAAEI,CAAC,EAAE;MACzC,MAAMkF,WAAW,GAAG1B,UAAU,CAACyB,SAAS,CAACjF,CAAC,CAAC,CAAC8D,IAAI,CAAC,CAAC,CAAC;MACnD,IAAIoB,WAAW,CAAC3C,SAAS,IAAKvC,CAAC,KAAKiF,SAAS,CAACrF,MAAM,GAAG,CAAE,EAAE;QACzD,MAAM,IAAIuF,WAAW,CACnB,6BAA6B,GAAGF,SAAS,CAACjF,CAAC,CAAC,GAAG,KAAK,GACpD,qCAAqC,CAAC;MAC1C;MACA;MACA,IAAIkF,WAAW,CAACzF,KAAK,CAACG,MAAM,KAAK,CAAC,EAAE;QAClC,OAAO,IAAI;MACb;MACA4B,MAAM,CAACtB,IAAI,CAACgF,WAAW,CAAC;IAC1B;IAEA,OAAO1D,MAAM;EACf;;EAEA;AACF;AACA;AACA;AACA;EACE,SAAS4D,YAAYA,CAAE5D,MAAM,EAAE;IAC7B,MAAMiC,KAAK,GAAG4B,IAAI,CAAC7D,MAAM,CAAC;IAC1B,OAAOiC,KAAK,GAAGA,KAAK,CAAClB,SAAS,GAAG,KAAK;EACxC;;EAEA;AACF;AACA;AACA;AACA;AACA;EACE,SAAS+C,WAAWA,CAAE7B,KAAK,EAAE;IAC3B,IAAI,CAACA,KAAK,IAAIA,KAAK,CAAChE,KAAK,CAACG,MAAM,KAAK,CAAC,EAAE;MACtC;MACA,OAAOpC,EAAE;IACX,CAAC,MAAM,IAAIiG,KAAK,CAAChE,KAAK,CAACG,MAAM,KAAK,CAAC,EAAE;MACnC,OAAOZ,QAAQ,CAACyE,KAAK,CAAChE,KAAK,CAAC,CAAC,CAAC,CAACtB,IAAI,CAAC,CAACC,IAAI;IAC3C,CAAC,MAAM,IAAIqF,KAAK,CAAChE,KAAK,CAACG,MAAM,KAAK,CAAC,EAAE;MACnC,MAAM2F,KAAK,GAAGvG,QAAQ,CAACyE,KAAK,CAAChE,KAAK,CAAC,CAAC,CAAC,CAACtB,IAAI,CAAC,CAACC,IAAI;MAChD,MAAMoH,KAAK,GAAGxG,QAAQ,CAACyE,KAAK,CAAChE,KAAK,CAAC,CAAC,CAAC,CAACtB,IAAI,CAAC,CAACC,IAAI;MAChD,OAAO,SAASqH,EAAEA,CAAE1H,CAAC,EAAE;QACrB,OAAOwH,KAAK,CAACxH,CAAC,CAAC,IAAIyH,KAAK,CAACzH,CAAC,CAAC;MAC7B,CAAC;IACH,CAAC,MAAM;MAAE;MACP,MAAM2H,KAAK,GAAGjC,KAAK,CAAChE,KAAK,CAAC6D,GAAG,CAAC,UAAUpE,IAAI,EAAE;QAC5C,OAAOF,QAAQ,CAACE,IAAI,CAACf,IAAI,CAAC,CAACC,IAAI;MACjC,CAAC,CAAC;MACF,OAAO,SAASqH,EAAEA,CAAE1H,CAAC,EAAE;QACrB,KAAK,IAAIiC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG0F,KAAK,CAAC9F,MAAM,EAAEI,CAAC,EAAE,EAAE;UACrC,IAAI0F,KAAK,CAAC1F,CAAC,CAAC,CAACjC,CAAC,CAAC,EAAE;YACf,OAAO,IAAI;UACb;QACF;QACA,OAAO,KAAK;MACd,CAAC;IACH;EACF;;EAEA;AACF;AACA;AACA;AACA;EACE,SAAS4H,YAAYA,CAAEnE,MAAM,EAAE;IAC7B,IAAIkE,KAAK,EAAEH,KAAK,EAAEC,KAAK;IAEvB,IAAIJ,YAAY,CAAC5D,MAAM,CAAC,EAAE;MACxB;MACAkE,KAAK,GAAGE,OAAO,CAACpE,MAAM,CAAC,CAAC8B,GAAG,CAACgC,WAAW,CAAC;MACxC,MAAMO,QAAQ,GAAGH,KAAK,CAAC9F,MAAM;MAC7B,MAAMkG,QAAQ,GAAGR,WAAW,CAACD,IAAI,CAAC7D,MAAM,CAAC,CAAC;MAC1C,MAAMuE,aAAa,GAAG,SAAAA,CAAUC,IAAI,EAAE;QACpC,KAAK,IAAIhG,CAAC,GAAG6F,QAAQ,EAAE7F,CAAC,GAAGgG,IAAI,CAACpG,MAAM,EAAEI,CAAC,EAAE,EAAE;UAC3C,IAAI,CAAC8F,QAAQ,CAACE,IAAI,CAAChG,CAAC,CAAC,CAAC,EAAE;YACtB,OAAO,KAAK;UACd;QACF;QACA,OAAO,IAAI;MACb,CAAC;MAED,OAAO,SAASiG,QAAQA,CAAED,IAAI,EAAE;QAC9B,KAAK,IAAIhG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG0F,KAAK,CAAC9F,MAAM,EAAEI,CAAC,EAAE,EAAE;UACrC,IAAI,CAAC0F,KAAK,CAAC1F,CAAC,CAAC,CAACgG,IAAI,CAAChG,CAAC,CAAC,CAAC,EAAE;YACtB,OAAO,KAAK;UACd;QACF;QACA,OAAO+F,aAAa,CAACC,IAAI,CAAC,IAAKA,IAAI,CAACpG,MAAM,IAAIiG,QAAQ,GAAG,CAAE;MAC7D,CAAC;IACH,CAAC,MAAM;MACL;MACA,IAAIrE,MAAM,CAAC5B,MAAM,KAAK,CAAC,EAAE;QACvB,OAAO,SAASqG,QAAQA,CAAED,IAAI,EAAE;UAC9B,OAAOA,IAAI,CAACpG,MAAM,KAAK,CAAC;QAC1B,CAAC;MACH,CAAC,MAAM,IAAI4B,MAAM,CAAC5B,MAAM,KAAK,CAAC,EAAE;QAC9B2F,KAAK,GAAGD,WAAW,CAAC9D,MAAM,CAAC,CAAC,CAAC,CAAC;QAC9B,OAAO,SAASyE,QAAQA,CAAED,IAAI,EAAE;UAC9B,OAAOT,KAAK,CAACS,IAAI,CAAC,CAAC,CAAC,CAAC,IAAIA,IAAI,CAACpG,MAAM,KAAK,CAAC;QAC5C,CAAC;MACH,CAAC,MAAM,IAAI4B,MAAM,CAAC5B,MAAM,KAAK,CAAC,EAAE;QAC9B2F,KAAK,GAAGD,WAAW,CAAC9D,MAAM,CAAC,CAAC,CAAC,CAAC;QAC9BgE,KAAK,GAAGF,WAAW,CAAC9D,MAAM,CAAC,CAAC,CAAC,CAAC;QAC9B,OAAO,SAASyE,QAAQA,CAAED,IAAI,EAAE;UAC9B,OAAOT,KAAK,CAACS,IAAI,CAAC,CAAC,CAAC,CAAC,IAAIR,KAAK,CAACQ,IAAI,CAAC,CAAC,CAAC,CAAC,IAAIA,IAAI,CAACpG,MAAM,KAAK,CAAC;QAC9D,CAAC;MACH,CAAC,MAAM;QAAE;QACP8F,KAAK,GAAGlE,MAAM,CAAC8B,GAAG,CAACgC,WAAW,CAAC;QAC/B,OAAO,SAASW,QAAQA,CAAED,IAAI,EAAE;UAC9B,KAAK,IAAIhG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG0F,KAAK,CAAC9F,MAAM,EAAEI,CAAC,EAAE,EAAE;YACrC,IAAI,CAAC0F,KAAK,CAAC1F,CAAC,CAAC,CAACgG,IAAI,CAAChG,CAAC,CAAC,CAAC,EAAE;cACtB,OAAO,KAAK;YACd;UACF;UACA,OAAOgG,IAAI,CAACpG,MAAM,KAAK8F,KAAK,CAAC9F,MAAM;QACrC,CAAC;MACH;IACF;EACF;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAAS0C,eAAeA,CAAEd,MAAM,EAAE1B,KAAK,EAAE;IACvC,OAAOA,KAAK,GAAG0B,MAAM,CAAC5B,MAAM,GACxB4B,MAAM,CAAC1B,KAAK,CAAC,GACbsF,YAAY,CAAC5D,MAAM,CAAC,GAAG6D,IAAI,CAAC7D,MAAM,CAAC,GAAG,IAAI;EAChD;;EAEA;AACF;AACA;AACA;AACA;AACA;EACE,SAAS0E,iBAAiBA,CAAE1E,MAAM,EAAE1B,KAAK,EAAE;IACzC,MAAM2D,KAAK,GAAGnB,eAAe,CAACd,MAAM,EAAE1B,KAAK,CAAC;IAC5C,IAAI,CAAC2D,KAAK,EAAE;MACV,OAAO,IAAIoB,GAAG,CAAC,CAAC;IAClB;IACA,OAAOnC,YAAY,CAACe,KAAK,CAAC;EAC5B;;EAEA;AACF;AACA;AACA;AACA;EACE,SAAS0C,WAAWA,CAAEjH,IAAI,EAAE;IAC1B,OAAOA,IAAI,CAACgF,UAAU,KAAK,IAAI,IAAIhF,IAAI,CAACgF,UAAU,KAAKvG,SAAS;EAClE;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;EACE,SAASyI,mBAAmBA,CAAExE,UAAU,EAAE9B,KAAK,EAAE;IAC/C,MAAM8E,OAAO,GAAG,IAAIC,GAAG,CAAC,CAAC;IACzBjD,UAAU,CAACkD,OAAO,CAAC3D,SAAS,IAAI;MAC9B,MAAMkF,QAAQ,GAAGH,iBAAiB,CAAC/E,SAAS,CAACK,MAAM,EAAE1B,KAAK,CAAC;MAC3D,IAAI3B,IAAI;MACR,KAAKA,IAAI,IAAIkI,QAAQ,EAAE;QACrBzB,OAAO,CAACG,GAAG,CAAC5G,IAAI,CAAC;MACnB;IACF,CAAC,CAAC;IAEF,OAAOyG,OAAO,CAAC3E,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG5B,KAAK,CAAC+E,IAAI,CAACwB,OAAO,CAAC;EAC3D;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAAS0B,WAAWA,CAAEnI,IAAI,EAAE6H,IAAI,EAAEpE,UAAU,EAAE;IAC5C,IAAI2E,GAAG,EAAEC,QAAQ;IACjB,MAAMC,KAAK,GAAGtI,IAAI,IAAI,SAAS;;IAE/B;IACA,IAAIuI,kBAAkB,GAAG9E,UAAU;IACnC,IAAI9B,KAAK;IACT,KAAKA,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGkG,IAAI,CAACpG,MAAM,EAAEE,KAAK,EAAE,EAAE;MAC5C,MAAM6G,gBAAgB,GAAG,EAAE;MAC3BD,kBAAkB,CAAC5B,OAAO,CAAC3D,SAAS,IAAI;QACtC,MAAMsC,KAAK,GAAGnB,eAAe,CAACnB,SAAS,CAACK,MAAM,EAAE1B,KAAK,CAAC;QACtD,MAAM1B,IAAI,GAAGkH,WAAW,CAAC7B,KAAK,CAAC;QAC/B,IAAI,CAAC3D,KAAK,GAAGqB,SAAS,CAACK,MAAM,CAAC5B,MAAM,IAClCwF,YAAY,CAACjE,SAAS,CAACK,MAAM,CAAC,KAC9BpD,IAAI,CAAC4H,IAAI,CAAClG,KAAK,CAAC,CAAC,EAAE;UACnB6G,gBAAgB,CAACzG,IAAI,CAACiB,SAAS,CAAC;QAClC;MACF,CAAC,CAAC;MAEF,IAAIwF,gBAAgB,CAAC/G,MAAM,KAAK,CAAC,EAAE;QACjC;QACA4G,QAAQ,GAAGJ,mBAAmB,CAACM,kBAAkB,EAAE5G,KAAK,CAAC;QACzD,IAAI0G,QAAQ,CAAC5G,MAAM,GAAG,CAAC,EAAE;UACvB,MAAMgH,WAAW,GAAGjG,aAAa,CAACqF,IAAI,CAAClG,KAAK,CAAC,CAAC;UAE9CyG,GAAG,GAAG,IAAIhH,SAAS,CAAC,0CAA0C,GAAGkH,KAAK,GACpE,cAAc,GAAGD,QAAQ,CAACjF,IAAI,CAAC,MAAM,CAAC,GACtC,YAAY,GAAGqF,WAAW,CAACrF,IAAI,CAAC,KAAK,CAAC,GAAG,WAAW,GAAGzB,KAAK,GAAG,GAAG,CAAC;UACrEyG,GAAG,CAACM,IAAI,GAAG;YACTC,QAAQ,EAAE,WAAW;YACrB5F,EAAE,EAAEuF,KAAK;YACT3G,KAAK;YACLiH,MAAM,EAAEH,WAAW;YACnBJ;UACF,CAAC;UACD,OAAOD,GAAG;QACZ;MACF,CAAC,MAAM;QACLG,kBAAkB,GAAGC,gBAAgB;MACvC;IACF;;IAEA;IACA,MAAMK,OAAO,GAAGN,kBAAkB,CAACpD,GAAG,CAAC,UAAUnC,SAAS,EAAE;MAC1D,OAAOiE,YAAY,CAACjE,SAAS,CAACK,MAAM,CAAC,GACjCyF,QAAQ,GACR9F,SAAS,CAACK,MAAM,CAAC5B,MAAM;IAC7B,CAAC,CAAC;IACF,IAAIoG,IAAI,CAACpG,MAAM,GAAGsH,IAAI,CAACC,GAAG,CAACC,KAAK,CAAC,IAAI,EAAEJ,OAAO,CAAC,EAAE;MAC/CR,QAAQ,GAAGJ,mBAAmB,CAACM,kBAAkB,EAAE5G,KAAK,CAAC;MACzDyG,GAAG,GAAG,IAAIhH,SAAS,CAAC,gCAAgC,GAAGkH,KAAK,GAC1D,cAAc,GAAGD,QAAQ,CAACjF,IAAI,CAAC,MAAM,CAAC,GACtC,WAAW,GAAGyE,IAAI,CAACpG,MAAM,GAAG,GAAG,CAAC;MAClC2G,GAAG,CAACM,IAAI,GAAG;QACTC,QAAQ,EAAE,YAAY;QACtB5F,EAAE,EAAEuF,KAAK;QACT3G,KAAK,EAAEkG,IAAI,CAACpG,MAAM;QAClB4G;MACF,CAAC;MACD,OAAOD,GAAG;IACZ;;IAEA;IACA,MAAMc,SAAS,GAAGH,IAAI,CAACI,GAAG,CAACF,KAAK,CAAC,IAAI,EAAEJ,OAAO,CAAC;IAC/C,IAAIhB,IAAI,CAACpG,MAAM,GAAGyH,SAAS,EAAE;MAC3Bd,GAAG,GAAG,IAAIhH,SAAS,CAAC,iCAAiC,GAAGkH,KAAK,GAC3D,cAAc,GAAGY,SAAS,GAAG,YAAY,GAAGrB,IAAI,CAACpG,MAAM,GAAG,GAAG,CAAC;MAChE2G,GAAG,CAACM,IAAI,GAAG;QACTC,QAAQ,EAAE,aAAa;QACvB5F,EAAE,EAAEuF,KAAK;QACT3G,KAAK,EAAEkG,IAAI,CAACpG,MAAM;QAClB2H,cAAc,EAAEF;MAClB,CAAC;MACD,OAAOd,GAAG;IACZ;;IAEA;IACA,MAAMiB,QAAQ,GAAG,EAAE;IACnB,KAAK,IAAIxH,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGgG,IAAI,CAACpG,MAAM,EAAE,EAAEI,CAAC,EAAE;MACpCwH,QAAQ,CAACtH,IAAI,CAACS,aAAa,CAACqF,IAAI,CAAChG,CAAC,CAAC,CAAC,CAACuB,IAAI,CAAC,GAAG,CAAC,CAAC;IACjD;IACAgF,GAAG,GAAG,IAAIhH,SAAS,CAAC,qBAAqB,GAAGiI,QAAQ,CAACjG,IAAI,CAAC,IAAI,CAAC,GAC7D,2DAA2D,GAAGkF,KAAK,GAAG,GAAG,CAAC;IAC5EF,GAAG,CAACM,IAAI,GAAG;MACTC,QAAQ,EAAE,UAAU;MACpBC,MAAM,EAAES;IACV,CAAC;IACD,OAAOjB,GAAG;EACZ;;EAEA;AACF;AACA;AACA;AACA;EACE,SAASkB,kBAAkBA,CAAEhE,KAAK,EAAE;IAClC,IAAI0D,GAAG,GAAGvI,QAAQ,CAACgB,MAAM,GAAG,CAAC;IAE7B,KAAK,IAAII,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGyD,KAAK,CAAChE,KAAK,CAACG,MAAM,EAAEI,CAAC,EAAE,EAAE;MAC3C,IAAImG,WAAW,CAAC1C,KAAK,CAAChE,KAAK,CAACO,CAAC,CAAC,CAAC,EAAE;QAC/BmH,GAAG,GAAGD,IAAI,CAACC,GAAG,CAACA,GAAG,EAAE1D,KAAK,CAAChE,KAAK,CAACO,CAAC,CAAC,CAACiE,SAAS,CAAC;MAC/C;IACF;IAEA,OAAOkD,GAAG;EACZ;;EAEA;AACF;AACA;AACA;AACA;AACA;EACE,SAASO,wBAAwBA,CAAEjE,KAAK,EAAE;IACxC,IAAI0D,GAAG,GAAGtI,YAAY,GAAG,CAAC;IAE1B,KAAK,IAAImB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGyD,KAAK,CAAChE,KAAK,CAACG,MAAM,EAAEI,CAAC,EAAE,EAAE;MAC3C,IAAI,CAACmG,WAAW,CAAC1C,KAAK,CAAChE,KAAK,CAACO,CAAC,CAAC,CAAC,EAAE;QAChCmH,GAAG,GAAGD,IAAI,CAACC,GAAG,CAACA,GAAG,EAAE1D,KAAK,CAAChE,KAAK,CAACO,CAAC,CAAC,CAACmE,eAAe,CAAC;MACrD;IACF;IAEA,OAAOgD,GAAG;EACZ;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASQ,aAAaA,CAAEC,MAAM,EAAEC,MAAM,EAAE;IACtC;IACA;IACA,IAAID,MAAM,CAACpF,MAAM,EAAE;MACjB,IAAI,CAACqF,MAAM,CAACrF,MAAM,EAAE;QAClB,OAAO,CAAC;MACV;IACF,CAAC,MAAM,IAAIqF,MAAM,CAACrF,MAAM,EAAE;MACxB,OAAO,CAAC,CAAC;IACX;;IAEA;IACA,IAAIoF,MAAM,CAACrF,SAAS,EAAE;MACpB,IAAI,CAACsF,MAAM,CAACtF,SAAS,EAAE;QACrB,OAAO,CAAC;MACV;IACF,CAAC,MAAM,IAAIsF,MAAM,CAACtF,SAAS,EAAE;MAC3B,OAAO,CAAC,CAAC;IACX;;IAEA;IACA,IAAIqF,MAAM,CAACxD,aAAa,EAAE;MACxB,IAAI,CAACyD,MAAM,CAACzD,aAAa,EAAE;QACzB,OAAO,CAAC;MACV;IACF,CAAC,MAAM,IAAIyD,MAAM,CAACzD,aAAa,EAAE;MAC/B,OAAO,CAAC,CAAC;IACX;;IAEA;IACA,MAAM0D,QAAQ,GAAGL,kBAAkB,CAACG,MAAM,CAAC,GAAGH,kBAAkB,CAACI,MAAM,CAAC;IACxE,IAAIC,QAAQ,GAAG,CAAC,EAAE;MAChB,OAAO,CAAC,CAAC;IACX;IACA,IAAIA,QAAQ,GAAG,CAAC,EAAE;MAChB,OAAO,CAAC;IACV;;IAEA;IACA,MAAMC,QAAQ,GACZL,wBAAwB,CAACE,MAAM,CAAC,GAAGF,wBAAwB,CAACG,MAAM,CAAC;IACrE,IAAIE,QAAQ,GAAG,CAAC,EAAE;MAChB,OAAO,CAAC,CAAC;IACX;IACA,IAAIA,QAAQ,GAAG,CAAC,EAAE;MAChB,OAAO,CAAC;IACV;;IAEA;IACA,OAAO,CAAC;EACV;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASC,iBAAiBA,CAAEC,UAAU,EAAEC,UAAU,EAAE;IAClD,MAAMC,KAAK,GAAGF,UAAU,CAACzG,MAAM;IAC/B,MAAM4G,KAAK,GAAGF,UAAU,CAAC1G,MAAM;IAC/B,MAAM6G,KAAK,GAAGhD,IAAI,CAAC8C,KAAK,CAAC;IACzB,MAAMG,KAAK,GAAGjD,IAAI,CAAC+C,KAAK,CAAC;IACzB,MAAMG,QAAQ,GAAGnD,YAAY,CAAC+C,KAAK,CAAC;IACpC,MAAMK,QAAQ,GAAGpD,YAAY,CAACgD,KAAK,CAAC;IACpC;IACA;IACA,IAAIG,QAAQ,IAAIF,KAAK,CAAC7F,MAAM,EAAE;MAC5B,IAAI,CAACgG,QAAQ,IAAI,CAACF,KAAK,CAAC9F,MAAM,EAAE;QAC9B,OAAO,CAAC;MACV;IACF,CAAC,MAAM,IAAIgG,QAAQ,IAAIF,KAAK,CAAC9F,MAAM,EAAE;MACnC,OAAO,CAAC,CAAC;IACX;;IAEA;IACA,IAAIiG,IAAI,GAAG,CAAC;IACZ,IAAIC,KAAK,GAAG,CAAC;IACb,IAAIC,GAAG;IACP,KAAKA,GAAG,IAAIR,KAAK,EAAE;MACjB,IAAIQ,GAAG,CAACnG,MAAM,EAAE,EAAEiG,IAAI;MACtB,IAAIE,GAAG,CAACvE,aAAa,EAAE,EAAEsE,KAAK;IAChC;IACA,IAAIE,IAAI,GAAG,CAAC;IACZ,IAAIC,KAAK,GAAG,CAAC;IACb,KAAKF,GAAG,IAAIP,KAAK,EAAE;MACjB,IAAIO,GAAG,CAACnG,MAAM,EAAE,EAAEoG,IAAI;MACtB,IAAID,GAAG,CAACvE,aAAa,EAAE,EAAEyE,KAAK;IAChC;IACA,IAAIJ,IAAI,KAAKG,IAAI,EAAE;MACjB,OAAOH,IAAI,GAAGG,IAAI;IACpB;;IAEA;IACA,IAAIL,QAAQ,IAAIF,KAAK,CAACjE,aAAa,EAAE;MACnC,IAAI,CAACoE,QAAQ,IAAI,CAACF,KAAK,CAAClE,aAAa,EAAE;QACrC,OAAO,CAAC;MACV;IACF,CAAC,MAAM,IAAIoE,QAAQ,IAAIF,KAAK,CAAClE,aAAa,EAAE;MAC1C,OAAO,CAAC,CAAC;IACX;;IAEA;IACA,IAAIsE,KAAK,KAAKG,KAAK,EAAE;MACnB,OAAOH,KAAK,GAAGG,KAAK;IACtB;;IAEA;IACA,IAAIN,QAAQ,EAAE;MACZ,IAAI,CAACC,QAAQ,EAAE;QACb,OAAO,CAAC;MACV;IACF,CAAC,MAAM,IAAIA,QAAQ,EAAE;MACnB,OAAO,CAAC,CAAC;IACX;;IAEA;IACA,MAAMM,eAAe,GACnB,CAACX,KAAK,CAACvI,MAAM,GAAGwI,KAAK,CAACxI,MAAM,KAAK2I,QAAQ,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;IACrD,IAAIO,eAAe,KAAK,CAAC,EAAE;MACzB,OAAOA,eAAe;IACxB;;IAEA;IACA;IACA;IACA;IACA,MAAMC,WAAW,GAAG,EAAE;IACtB,IAAIC,EAAE,GAAG,CAAC;IACV,KAAK,IAAIhJ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGmI,KAAK,CAACvI,MAAM,EAAE,EAAEI,CAAC,EAAE;MACrC,MAAMiJ,cAAc,GAAGtB,aAAa,CAACQ,KAAK,CAACnI,CAAC,CAAC,EAAEoI,KAAK,CAACpI,CAAC,CAAC,CAAC;MACxD+I,WAAW,CAAC7I,IAAI,CAAC+I,cAAc,CAAC;MAChCD,EAAE,IAAIC,cAAc;IACtB;IACA,IAAID,EAAE,KAAK,CAAC,EAAE;MACZ,OAAOA,EAAE;IACX;;IAEA;IACA;IACA;IACA;IACA,IAAIE,CAAC;IACL,KAAKA,CAAC,IAAIH,WAAW,EAAE;MACrB,IAAIG,CAAC,KAAK,CAAC,EAAE;QACX,OAAOA,CAAC;MACV;IACF;;IAEA;IACA,OAAO,CAAC;EACV;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASzE,oBAAoBA,CAAEH,SAAS,EAAE;IACxC,IAAIA,SAAS,CAAC1E,MAAM,KAAK,CAAC,EAAE;MAC1B,OAAO,EAAE;IACX;IACA,MAAMH,KAAK,GAAG6E,SAAS,CAAChB,GAAG,CAACtE,QAAQ,CAAC;IACrC,IAAIsF,SAAS,CAAC1E,MAAM,GAAG,CAAC,EAAE;MACxBH,KAAK,CAAC0J,IAAI,CAAC,CAACC,EAAE,EAAEC,EAAE,KAAKD,EAAE,CAACtJ,KAAK,GAAGuJ,EAAE,CAACvJ,KAAK,CAAC;IAC7C;IACA,IAAIe,OAAO,GAAGpB,KAAK,CAAC,CAAC,CAAC,CAACW,aAAa;IACpC,IAAIkE,SAAS,CAAC1E,MAAM,KAAK,CAAC,EAAE;MAC1B,OAAOiB,OAAO;IAChB;IAEAA,OAAO,GAAGA,OAAO,CAACN,MAAM,CAAC,EAAE,CAAC,EAAC;IAC7B;IACA;IACA,MAAM+I,UAAU,GAAG,IAAIzE,GAAG,CAACP,SAAS,CAAC;IACrC,KAAK,IAAItE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGP,KAAK,CAACG,MAAM,EAAE,EAAEI,CAAC,EAAE;MACrC,IAAIuJ,QAAQ;MACZ,KAAKA,QAAQ,IAAI9J,KAAK,CAACO,CAAC,CAAC,CAACI,aAAa,EAAE;QACvC,IAAI,CAACkJ,UAAU,CAACrJ,GAAG,CAACsJ,QAAQ,CAACnG,IAAI,CAAC,EAAE;UAClCvC,OAAO,CAACX,IAAI,CAACqJ,QAAQ,CAAC;UACtBD,UAAU,CAACvE,GAAG,CAACwE,QAAQ,CAACnG,IAAI,CAAC;QAC/B;MACF;IACF;IAEA,OAAOvC,OAAO;EAChB;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAAS2I,wBAAwBA,CAAEhI,MAAM,EAAEN,EAAE,EAAE;IAC7C,IAAIuI,SAAS,GAAGvI,EAAE;;IAElB;;IAEA,IAAIM,MAAM,CAACmB,IAAI,CAACY,CAAC,IAAIA,CAAC,CAACa,aAAa,CAAC,EAAE;MACrC,MAAM7B,SAAS,GAAG6C,YAAY,CAAC5D,MAAM,CAAC;MACtC,MAAMkI,mBAAmB,GAAGlI,MAAM,CAAC8B,GAAG,CAACqG,oBAAoB,CAAC;MAE5DF,SAAS,GAAG,SAASG,WAAWA,CAAA,EAAI;QAClC,MAAM5D,IAAI,GAAG,EAAE;QACf,MAAMX,IAAI,GAAG9C,SAAS,GAAG5C,SAAS,CAACC,MAAM,GAAG,CAAC,GAAGD,SAAS,CAACC,MAAM;QAChE,KAAK,IAAII,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGqF,IAAI,EAAErF,CAAC,EAAE,EAAE;UAC7BgG,IAAI,CAAChG,CAAC,CAAC,GAAG0J,mBAAmB,CAAC1J,CAAC,CAAC,CAACL,SAAS,CAACK,CAAC,CAAC,CAAC;QAChD;QACA,IAAIuC,SAAS,EAAE;UACbyD,IAAI,CAACX,IAAI,CAAC,GAAG1F,SAAS,CAAC0F,IAAI,CAAC,CAAC/B,GAAG,CAACoG,mBAAmB,CAACrE,IAAI,CAAC,CAAC;QAC7D;QAEA,OAAOnE,EAAE,CAACkG,KAAK,CAAC,IAAI,EAAEpB,IAAI,CAAC;MAC7B,CAAC;IACH;IAEA,IAAI6D,YAAY,GAAGJ,SAAS;IAC5B,IAAIrE,YAAY,CAAC5D,MAAM,CAAC,EAAE;MACxB,MAAMsI,MAAM,GAAGtI,MAAM,CAAC5B,MAAM,GAAG,CAAC;MAEhCiK,YAAY,GAAG,SAASE,oBAAoBA,CAAA,EAAI;QAC9C,OAAON,SAAS,CAACrC,KAAK,CAAC,IAAI,EACzB9G,KAAK,CAACX,SAAS,EAAE,CAAC,EAAEmK,MAAM,CAAC,CAACvJ,MAAM,CAAC,CAACD,KAAK,CAACX,SAAS,EAAEmK,MAAM,CAAC,CAAC,CAAC,CAAC;MACnE,CAAC;IACH;IAEA,OAAOD,YAAY;EACrB;;EAEA;AACF;AACA;AACA;AACA;AACA;EACE,SAASF,oBAAoBA,CAAElG,KAAK,EAAE;IACpC,IAAI8B,KAAK,EAAEC,KAAK,EAAEwE,WAAW,EAAEC,WAAW;IAC1C,MAAMvE,KAAK,GAAG,EAAE;IAChB,MAAMzC,WAAW,GAAG,EAAE;IAEtBQ,KAAK,CAAChE,KAAK,CAACqF,OAAO,CAAC,UAAU5F,IAAI,EAAE;MAClC,IAAIA,IAAI,CAACgF,UAAU,EAAE;QACnBwB,KAAK,CAACxF,IAAI,CAAClB,QAAQ,CAACE,IAAI,CAACgF,UAAU,CAACd,IAAI,CAAC,CAAChF,IAAI,CAAC;QAC/C6E,WAAW,CAAC/C,IAAI,CAAChB,IAAI,CAACgF,UAAU,CAAClB,OAAO,CAAC;MAC3C;IACF,CAAC,CAAC;;IAEF;IACA,QAAQC,WAAW,CAACrD,MAAM;MACxB,KAAK,CAAC;QACJ,OAAO,SAASsK,UAAUA,CAAEC,GAAG,EAAE;UAC/B,OAAOA,GAAG;QACZ,CAAC;MAEH,KAAK,CAAC;QACJ5E,KAAK,GAAGG,KAAK,CAAC,CAAC,CAAC;QAChBsE,WAAW,GAAG/G,WAAW,CAAC,CAAC,CAAC;QAC5B,OAAO,SAASiH,UAAUA,CAAEC,GAAG,EAAE;UAC/B,IAAI5E,KAAK,CAAC4E,GAAG,CAAC,EAAE;YACd,OAAOH,WAAW,CAACG,GAAG,CAAC;UACzB;UACA,OAAOA,GAAG;QACZ,CAAC;MAEH,KAAK,CAAC;QACJ5E,KAAK,GAAGG,KAAK,CAAC,CAAC,CAAC;QAChBF,KAAK,GAAGE,KAAK,CAAC,CAAC,CAAC;QAChBsE,WAAW,GAAG/G,WAAW,CAAC,CAAC,CAAC;QAC5BgH,WAAW,GAAGhH,WAAW,CAAC,CAAC,CAAC;QAC5B,OAAO,SAASiH,UAAUA,CAAEC,GAAG,EAAE;UAC/B,IAAI5E,KAAK,CAAC4E,GAAG,CAAC,EAAE;YACd,OAAOH,WAAW,CAACG,GAAG,CAAC;UACzB;UACA,IAAI3E,KAAK,CAAC2E,GAAG,CAAC,EAAE;YACd,OAAOF,WAAW,CAACE,GAAG,CAAC;UACzB;UACA,OAAOA,GAAG;QACZ,CAAC;MAEH;QACE,OAAO,SAASD,UAAUA,CAAEC,GAAG,EAAE;UAC/B,KAAK,IAAInK,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGiD,WAAW,CAACrD,MAAM,EAAEI,CAAC,EAAE,EAAE;YAC3C,IAAI0F,KAAK,CAAC1F,CAAC,CAAC,CAACmK,GAAG,CAAC,EAAE;cACjB,OAAOlH,WAAW,CAACjD,CAAC,CAAC,CAACmK,GAAG,CAAC;YAC5B;UACF;UACA,OAAOA,GAAG;QACZ,CAAC;IACL;EACF;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASC,WAAWA,CAAE5I,MAAM,EAAE;IAC5B,SAAS6I,YAAYA,CAAE7I,MAAM,EAAE1B,KAAK,EAAEwK,WAAW,EAAE;MACjD,IAAIxK,KAAK,GAAG0B,MAAM,CAAC5B,MAAM,EAAE;QACzB,MAAM6D,KAAK,GAAGjC,MAAM,CAAC1B,KAAK,CAAC;QAC3B,IAAIyK,eAAe,GAAG,EAAE;QAExB,IAAI9G,KAAK,CAAClB,SAAS,EAAE;UACnB;UACA;UACA,MAAMyB,UAAU,GAAGP,KAAK,CAAChE,KAAK,CAACqB,MAAM,CAACqF,WAAW,CAAC;UAClD,IAAInC,UAAU,CAACpE,MAAM,GAAG6D,KAAK,CAAChE,KAAK,CAACG,MAAM,EAAE;YAC1C2K,eAAe,CAACrK,IAAI,CAAC;cACnBT,KAAK,EAAEuE,UAAU;cACjB7F,IAAI,EAAE,KAAK,GAAG6F,UAAU,CAACV,GAAG,CAACiB,CAAC,IAAIA,CAAC,CAACpG,IAAI,CAAC,CAACoD,IAAI,CAAC,GAAG,CAAC;cACnDiB,MAAM,EAAEwB,UAAU,CAACrB,IAAI,CAAC4B,CAAC,IAAIA,CAAC,CAAC7F,KAAK,CAAC;cACrC0F,aAAa,EAAE,KAAK;cACpB7B,SAAS,EAAE;YACb,CAAC,CAAC;UACJ;UACAgI,eAAe,CAACrK,IAAI,CAACuD,KAAK,CAAC;QAC7B,CAAC,MAAM;UACL;UACA8G,eAAe,GAAG9G,KAAK,CAAChE,KAAK,CAAC6D,GAAG,CAAC,UAAUpE,IAAI,EAAE;YAChD,OAAO;cACLO,KAAK,EAAE,CAACP,IAAI,CAAC;cACbf,IAAI,EAAEe,IAAI,CAACf,IAAI;cACfqE,MAAM,EAAEtD,IAAI,CAACR,KAAK;cAClB0F,aAAa,EAAElF,IAAI,CAACgF,UAAU;cAC9B3B,SAAS,EAAE;YACb,CAAC;UACH,CAAC,CAAC;QACJ;;QAEA;QACA,OAAOiI,OAAO,CAACD,eAAe,EAAE,UAAUE,SAAS,EAAE;UACnD,OAAOJ,YAAY,CAAC7I,MAAM,EAAE1B,KAAK,GAAG,CAAC,EAAEwK,WAAW,CAAC/J,MAAM,CAAC,CAACkK,SAAS,CAAC,CAAC,CAAC;QACzE,CAAC,CAAC;MACJ,CAAC,MAAM;QACL;QACA,OAAO,CAACH,WAAW,CAAC;MACtB;IACF;IAEA,OAAOD,YAAY,CAAC7I,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;EACpC;;EAEA;AACF;AACA;AACA;AACA;AACA;EACE,SAASkJ,WAAWA,CAAEC,OAAO,EAAEC,OAAO,EAAE;IACtC,MAAMC,EAAE,GAAG3D,IAAI,CAACI,GAAG,CAACqD,OAAO,CAAC/K,MAAM,EAAEgL,OAAO,CAAChL,MAAM,CAAC;IAEnD,KAAK,IAAII,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG6K,EAAE,EAAE7K,CAAC,EAAE,EAAE;MAC3B,MAAM8K,QAAQ,GAAG5E,iBAAiB,CAACyE,OAAO,EAAE3K,CAAC,CAAC;MAC9C,MAAM+K,QAAQ,GAAG7E,iBAAiB,CAAC0E,OAAO,EAAE5K,CAAC,CAAC;MAC9C,IAAIgL,OAAO,GAAG,KAAK;MACnB,IAAI7M,IAAI;MACR,KAAKA,IAAI,IAAI4M,QAAQ,EAAE;QACrB,IAAID,QAAQ,CAAC7K,GAAG,CAAC9B,IAAI,CAAC,EAAE;UACtB6M,OAAO,GAAG,IAAI;UACd;QACF;MACF;MACA,IAAI,CAACA,OAAO,EAAE;QACZ,OAAO,KAAK;MACd;IACF;IAEA,MAAMC,IAAI,GAAGN,OAAO,CAAC/K,MAAM;IAC3B,MAAMsL,IAAI,GAAGN,OAAO,CAAChL,MAAM;IAC3B,MAAMuL,UAAU,GAAG/F,YAAY,CAACuF,OAAO,CAAC;IACxC,MAAMS,UAAU,GAAGhG,YAAY,CAACwF,OAAO,CAAC;IAExC,OAAOO,UAAU,GACbC,UAAU,GAAIH,IAAI,KAAKC,IAAI,GAAKA,IAAI,IAAID,IAAK,GAC7CG,UAAU,GAAIH,IAAI,IAAIC,IAAI,GAAKD,IAAI,KAAKC,IAAK;EACnD;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASG,gBAAgBA,CAAEC,YAAY,EAAE;IACvC,OAAOA,YAAY,CAAChI,GAAG,CAACpC,EAAE,IAAI;MAC5B,IAAIqK,aAAa,CAACrK,EAAE,CAAC,EAAE;QACrB,OAAOsK,WAAW,CAACtK,EAAE,CAACsK,WAAW,CAACC,QAAQ,CAAC;MAC7C;MACA,IAAIC,SAAS,CAACxK,EAAE,CAAC,EAAE;QACjB,OAAOyK,WAAW,CAACzK,EAAE,CAAC0K,OAAO,CAACC,UAAU,EAAE3K,EAAE,CAAC0K,OAAO,CAACH,QAAQ,CAAC;MAChE;MACA,OAAOvK,EAAE;IACX,CAAC,CAAC;EACJ;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAAS4K,kBAAkBA,CAAED,UAAU,EAAEP,YAAY,EAAEvJ,YAAY,EAAE;IACnE,MAAMgK,kBAAkB,GAAG,EAAE;IAC7B,IAAIC,SAAS;IACb,KAAKA,SAAS,IAAIH,UAAU,EAAE;MAC5B,IAAII,UAAU,GAAGlK,YAAY,CAACiK,SAAS,CAAC;MACxC,IAAI,OAAOC,UAAU,KAAK,QAAQ,EAAE;QAClC,MAAM,IAAI1M,SAAS,CACjB,0CAA0C,GAAGyM,SAAS,GAAG,GAAG,CAAC;MACjE;MACAC,UAAU,GAAGX,YAAY,CAACW,UAAU,CAAC;MACrC,IAAI,OAAOA,UAAU,KAAK,UAAU,EAAE;QACpC,OAAO,KAAK;MACd;MACAF,kBAAkB,CAAC7L,IAAI,CAAC+L,UAAU,CAAC;IACrC;IACA,OAAOF,kBAAkB;EAC3B;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASG,iBAAiBA,CAAEZ,YAAY,EAAEvJ,YAAY,EAAEoK,IAAI,EAAE;IAC5D,MAAMC,iBAAiB,GAAGf,gBAAgB,CAACC,YAAY,CAAC;IACxD,MAAMe,UAAU,GAAG,IAAIhO,KAAK,CAAC+N,iBAAiB,CAACxM,MAAM,CAAC,CAAC0M,IAAI,CAAC,KAAK,CAAC;IAClE,IAAIC,cAAc,GAAG,IAAI;IACzB,OAAOA,cAAc,EAAE;MACrBA,cAAc,GAAG,KAAK;MACtB,IAAIC,eAAe,GAAG,IAAI;MAC1B,KAAK,IAAIxM,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGoM,iBAAiB,CAACxM,MAAM,EAAE,EAAEI,CAAC,EAAE;QACjD,IAAIqM,UAAU,CAACrM,CAAC,CAAC,EAAE;QACnB,MAAMkB,EAAE,GAAGkL,iBAAiB,CAACpM,CAAC,CAAC;QAE/B,IAAIuL,aAAa,CAACrK,EAAE,CAAC,EAAE;UACrBkL,iBAAiB,CAACpM,CAAC,CAAC,GAAGkB,EAAE,CAACsK,WAAW,CAACC,QAAQ,CAACU,IAAI,CAAC;UACpD;UACAC,iBAAiB,CAACpM,CAAC,CAAC,CAACwL,WAAW,GAAGtK,EAAE,CAACsK,WAAW;UACjDa,UAAU,CAACrM,CAAC,CAAC,GAAG,IAAI;UACpBwM,eAAe,GAAG,KAAK;QACzB,CAAC,MAAM,IAAId,SAAS,CAACxK,EAAE,CAAC,EAAE;UACxB,MAAM6K,kBAAkB,GAAGD,kBAAkB,CAC3C5K,EAAE,CAAC0K,OAAO,CAACC,UAAU,EAAEO,iBAAiB,EAAErK,YAAY,CAAC;UACzD,IAAIgK,kBAAkB,EAAE;YACtBK,iBAAiB,CAACpM,CAAC,CAAC,GAClBkB,EAAE,CAAC0K,OAAO,CAACH,QAAQ,CAACrE,KAAK,CAAC,IAAI,EAAE2E,kBAAkB,CAAC;YACrD;YACAK,iBAAiB,CAACpM,CAAC,CAAC,CAAC4L,OAAO,GAAG1K,EAAE,CAAC0K,OAAO;YACzCS,UAAU,CAACrM,CAAC,CAAC,GAAG,IAAI;YACpBwM,eAAe,GAAG,KAAK;UACzB,CAAC,MAAM;YACLD,cAAc,GAAG,IAAI;UACvB;QACF;MACF;MAEA,IAAIC,eAAe,IAAID,cAAc,EAAE;QACrC,MAAM,IAAIpH,WAAW,CACnB,wDAAwD,CAAC;MAC7D;IACF;IAEA,OAAOiH,iBAAiB;EAC1B;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;EACE,SAASK,sBAAsBA,CAAEC,aAAa,EAAE;IAC9C;;IAEA;IACA,MAAMC,mBAAmB,GAAG,2BAA2B;IAEvD1O,MAAM,CAAC2O,IAAI,CAACF,aAAa,CAAC,CAAC5H,OAAO,CAAC3D,SAAS,IAAI;MAC9C,MAAMD,EAAE,GAAGwL,aAAa,CAACvL,SAAS,CAAC;MAEnC,IAAIwL,mBAAmB,CAACvO,IAAI,CAAC8C,EAAE,CAAC2L,QAAQ,CAAC,CAAC,CAAC,EAAE;QAC3C,MAAM,IAAI1H,WAAW,CAAC,4CAA4C,GAChE,wCAAwC,GACxC,kDAAkD,CAAC;MACvD;IACF,CAAC,CAAC;EACJ;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAAS2H,mBAAmBA,CAAE3O,IAAI,EAAE4O,gBAAgB,EAAE;IACpDjO,KAAK,CAACC,WAAW,EAAE;IAEnB,IAAId,MAAM,CAAC2O,IAAI,CAACG,gBAAgB,CAAC,CAACnN,MAAM,KAAK,CAAC,EAAE;MAC9C,MAAM,IAAIuF,WAAW,CAAC,wBAAwB,CAAC;IACjD;IAEA,IAAIrG,KAAK,CAACkO,yBAAyB,EAAE;MACnCP,sBAAsB,CAACM,gBAAgB,CAAC;IAC1C;;IAEA;IACA,MAAME,YAAY,GAAG,EAAE;IACvB,MAAMC,iBAAiB,GAAG,EAAE;IAC5B,MAAMR,aAAa,GAAG,CAAC,CAAC;IACxB,MAAMS,qBAAqB,GAAG,EAAE,EAAC;IACjC,IAAIhM,SAAS;IACb,KAAKA,SAAS,IAAI4L,gBAAgB,EAAE;MAClC;MACA,IAAI,CAAC9O,MAAM,CAACmP,SAAS,CAACC,cAAc,CAACC,IAAI,CAACP,gBAAgB,EAAE5L,SAAS,CAAC,EAAE;QACtE;MACF;MACA;MACA,MAAMK,MAAM,GAAGC,cAAc,CAACN,SAAS,CAAC;MACxC,IAAI,CAACK,MAAM,EAAE;MACb;MACAyL,YAAY,CAACnI,OAAO,CAAC,UAAUyI,EAAE,EAAE;QACjC,IAAI7C,WAAW,CAAC6C,EAAE,EAAE/L,MAAM,CAAC,EAAE;UAC3B,MAAM,IAAIjC,SAAS,CAAC,0BAA0B,GAC5CoC,eAAe,CAAC4L,EAAE,CAAC,GAAG,SAAS,GAC/B5L,eAAe,CAACH,MAAM,CAAC,GAAG,IAAI,CAAC;QACnC;MACF,CAAC,CAAC;MACFyL,YAAY,CAAC/M,IAAI,CAACsB,MAAM,CAAC;MACzB;MACA,MAAMgM,aAAa,GAAGN,iBAAiB,CAACtN,MAAM;MAC9CsN,iBAAiB,CAAChN,IAAI,CAAC6M,gBAAgB,CAAC5L,SAAS,CAAC,CAAC;MACnD,MAAMsM,gBAAgB,GAAGjM,MAAM,CAAC8B,GAAG,CAACe,WAAW,CAAC;MAChD;MACA,IAAIqJ,EAAE;MACN,KAAKA,EAAE,IAAItD,WAAW,CAACqD,gBAAgB,CAAC,EAAE;QACxC,MAAME,MAAM,GAAGhM,eAAe,CAAC+L,EAAE,CAAC;QAClCP,qBAAqB,CAACjN,IAAI,CACxB;UAAEsB,MAAM,EAAEkM,EAAE;UAAEvP,IAAI,EAAEwP,MAAM;UAAEzM,EAAE,EAAEsM;QAAc,CAAC,CAAC;QAClD,IAAIE,EAAE,CAACE,KAAK,CAACrK,CAAC,IAAI,CAACA,CAAC,CAACa,aAAa,CAAC,EAAE;UACnCsI,aAAa,CAACiB,MAAM,CAAC,GAAGH,aAAa;QACvC;MACF;IACF;IAEAL,qBAAqB,CAAChE,IAAI,CAACnB,iBAAiB,CAAC;;IAE7C;IACA,MAAMoE,iBAAiB,GACrBF,iBAAiB,CAACgB,iBAAiB,EAAER,aAAa,EAAEmB,UAAU,CAAC;;IAEjE;IACA,IAAIhK,CAAC;IACL,KAAKA,CAAC,IAAI6I,aAAa,EAAE;MACvB,IAAIzO,MAAM,CAACmP,SAAS,CAACC,cAAc,CAACC,IAAI,CAACZ,aAAa,EAAE7I,CAAC,CAAC,EAAE;QAC1D6I,aAAa,CAAC7I,CAAC,CAAC,GAAGuI,iBAAiB,CAACM,aAAa,CAAC7I,CAAC,CAAC,CAAC;MACxD;IACF;IACA,MAAMjC,UAAU,GAAG,EAAE;IACrB,MAAMkM,oBAAoB,GAAG,IAAIrN,GAAG,CAAC,CAAC,EAAC;IACvC,KAAKoD,CAAC,IAAIsJ,qBAAqB,EAAE;MAC/B;MACA;MACA;MACA,IAAI,CAACW,oBAAoB,CAAC7N,GAAG,CAAC4D,CAAC,CAAC1F,IAAI,CAAC,EAAE;QACrC0F,CAAC,CAAC3C,EAAE,GAAGkL,iBAAiB,CAACvI,CAAC,CAAC3C,EAAE,CAAC;QAC9BU,UAAU,CAAC1B,IAAI,CAAC2D,CAAC,CAAC;QAClBiK,oBAAoB,CAAC3N,GAAG,CAAC0D,CAAC,CAAC1F,IAAI,EAAE0F,CAAC,CAAC;MACrC;IACF;;IAEA;IACA,MAAMkK,GAAG,GAAGnM,UAAU,CAAC,CAAC,CAAC,IAAIA,UAAU,CAAC,CAAC,CAAC,CAACJ,MAAM,CAAC5B,MAAM,IAAI,CAAC,IAAI,CAACwF,YAAY,CAACxD,UAAU,CAAC,CAAC,CAAC,CAACJ,MAAM,CAAC;IACpG,MAAMwM,GAAG,GAAGpM,UAAU,CAAC,CAAC,CAAC,IAAIA,UAAU,CAAC,CAAC,CAAC,CAACJ,MAAM,CAAC5B,MAAM,IAAI,CAAC,IAAI,CAACwF,YAAY,CAACxD,UAAU,CAAC,CAAC,CAAC,CAACJ,MAAM,CAAC;IACpG,MAAMyM,GAAG,GAAGrM,UAAU,CAAC,CAAC,CAAC,IAAIA,UAAU,CAAC,CAAC,CAAC,CAACJ,MAAM,CAAC5B,MAAM,IAAI,CAAC,IAAI,CAACwF,YAAY,CAACxD,UAAU,CAAC,CAAC,CAAC,CAACJ,MAAM,CAAC;IACpG,MAAM0M,GAAG,GAAGtM,UAAU,CAAC,CAAC,CAAC,IAAIA,UAAU,CAAC,CAAC,CAAC,CAACJ,MAAM,CAAC5B,MAAM,IAAI,CAAC,IAAI,CAACwF,YAAY,CAACxD,UAAU,CAAC,CAAC,CAAC,CAACJ,MAAM,CAAC;IACpG,MAAM2M,GAAG,GAAGvM,UAAU,CAAC,CAAC,CAAC,IAAIA,UAAU,CAAC,CAAC,CAAC,CAACJ,MAAM,CAAC5B,MAAM,IAAI,CAAC,IAAI,CAACwF,YAAY,CAACxD,UAAU,CAAC,CAAC,CAAC,CAACJ,MAAM,CAAC;IACpG,MAAM4M,GAAG,GAAGxM,UAAU,CAAC,CAAC,CAAC,IAAIA,UAAU,CAAC,CAAC,CAAC,CAACJ,MAAM,CAAC5B,MAAM,IAAI,CAAC,IAAI,CAACwF,YAAY,CAACxD,UAAU,CAAC,CAAC,CAAC,CAACJ,MAAM,CAAC;IACpG,MAAM6M,KAAK,GAAGN,GAAG,IAAIC,GAAG,IAAIC,GAAG,IAAIC,GAAG,IAAIC,GAAG,IAAIC,GAAG;;IAEpD;IACA,KAAK,IAAIpO,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG4B,UAAU,CAAChC,MAAM,EAAE,EAAEI,CAAC,EAAE;MAC1C4B,UAAU,CAAC5B,CAAC,CAAC,CAAC5B,IAAI,GAAGuH,YAAY,CAAC/D,UAAU,CAAC5B,CAAC,CAAC,CAACwB,MAAM,CAAC;IACzD;IAEA,MAAM8M,MAAM,GAAGP,GAAG,GAAGzI,WAAW,CAAC1D,UAAU,CAAC,CAAC,CAAC,CAACJ,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG/D,KAAK;IACjE,MAAM8Q,MAAM,GAAGP,GAAG,GAAG1I,WAAW,CAAC1D,UAAU,CAAC,CAAC,CAAC,CAACJ,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG/D,KAAK;IACjE,MAAM+Q,MAAM,GAAGP,GAAG,GAAG3I,WAAW,CAAC1D,UAAU,CAAC,CAAC,CAAC,CAACJ,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG/D,KAAK;IACjE,MAAMgR,MAAM,GAAGP,GAAG,GAAG5I,WAAW,CAAC1D,UAAU,CAAC,CAAC,CAAC,CAACJ,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG/D,KAAK;IACjE,MAAMiR,MAAM,GAAGP,GAAG,GAAG7I,WAAW,CAAC1D,UAAU,CAAC,CAAC,CAAC,CAACJ,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG/D,KAAK;IACjE,MAAMkR,MAAM,GAAGP,GAAG,GAAG9I,WAAW,CAAC1D,UAAU,CAAC,CAAC,CAAC,CAACJ,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG/D,KAAK;IAEjE,MAAMmR,MAAM,GAAGb,GAAG,GAAGzI,WAAW,CAAC1D,UAAU,CAAC,CAAC,CAAC,CAACJ,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG/D,KAAK;IACjE,MAAMoR,MAAM,GAAGb,GAAG,GAAG1I,WAAW,CAAC1D,UAAU,CAAC,CAAC,CAAC,CAACJ,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG/D,KAAK;IACjE,MAAMqR,MAAM,GAAGb,GAAG,GAAG3I,WAAW,CAAC1D,UAAU,CAAC,CAAC,CAAC,CAACJ,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG/D,KAAK;IACjE,MAAMsR,MAAM,GAAGb,GAAG,GAAG5I,WAAW,CAAC1D,UAAU,CAAC,CAAC,CAAC,CAACJ,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG/D,KAAK;IACjE,MAAMuR,MAAM,GAAGb,GAAG,GAAG7I,WAAW,CAAC1D,UAAU,CAAC,CAAC,CAAC,CAACJ,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG/D,KAAK;IACjE,MAAMwR,MAAM,GAAGb,GAAG,GAAG9I,WAAW,CAAC1D,UAAU,CAAC,CAAC,CAAC,CAACJ,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG/D,KAAK;;IAEjE;IACA,KAAK,IAAIuC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG4B,UAAU,CAAChC,MAAM,EAAE,EAAEI,CAAC,EAAE;MAC1C4B,UAAU,CAAC5B,CAAC,CAAC,CAAC+C,cAAc,GAC1ByG,wBAAwB,CAAC5H,UAAU,CAAC5B,CAAC,CAAC,CAACwB,MAAM,EAAEI,UAAU,CAAC5B,CAAC,CAAC,CAACkB,EAAE,CAAC;IACpE;IAEA,MAAMgO,GAAG,GAAGnB,GAAG,GAAGnM,UAAU,CAAC,CAAC,CAAC,CAACmB,cAAc,GAAGrF,KAAK;IACtD,MAAMyR,GAAG,GAAGnB,GAAG,GAAGpM,UAAU,CAAC,CAAC,CAAC,CAACmB,cAAc,GAAGrF,KAAK;IACtD,MAAM0R,GAAG,GAAGnB,GAAG,GAAGrM,UAAU,CAAC,CAAC,CAAC,CAACmB,cAAc,GAAGrF,KAAK;IACtD,MAAM2R,GAAG,GAAGnB,GAAG,GAAGtM,UAAU,CAAC,CAAC,CAAC,CAACmB,cAAc,GAAGrF,KAAK;IACtD,MAAM4R,GAAG,GAAGnB,GAAG,GAAGvM,UAAU,CAAC,CAAC,CAAC,CAACmB,cAAc,GAAGrF,KAAK;IACtD,MAAM6R,GAAG,GAAGnB,GAAG,GAAGxM,UAAU,CAAC,CAAC,CAAC,CAACmB,cAAc,GAAGrF,KAAK;IAEtD,MAAM8R,IAAI,GAAGzB,GAAG,GAAGnM,UAAU,CAAC,CAAC,CAAC,CAACJ,MAAM,CAAC5B,MAAM,GAAG,CAAC,CAAC;IACnD,MAAMqL,IAAI,GAAG+C,GAAG,GAAGpM,UAAU,CAAC,CAAC,CAAC,CAACJ,MAAM,CAAC5B,MAAM,GAAG,CAAC,CAAC;IACnD,MAAMsL,IAAI,GAAG+C,GAAG,GAAGrM,UAAU,CAAC,CAAC,CAAC,CAACJ,MAAM,CAAC5B,MAAM,GAAG,CAAC,CAAC;IACnD,MAAM6P,IAAI,GAAGvB,GAAG,GAAGtM,UAAU,CAAC,CAAC,CAAC,CAACJ,MAAM,CAAC5B,MAAM,GAAG,CAAC,CAAC;IACnD,MAAM8P,IAAI,GAAGvB,GAAG,GAAGvM,UAAU,CAAC,CAAC,CAAC,CAACJ,MAAM,CAAC5B,MAAM,GAAG,CAAC,CAAC;IACnD,MAAM+P,IAAI,GAAGvB,GAAG,GAAGxM,UAAU,CAAC,CAAC,CAAC,CAACJ,MAAM,CAAC5B,MAAM,GAAG,CAAC,CAAC;;IAEnD;IACA,MAAMgQ,MAAM,GAAGvB,KAAK,GAAG,CAAC,GAAG,CAAC;IAC5B,MAAMwB,IAAI,GAAGjO,UAAU,CAAChC,MAAM;IAC9B;IACA,MAAM8F,KAAK,GAAG9D,UAAU,CAAC0B,GAAG,CAACO,CAAC,IAAIA,CAAC,CAACzF,IAAI,CAAC;IACzC,MAAM0R,GAAG,GAAGlO,UAAU,CAAC0B,GAAG,CAACO,CAAC,IAAIA,CAAC,CAACd,cAAc,CAAC;IACjD,MAAMgN,OAAO,GAAG,SAASA,OAAOA,CAAA,EAAI;MAClC,YAAY;;MAEZ,KAAK,IAAI/P,CAAC,GAAG4P,MAAM,EAAE5P,CAAC,GAAG6P,IAAI,EAAE7P,CAAC,EAAE,EAAE;QAClC,IAAI0F,KAAK,CAAC1F,CAAC,CAAC,CAACL,SAAS,CAAC,EAAE;UACvB,OAAOmQ,GAAG,CAAC9P,CAAC,CAAC,CAACoH,KAAK,CAAC,IAAI,EAAEzH,SAAS,CAAC;QACtC;MACF;MAEA,OAAOb,KAAK,CAACkR,UAAU,CAAC7R,IAAI,EAAEwB,SAAS,EAAEiC,UAAU,CAAC;IACtD,CAAC;;IAED;IACA;IACA,SAASiM,UAAUA,CAAEoC,IAAI,EAAEC,IAAI,EAAE;MAC/B,YAAY;;MAEZ,IAAIvQ,SAAS,CAACC,MAAM,KAAK4P,IAAI,IAAIlB,MAAM,CAAC2B,IAAI,CAAC,IAAIrB,MAAM,CAACsB,IAAI,CAAC,EAAE;QAAE,OAAOhB,GAAG,CAAC9H,KAAK,CAAC,IAAI,EAAEzH,SAAS,CAAC;MAAC;MACnG,IAAIA,SAAS,CAACC,MAAM,KAAKqL,IAAI,IAAIsD,MAAM,CAAC0B,IAAI,CAAC,IAAIpB,MAAM,CAACqB,IAAI,CAAC,EAAE;QAAE,OAAOf,GAAG,CAAC/H,KAAK,CAAC,IAAI,EAAEzH,SAAS,CAAC;MAAC;MACnG,IAAIA,SAAS,CAACC,MAAM,KAAKsL,IAAI,IAAIsD,MAAM,CAACyB,IAAI,CAAC,IAAInB,MAAM,CAACoB,IAAI,CAAC,EAAE;QAAE,OAAOd,GAAG,CAAChI,KAAK,CAAC,IAAI,EAAEzH,SAAS,CAAC;MAAC;MACnG,IAAIA,SAAS,CAACC,MAAM,KAAK6P,IAAI,IAAIhB,MAAM,CAACwB,IAAI,CAAC,IAAIlB,MAAM,CAACmB,IAAI,CAAC,EAAE;QAAE,OAAOb,GAAG,CAACjI,KAAK,CAAC,IAAI,EAAEzH,SAAS,CAAC;MAAC;MACnG,IAAIA,SAAS,CAACC,MAAM,KAAK8P,IAAI,IAAIhB,MAAM,CAACuB,IAAI,CAAC,IAAIjB,MAAM,CAACkB,IAAI,CAAC,EAAE;QAAE,OAAOZ,GAAG,CAAClI,KAAK,CAAC,IAAI,EAAEzH,SAAS,CAAC;MAAC;MACnG,IAAIA,SAAS,CAACC,MAAM,KAAK+P,IAAI,IAAIhB,MAAM,CAACsB,IAAI,CAAC,IAAIhB,MAAM,CAACiB,IAAI,CAAC,EAAE;QAAE,OAAOX,GAAG,CAACnI,KAAK,CAAC,IAAI,EAAEzH,SAAS,CAAC;MAAC;MAEnG,OAAOoQ,OAAO,CAAC3I,KAAK,CAAC,IAAI,EAAEzH,SAAS,CAAC;IACvC;;IAEA;IACA,IAAI;MACF1B,MAAM,CAACkS,cAAc,CAACtC,UAAU,EAAE,MAAM,EAAE;QAAEjN,KAAK,EAAEzC;MAAK,CAAC,CAAC;IAC5D,CAAC,CAAC,OAAOoI,GAAG,EAAE;MACZ;MACA;MACA;IAAA;;IAGF;IACA;IACA;IACAsH,UAAU,CAACjM,UAAU,GAAG8K,aAAa;;IAErC;IACA;IACAmB,UAAU,CAAC/L,kBAAkB,GAAG;MAC9BF,UAAU;MACVG,YAAY,EAAE+L;IAChB,CAAC;IAED,OAAOD,UAAU;EACnB;;EAEA;AACF;AACA;AACA;AACA;AACA;EACE,SAASuC,WAAWA,CAAEjS,IAAI,EAAE6H,IAAI,EAAEpE,UAAU,EAAE;IAC5C,MAAM0E,WAAW,CAACnI,IAAI,EAAE6H,IAAI,EAAEpE,UAAU,CAAC;EAC3C;;EAEA;AACF;AACA;AACA;AACA;EACE,SAASgE,OAAOA,CAAEyK,GAAG,EAAE;IACrB,OAAO/P,KAAK,CAAC+P,GAAG,EAAE,CAAC,EAAEA,GAAG,CAACzQ,MAAM,GAAG,CAAC,CAAC;EACtC;;EAEA;AACF;AACA;AACA;AACA;EACE,SAASyF,IAAIA,CAAEgL,GAAG,EAAE;IAClB,OAAOA,GAAG,CAACA,GAAG,CAACzQ,MAAM,GAAG,CAAC,CAAC;EAC5B;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;EACE,SAASU,KAAKA,CAAE+P,GAAG,EAAEC,KAAK,EAAEC,GAAG,EAAE;IAC/B,OAAOlS,KAAK,CAAC+O,SAAS,CAAC9M,KAAK,CAACgN,IAAI,CAAC+C,GAAG,EAAEC,KAAK,EAAEC,GAAG,CAAC;EACpD;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;EACE,SAASC,WAAWA,CAAEH,GAAG,EAAEjS,IAAI,EAAE;IAC/B,KAAK,IAAI4B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGqQ,GAAG,CAACzQ,MAAM,EAAEI,CAAC,EAAE,EAAE;MACnC,IAAI5B,IAAI,CAACiS,GAAG,CAACrQ,CAAC,CAAC,CAAC,EAAE;QAChB,OAAOqQ,GAAG,CAACrQ,CAAC,CAAC;MACf;IACF;IACA,OAAOrC,SAAS;EAClB;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;EACE,SAAS6M,OAAOA,CAAE6F,GAAG,EAAE5E,QAAQ,EAAE;IAC/B,OAAOpN,KAAK,CAAC+O,SAAS,CAAC7M,MAAM,CAAC6G,KAAK,CAAC,EAAE,EAAEiJ,GAAG,CAAC/M,GAAG,CAACmI,QAAQ,CAAC,CAAC;EAC5D;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASG,OAAOA,CAAA,EAAI;IAClB,MAAMC,UAAU,GACdjG,OAAO,CAACjG,SAAS,CAAC,CAAC2D,GAAG,CAACO,CAAC,IAAIlC,eAAe,CAACF,cAAc,CAACoC,CAAC,CAAC,CAAC,CAAC;IACjE,MAAM4H,QAAQ,GAAGpG,IAAI,CAAC1F,SAAS,CAAC;IAEhC,IAAI,OAAO8L,QAAQ,KAAK,UAAU,EAAE;MAClC,MAAM,IAAIlM,SAAS,CAAC,6CAA6C,CAAC;IACpE;IAEA,OAAOoM,WAAW,CAACE,UAAU,EAAEJ,QAAQ,CAAC;EAC1C;EAEA,SAASE,WAAWA,CAAEE,UAAU,EAAEJ,QAAQ,EAAE;IAC1C,OAAO;MAAEG,OAAO,EAAE;QAAEC,UAAU;QAAEJ;MAAS;IAAE,CAAC;EAC9C;;EAEA;AACF;AACA;AACA;AACA;AACA;EACE,SAASD,WAAWA,CAAEC,QAAQ,EAAE;IAC9B,IAAI,OAAOA,QAAQ,KAAK,UAAU,EAAE;MAClC,MAAM,IAAIlM,SAAS,CAAC,8CAA8C,CAAC;IACrE;IAEA,OAAO;MAAEiM,WAAW,EAAE;QAAEC;MAAS;IAAE,CAAC;EACtC;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;EACE,SAASC,SAASA,CAAE+E,UAAU,EAAE;IAC9B,OAAOA,UAAU,IACf,OAAOA,UAAU,CAAC7E,OAAO,KAAK,QAAQ,IACtCvN,KAAK,CAACC,OAAO,CAACmS,UAAU,CAAC7E,OAAO,CAACC,UAAU,CAAC,IAC5C,OAAO4E,UAAU,CAAC7E,OAAO,CAACH,QAAQ,KAAK,UAAU;EACrD;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;EACE,SAASF,aAAaA,CAAEkF,UAAU,EAAE;IAClC,OAAOA,UAAU,IACf,OAAOA,UAAU,CAACjF,WAAW,KAAK,QAAQ,IAC1C,OAAOiF,UAAU,CAACjF,WAAW,CAACC,QAAQ,KAAK,UAAU;EACzD;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASiF,SAASA,CAAEC,SAAS,EAAEjM,OAAO,EAAE;IACtC,IAAI,CAACiM,SAAS,EAAE;MACd,OAAOjM,OAAO;IAChB;IACA,IAAIA,OAAO,IAAIA,OAAO,KAAKiM,SAAS,EAAE;MACpC,MAAMpK,GAAG,GAAG,IAAIrD,KAAK,CAAC,yCAAyC,GAC7DyN,SAAS,GAAG,YAAY,GAAGjM,OAAO,GAAG,GAAG,CAAC;MAC3C6B,GAAG,CAACM,IAAI,GAAG;QAAEE,MAAM,EAAErC,OAAO;QAAE8B,QAAQ,EAAEmK;MAAU,CAAC;MACnD,MAAMpK,GAAG;IACX;IACA,OAAOoK,SAAS;EAClB;;EAEA;AACF;AACA;AACA;AACA;AACA;EACE,SAASC,aAAaA,CAAEC,GAAG,EAAE;IAC3B,IAAI1S,IAAI;IACR,KAAK,MAAM2S,GAAG,IAAID,GAAG,EAAE;MACrB;MACA;MACA,IAAI5S,MAAM,CAACmP,SAAS,CAACC,cAAc,CAACC,IAAI,CAACuD,GAAG,EAAEC,GAAG,CAAC,KAC/C/P,eAAe,CAAC8P,GAAG,CAACC,GAAG,CAAC,CAAC,IACxB,OAAOD,GAAG,CAACC,GAAG,CAAC,CAAC3P,SAAS,KAAK,QAAQ,CAAC,EAAE;QAC3ChD,IAAI,GAAGuS,SAAS,CAACvS,IAAI,EAAE0S,GAAG,CAACC,GAAG,CAAC,CAAC3S,IAAI,CAAC;MACvC;IACF;IACA,OAAOA,IAAI;EACb;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;EACE,SAAS4S,eAAeA,CAAEC,IAAI,EAAEC,MAAM,EAAE;IACtC,IAAIH,GAAG;IACP,KAAKA,GAAG,IAAIG,MAAM,EAAE;MAClB,IAAIhT,MAAM,CAACmP,SAAS,CAACC,cAAc,CAACC,IAAI,CAAC2D,MAAM,EAAEH,GAAG,CAAC,EAAE;QACrD,IAAIA,GAAG,IAAIE,IAAI,EAAE;UACf,IAAIC,MAAM,CAACH,GAAG,CAAC,KAAKE,IAAI,CAACF,GAAG,CAAC,EAAE;YAC7B,MAAMvK,GAAG,GAAG,IAAIrD,KAAK,CAAC,aAAa,GAAG4N,GAAG,GAAG,oBAAoB,CAAC;YACjEvK,GAAG,CAACM,IAAI,GAAG;cACT1F,SAAS,EAAE2P,GAAG;cACdI,cAAc,EAAED,MAAM,CAACH,GAAG,CAAC;cAC3BK,YAAY,EAAEH,IAAI,CAACF,GAAG;YACxB,CAAC;YACD,MAAMvK,GAAG;UACX;UACA;QACF;QACAyK,IAAI,CAACF,GAAG,CAAC,GAAGG,MAAM,CAACH,GAAG,CAAC;MACzB;IACF;EACF;EAEA,MAAMM,SAAS,GAAGtS,KAAK;;EAEvB;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EAEEA,KAAK,GAAG,SAAAA,CAAUuS,SAAS,EAAE;IAC3B,MAAMC,KAAK,GAAG,OAAOD,SAAS,KAAK,QAAQ;IAC3C,MAAMf,KAAK,GAAGgB,KAAK,GAAG,CAAC,GAAG,CAAC;IAC3B,IAAInT,IAAI,GAAGmT,KAAK,GAAGD,SAAS,GAAG,EAAE;IACjC,MAAME,aAAa,GAAG,CAAC,CAAC;IACxB,KAAK,IAAIvR,CAAC,GAAGsQ,KAAK,EAAEtQ,CAAC,GAAGL,SAAS,CAACC,MAAM,EAAE,EAAEI,CAAC,EAAE;MAC7C,MAAMwR,IAAI,GAAG7R,SAAS,CAACK,CAAC,CAAC;MACzB,IAAIyR,eAAe,GAAG,CAAC,CAAC;MACxB,IAAIC,QAAQ;MACZ,IAAI,OAAOF,IAAI,KAAK,UAAU,EAAE;QAC9BE,QAAQ,GAAGF,IAAI,CAACrT,IAAI;QACpB,IAAI,OAAOqT,IAAI,CAACrQ,SAAS,KAAK,QAAQ,EAAE;UACtC;UACAsQ,eAAe,CAACD,IAAI,CAACrQ,SAAS,CAAC,GAAGqQ,IAAI;QACxC,CAAC,MAAM,IAAIzQ,eAAe,CAACyQ,IAAI,CAAC,EAAE;UAChC;UACAC,eAAe,GAAGD,IAAI,CAAC5P,UAAU;QACnC;MACF,CAAC,MAAM,IAAI9D,aAAa,CAAC0T,IAAI,CAAC,EAAE;QAC9B;QACAC,eAAe,GAAGD,IAAI;QACtB,IAAI,CAACF,KAAK,EAAE;UACVI,QAAQ,GAAGd,aAAa,CAACY,IAAI,CAAC;QAChC;MACF;MAEA,IAAIvT,MAAM,CAAC2O,IAAI,CAAC6E,eAAe,CAAC,CAAC7R,MAAM,KAAK,CAAC,EAAE;QAC7C,MAAM2G,GAAG,GAAG,IAAIhH,SAAS,CACvB,iCAAiC,GAAGS,CAAC,GAAG,8BAA8B,GACtE,gEAAgE,CAAC;QACnEuG,GAAG,CAACM,IAAI,GAAG;UAAE/G,KAAK,EAAEE,CAAC;UAAE2R,QAAQ,EAAEH;QAAK,CAAC;QACvC,MAAMjL,GAAG;MACX;MAEA,IAAI,CAAC+K,KAAK,EAAE;QACVnT,IAAI,GAAGuS,SAAS,CAACvS,IAAI,EAAEuT,QAAQ,CAAC;MAClC;MACAX,eAAe,CAACQ,aAAa,EAAEE,eAAe,CAAC;IACjD;IAEA,OAAO3E,mBAAmB,CAAC3O,IAAI,IAAI,EAAE,EAAEoT,aAAa,CAAC;EACvD,CAAC;EAEDzS,KAAK,CAACjB,MAAM,GAAGA,MAAM;EACrBiB,KAAK,CAACC,WAAW,GAAGqS,SAAS,CAACrS,WAAW;EACzCD,KAAK,CAACkR,UAAU,GAAGI,WAAW;EAC9BtR,KAAK,CAAC8S,kBAAkB,GAAGxB,WAAW;EACtCtR,KAAK,CAACwH,WAAW,GAAGA,WAAW;EAC/BxH,KAAK,CAAC0B,KAAK,GAAGA,KAAK;EACnB1B,KAAK,CAAC4B,gBAAgB,GAAGA,gBAAgB;EACzC5B,KAAK,CAACU,QAAQ,GAAGA,QAAQ;EACzBV,KAAK,CAAC+S,SAAS,GAAG7S,QAAQ,EAAC;EAC3BF,KAAK,CAAC8M,OAAO,GAAGA,OAAO;EACvB9M,KAAK,CAAC0M,WAAW,GAAGA,WAAW;EAC/B1M,KAAK,CAACkE,OAAO,GAAGA,OAAO;EACvBlE,KAAK,CAACmC,aAAa,GAAGA,aAAa;EACnCnC,KAAK,CAACgE,IAAI,GAAGA,IAAI;EACjBhE,KAAK,CAACiC,eAAe,GAAGA,eAAe;EACvCjC,KAAK,CAACkO,yBAAyB,GAAG,IAAI;;EAEtC;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACElO,KAAK,CAACgT,OAAO,GAAG,UAAU5S,IAAI,EAAE6S,gBAAgB,EAAE;IAChD,IAAIC,MAAM,GAAG,KAAK;IAClB,IAAID,gBAAgB,KAAK,KAAK,IAAIpT,OAAO,CAACsB,GAAG,CAAC,QAAQ,CAAC,EAAE;MACvD+R,MAAM,GAAG,QAAQ;IACnB;IACAlT,KAAK,CAACU,QAAQ,CAAC,CAACN,IAAI,CAAC,EAAE8S,MAAM,CAAC;EAChC,CAAC;;EAED;AACF;AACA;AACA;AACA;AACA;AACA;EACE,SAASC,mBAAmBA,CAAE/N,UAAU,EAAE;IACxC,IAAI,CAACA,UAAU,IACb,OAAOA,UAAU,CAACd,IAAI,KAAK,QAAQ,IACnC,OAAOc,UAAU,CAACgO,EAAE,KAAK,QAAQ,IACjC,OAAOhO,UAAU,CAAClB,OAAO,KAAK,UAAU,EAAE;MAC1C,MAAM,IAAIzD,SAAS,CAAC,+EAA+E,CAAC;IACtG;IACA,IAAI2E,UAAU,CAACgO,EAAE,KAAKhO,UAAU,CAACd,IAAI,EAAE;MACrC,MAAM,IAAI+B,WAAW,CACnB,qCAAqC,GAAGjB,UAAU,CAACd,IAAI,GACvD,cAAc,CAAC;IACnB;EACF;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACEtE,KAAK,CAACqT,aAAa,GAAG,UAAUjO,UAAU,EAAiC;IAAA,IAA/B9C,OAAO,GAAAzB,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAhC,SAAA,GAAAgC,SAAA,MAAG;MAAEyS,QAAQ,EAAE;IAAM,CAAC;IACvEH,mBAAmB,CAAC/N,UAAU,CAAC;IAE/B,MAAMgO,EAAE,GAAGlT,QAAQ,CAACkF,UAAU,CAACgO,EAAE,CAAC;IAClC,MAAMG,QAAQ,GAAGH,EAAE,CAAC9R,aAAa,CAAC0C,IAAI,CAAEwP,KAAK,IAAKA,KAAK,CAAClP,IAAI,KAAKc,UAAU,CAACd,IAAI,CAAC;IAEjF,IAAIiP,QAAQ,EAAE;MACZ,IAAIjR,OAAO,IAAIA,OAAO,CAACgR,QAAQ,EAAE;QAC/BtT,KAAK,CAACyT,gBAAgB,CAAC;UAAEnP,IAAI,EAAEiP,QAAQ,CAACjP,IAAI;UAAE8O,EAAE,EAAEhO,UAAU,CAACgO,EAAE;UAAElP,OAAO,EAAEqP,QAAQ,CAACrP;QAAQ,CAAC,CAAC;MAC/F,CAAC,MAAM;QACL,MAAM,IAAIE,KAAK,CACb,sCAAsC,GAAGgB,UAAU,CAACd,IAAI,GAAG,QAAQ,GACnE8O,EAAE,CAAC/T,IAAI,GAAG,GAAG,CAAC;MAClB;IACF;IAEA+T,EAAE,CAAC9R,aAAa,CAACF,IAAI,CAAC;MACpBkD,IAAI,EAAEc,UAAU,CAACd,IAAI;MACrBJ,OAAO,EAAEkB,UAAU,CAAClB,OAAO;MAC3BlD,KAAK,EAAEjB,YAAY;IACrB,CAAC,CAAC;EACJ,CAAC;;EAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACEC,KAAK,CAAC0T,cAAc,GAAG,UAAUvP,WAAW,EAAE7B,OAAO,EAAE;IACrD6B,WAAW,CAAC6B,OAAO,CAACZ,UAAU,IAAIpF,KAAK,CAACqT,aAAa,CAACjO,UAAU,EAAE9C,OAAO,CAAC,CAAC;EAC7E,CAAC;;EAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEtC,KAAK,CAACyT,gBAAgB,GAAG,UAAUrO,UAAU,EAAE;IAC7C+N,mBAAmB,CAAC/N,UAAU,CAAC;IAC/B,MAAMgO,EAAE,GAAGlT,QAAQ,CAACkF,UAAU,CAACgO,EAAE,CAAC;IAClC,MAAMO,kBAAkB,GACtBjC,WAAW,CAAC0B,EAAE,CAAC9R,aAAa,EAAE8I,CAAC,IAAKA,CAAC,CAAC9F,IAAI,KAAKc,UAAU,CAACd,IAAK,CAAC;IAClE,IAAI,CAACqP,kBAAkB,EAAE;MACvB,MAAM,IAAIvP,KAAK,CACb,gDAAgD,GAAGgB,UAAU,CAACd,IAAI,GAClE,MAAM,GAAGc,UAAU,CAACgO,EAAE,CAAC;IAC3B;IACA,IAAIO,kBAAkB,CAACzP,OAAO,KAAKkB,UAAU,CAAClB,OAAO,EAAE;MACrD,MAAM,IAAIE,KAAK,CACb,yDAAyD,CAAC;IAC9D;IACA,MAAMpD,KAAK,GAAGoS,EAAE,CAAC9R,aAAa,CAACsD,OAAO,CAAC+O,kBAAkB,CAAC;IAC1DP,EAAE,CAAC9R,aAAa,CAACsS,MAAM,CAAC5S,KAAK,EAAE,CAAC,CAAC;EACnC,CAAC;;EAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEhB,KAAK,CAAC6T,OAAO,GAAG,UAAUC,EAAE,EAAEC,OAAO,EAAE;IACrC,IAAI,CAAC9R,eAAe,CAAC6R,EAAE,CAAC,EAAE;MACxB,MAAM,IAAIrT,SAAS,CAAC3B,kBAAkB,CAAC;IACzC;IACA,MAAMkV,IAAI,GAAGF,EAAE,CAAC9Q,kBAAkB,CAACF,UAAU;IAC7C,KAAK,IAAI5B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG8S,IAAI,CAAClT,MAAM,EAAE,EAAEI,CAAC,EAAE;MACpC,IAAI8S,IAAI,CAAC9S,CAAC,CAAC,CAAC5B,IAAI,CAACyU,OAAO,CAAC,EAAE;QACzB,OAAOC,IAAI,CAAC9S,CAAC,CAAC;MAChB;IACF;IACA,OAAO,IAAI;EACb,CAAC;EAED,OAAOlB,KAAK;AACd;AAEA,eAAejB,MAAM,CAAC,CAAC", "ignoreList": []}