{"version": 3, "file": "typed-function.js", "sources": ["../esm/typed-function.mjs"], "sourcesContent": ["function ok() {\n  return true;\n}\nfunction notOk() {\n  return false;\n}\nfunction undef() {\n  return undefined;\n}\nconst NOT_TYPED_FUNCTION = 'Argument is not a typed-function.';\n\n/**\n * @typedef {{\n *   params: Param[],\n *   fn: function,\n *   test: function,\n *   implementation: function\n * }} Signature\n *\n * @typedef {{\n *   types: Type[],\n *   hasAny: boolean,\n *   hasConversion: boolean,\n *   restParam: boolean\n * }} Param\n *\n * @typedef {{\n *   name: string,\n *   typeIndex: number,\n *   test: function,\n *   isAny: boolean,\n *   conversion?: ConversionDef,\n *   conversionIndex: number,\n * }} Type\n *\n * @typedef {{\n *   from: string,\n *   to: string,\n *   convert: function (*) : *\n * }} ConversionDef\n *\n * @typedef {{\n *   name: string,\n *   test: function(*) : boolean,\n *   isAny?: boolean\n * }} TypeDef\n */\n\n/**\n * @returns {() => function}\n */\nfunction create() {\n  // data type tests\n\n  /**\n   * Returns true if the argument is a non-null \"plain\" object\n   */\n  function isPlainObject(x) {\n    return typeof x === 'object' && x !== null && x.constructor === Object;\n  }\n  const _types = [{\n    name: 'number',\n    test: function (x) {\n      return typeof x === 'number';\n    }\n  }, {\n    name: 'string',\n    test: function (x) {\n      return typeof x === 'string';\n    }\n  }, {\n    name: 'boolean',\n    test: function (x) {\n      return typeof x === 'boolean';\n    }\n  }, {\n    name: 'Function',\n    test: function (x) {\n      return typeof x === 'function';\n    }\n  }, {\n    name: 'Array',\n    test: Array.isArray\n  }, {\n    name: 'Date',\n    test: function (x) {\n      return x instanceof Date;\n    }\n  }, {\n    name: 'RegExp',\n    test: function (x) {\n      return x instanceof RegExp;\n    }\n  }, {\n    name: 'Object',\n    test: isPlainObject\n  }, {\n    name: 'null',\n    test: function (x) {\n      return x === null;\n    }\n  }, {\n    name: 'undefined',\n    test: function (x) {\n      return x === undefined;\n    }\n  }];\n  const anyType = {\n    name: 'any',\n    test: ok,\n    isAny: true\n  };\n\n  // Data structures to track the types. As these are local variables in\n  // create(), each typed universe will get its own copy, but the variables\n  // will only be accessible through the (closures of the) functions supplied\n  // as properties of the typed object, not directly.\n  // These will be initialized in clear() below\n  let typeMap; // primary store of all types\n  let typeList; // Array of just type names, for the sake of ordering\n\n  // And similar data structures for the type conversions:\n  let nConversions = 0;\n  // the actual conversions are stored on a property of the destination types\n\n  // This is a temporary object, will be replaced with a function at the end\n  let typed = {\n    createCount: 0\n  };\n\n  /**\n   * Takes a type name and returns the corresponding official type object\n   * for that type.\n   *\n   * @param {string} typeName\n   * @returns {TypeDef} type\n   */\n  function findType(typeName) {\n    const type = typeMap.get(typeName);\n    if (type) {\n      return type;\n    }\n    // Remainder is error handling\n    let message = 'Unknown type \"' + typeName + '\"';\n    const name = typeName.toLowerCase();\n    let otherName;\n    for (otherName of typeList) {\n      if (otherName.toLowerCase() === name) {\n        message += '. Did you mean \"' + otherName + '\" ?';\n        break;\n      }\n    }\n    throw new TypeError(message);\n  }\n\n  /**\n   * Adds an array `types` of type definitions to this typed instance.\n   * Each type definition should be an object with properties:\n   * 'name' - a string giving the name of the type; 'test' - function\n   * returning a boolean that tests membership in the type; and optionally\n   * 'isAny' - true only for the 'any' type.\n   *\n   * The second optional argument, `before`, gives the name of a type that\n   * these types should be added before. The new types are added in the\n   * order specified.\n   * @param {TypeDef[]} types\n   * @param {string | boolean} [beforeSpec='any'] before\n   */\n  function addTypes(types) {\n    let beforeSpec = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'any';\n    const beforeIndex = beforeSpec ? findType(beforeSpec).index : typeList.length;\n    const newTypes = [];\n    for (let i = 0; i < types.length; ++i) {\n      if (!types[i] || typeof types[i].name !== 'string' || typeof types[i].test !== 'function') {\n        throw new TypeError('Object with properties {name: string, test: function} expected');\n      }\n      const typeName = types[i].name;\n      if (typeMap.has(typeName)) {\n        throw new TypeError('Duplicate type name \"' + typeName + '\"');\n      }\n      newTypes.push(typeName);\n      typeMap.set(typeName, {\n        name: typeName,\n        test: types[i].test,\n        isAny: types[i].isAny,\n        index: beforeIndex + i,\n        conversionsTo: [] // Newly added type can't have any conversions to it\n      });\n    }\n    // update the typeList\n    const affectedTypes = typeList.slice(beforeIndex);\n    typeList = typeList.slice(0, beforeIndex).concat(newTypes).concat(affectedTypes);\n    // Fix the indices\n    for (let i = beforeIndex + newTypes.length; i < typeList.length; ++i) {\n      typeMap.get(typeList[i]).index = i;\n    }\n  }\n\n  /**\n   * Removes all types and conversions from this typed instance.\n   * May cause previously constructed typed-functions to throw\n   * strange errors when they are called with types that do not\n   * match any of their signatures.\n   */\n  function clear() {\n    typeMap = new Map();\n    typeList = [];\n    nConversions = 0;\n    addTypes([anyType], false);\n  }\n\n  // initialize the types to the default list\n  clear();\n  addTypes(_types);\n\n  /**\n   * Removes all conversions, leaving the types alone.\n   */\n  function clearConversions() {\n    let typeName;\n    for (typeName of typeList) {\n      typeMap.get(typeName).conversionsTo = [];\n    }\n    nConversions = 0;\n  }\n\n  /**\n   * Find the type names that match a value.\n   * @param {*} value\n   * @return {string[]} Array of names of types for which\n   *                  the type test matches the value.\n   */\n  function findTypeNames(value) {\n    const matches = typeList.filter(name => {\n      const type = typeMap.get(name);\n      return !type.isAny && type.test(value);\n    });\n    if (matches.length) {\n      return matches;\n    }\n    return ['any'];\n  }\n\n  /**\n   * Check if an entity is a typed function created by any instance\n   * @param {any} entity\n   * @returns {boolean}\n   */\n  function isTypedFunction(entity) {\n    return entity && typeof entity === 'function' && '_typedFunctionData' in entity;\n  }\n\n  /**\n   * Find a specific signature from a (composed) typed function, for example:\n   *\n   *   typed.findSignature(fn, ['number', 'string'])\n   *   typed.findSignature(fn, 'number, string')\n   *   typed.findSignature(fn, 'number,string', {exact: true})\n   *\n   * This function findSignature will by default return the best match to\n   * the given signature, possibly employing type conversions.\n   *\n   * The (optional) third argument is a plain object giving options\n   * controlling the signature search. Currently the only implemented\n   * option is `exact`: if specified as true (default is false), only\n   * exact matches will be returned (i.e. signatures for which `fn` was\n   * directly defined). Note that a (possibly different) type matching\n   * `any`, or one or more instances of TYPE matching `...TYPE` are\n   * considered exact matches in this regard, as no conversions are used.\n   *\n   * This function returns a \"signature\" object, as does `typed.resolve()`,\n   * which is a plain object with four keys: `params` (the array of parameters\n   * for this signature), `fn` (the originally supplied function for this\n   * signature), `test` (a generated function that determines if an argument\n   * list matches this signature, and `implementation` (the function to call\n   * on a matching argument list, that performs conversions if necessary and\n   * then calls the originally supplied function).\n   *\n   * @param {Function} fn                   A typed-function\n   * @param {string | string[]} signature\n   *     Signature to be found, can be an array or a comma separated string.\n   * @param {object} options  Controls the signature search as documented\n   * @return {{ params: Param[], fn: function, test: function, implementation: function }}\n   *     Returns the matching signature, or throws an error when no signature\n   *     is found.\n   */\n  function findSignature(fn, signature, options) {\n    if (!isTypedFunction(fn)) {\n      throw new TypeError(NOT_TYPED_FUNCTION);\n    }\n\n    // Canonicalize input\n    const exact = options && options.exact;\n    const stringSignature = Array.isArray(signature) ? signature.join(',') : signature;\n    const params = parseSignature(stringSignature);\n    const canonicalSignature = stringifyParams(params);\n\n    // First hope we get lucky and exactly match a signature\n    if (!exact || canonicalSignature in fn.signatures) {\n      // OK, we can check the internal signatures\n      const match = fn._typedFunctionData.signatureMap.get(canonicalSignature);\n      if (match) {\n        return match;\n      }\n    }\n\n    // Oh well, we did not; so we have to go back and check the parameters\n    // one by one, in order to catch things like `any` and rest params.\n    // Note here we can assume there is at least one parameter, because\n    // the empty signature would have matched successfully above.\n    const nParams = params.length;\n    let remainingSignatures;\n    if (exact) {\n      remainingSignatures = [];\n      let name;\n      for (name in fn.signatures) {\n        remainingSignatures.push(fn._typedFunctionData.signatureMap.get(name));\n      }\n    } else {\n      remainingSignatures = fn._typedFunctionData.signatures;\n    }\n    for (let i = 0; i < nParams; ++i) {\n      const want = params[i];\n      const filteredSignatures = [];\n      let possibility;\n      for (possibility of remainingSignatures) {\n        const have = getParamAtIndex(possibility.params, i);\n        if (!have || want.restParam && !have.restParam) {\n          continue;\n        }\n        if (!have.hasAny) {\n          // have to check all of the wanted types are available\n          const haveTypes = paramTypeSet(have);\n          if (want.types.some(wtype => !haveTypes.has(wtype.name))) {\n            continue;\n          }\n        }\n        // OK, this looks good\n        filteredSignatures.push(possibility);\n      }\n      remainingSignatures = filteredSignatures;\n      if (remainingSignatures.length === 0) break;\n    }\n    // Return the first remaining signature that was totally matched:\n    let candidate;\n    for (candidate of remainingSignatures) {\n      if (candidate.params.length <= nParams) {\n        return candidate;\n      }\n    }\n    throw new TypeError('Signature not found (signature: ' + (fn.name || 'unnamed') + '(' + stringifyParams(params, ', ') + '))');\n  }\n\n  /**\n   * Find the proper function to call for a specific signature from\n   * a (composed) typed function, for example:\n   *\n   *   typed.find(fn, ['number', 'string'])\n   *   typed.find(fn, 'number, string')\n   *   typed.find(fn, 'number,string', {exact: true})\n   *\n   * This function find will by default return the best match to\n   * the given signature, possibly employing type conversions (and returning\n   * a function that will perform those conversions as needed). The\n   * (optional) third argument is a plain object giving options contolling\n   * the signature search. Currently only the option `exact` is implemented,\n   * which defaults to \"false\". If `exact` is specified as true, then only\n   * exact matches will be returned (i.e. signatures for which `fn` was\n   * directly defined). Uses of `any` and `...TYPE` are considered exact if\n   * no conversions are necessary to apply the corresponding function.\n   *\n   * @param {Function} fn                   A typed-function\n   * @param {string | string[]} signature\n   *     Signature to be found, can be an array or a comma separated string.\n   * @param {object} options  Controls the signature match as documented\n   * @return {function}\n   *     Returns the function to call for the given signature, or throws an\n   *     error if no match is found.\n   */\n  function find(fn, signature, options) {\n    return findSignature(fn, signature, options).implementation;\n  }\n\n  /**\n   * Convert a given value to another data type, specified by type name.\n   *\n   * @param {*} value\n   * @param {string} typeName\n   */\n  function convert(value, typeName) {\n    // check conversion is needed\n    const type = findType(typeName);\n    if (type.test(value)) {\n      return value;\n    }\n    const conversions = type.conversionsTo;\n    if (conversions.length === 0) {\n      throw new Error('There are no conversions to ' + typeName + ' defined.');\n    }\n    for (let i = 0; i < conversions.length; i++) {\n      const fromType = findType(conversions[i].from);\n      if (fromType.test(value)) {\n        return conversions[i].convert(value);\n      }\n    }\n    throw new Error('Cannot convert ' + value + ' to ' + typeName);\n  }\n\n  /**\n   * Stringify parameters in a normalized way\n   * @param {Param[]} params\n   * @param {string} [','] separator\n   * @return {string}\n   */\n  function stringifyParams(params) {\n    let separator = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : ',';\n    return params.map(p => p.name).join(separator);\n  }\n\n  /**\n   * Parse a parameter, like \"...number | boolean\"\n   * @param {string} param\n   * @return {Param} param\n   */\n  function parseParam(param) {\n    const restParam = param.indexOf('...') === 0;\n    const types = !restParam ? param : param.length > 3 ? param.slice(3) : 'any';\n    const typeDefs = types.split('|').map(s => findType(s.trim()));\n    let hasAny = false;\n    let paramName = restParam ? '...' : '';\n    const exactTypes = typeDefs.map(function (type) {\n      hasAny = type.isAny || hasAny;\n      paramName += type.name + '|';\n      return {\n        name: type.name,\n        typeIndex: type.index,\n        test: type.test,\n        isAny: type.isAny,\n        conversion: null,\n        conversionIndex: -1\n      };\n    });\n    return {\n      types: exactTypes,\n      name: paramName.slice(0, -1),\n      // remove trailing '|' from above\n      hasAny,\n      hasConversion: false,\n      restParam\n    };\n  }\n\n  /**\n   * Expands a parsed parameter with the types available from currently\n   * defined conversions.\n   * @param {Param} param\n   * @return {Param} param\n   */\n  function expandParam(param) {\n    const typeNames = param.types.map(t => t.name);\n    const matchingConversions = availableConversions(typeNames);\n    let hasAny = param.hasAny;\n    let newName = param.name;\n    const convertibleTypes = matchingConversions.map(function (conversion) {\n      const type = findType(conversion.from);\n      hasAny = type.isAny || hasAny;\n      newName += '|' + conversion.from;\n      return {\n        name: conversion.from,\n        typeIndex: type.index,\n        test: type.test,\n        isAny: type.isAny,\n        conversion,\n        conversionIndex: conversion.index\n      };\n    });\n    return {\n      types: param.types.concat(convertibleTypes),\n      name: newName,\n      hasAny,\n      hasConversion: convertibleTypes.length > 0,\n      restParam: param.restParam\n    };\n  }\n\n  /**\n   * Return the set of type names in a parameter.\n   * Caches the result for efficiency\n   *\n   * @param {Param} param\n   * @return {Set<string>} typenames\n   */\n  function paramTypeSet(param) {\n    if (!param.typeSet) {\n      param.typeSet = new Set();\n      param.types.forEach(type => param.typeSet.add(type.name));\n    }\n    return param.typeSet;\n  }\n\n  /**\n   * Parse a signature with comma separated parameters,\n   * like \"number | boolean, ...string\"\n   *\n   * @param {string} signature\n   * @return {Param[]} params\n   */\n  function parseSignature(rawSignature) {\n    const params = [];\n    if (typeof rawSignature !== 'string') {\n      throw new TypeError('Signatures must be strings');\n    }\n    const signature = rawSignature.trim();\n    if (signature === '') {\n      return params;\n    }\n    const rawParams = signature.split(',');\n    for (let i = 0; i < rawParams.length; ++i) {\n      const parsedParam = parseParam(rawParams[i].trim());\n      if (parsedParam.restParam && i !== rawParams.length - 1) {\n        throw new SyntaxError('Unexpected rest parameter \"' + rawParams[i] + '\": ' + 'only allowed for the last parameter');\n      }\n      // if invalid, short-circuit (all the types may have been filtered)\n      if (parsedParam.types.length === 0) {\n        return null;\n      }\n      params.push(parsedParam);\n    }\n    return params;\n  }\n\n  /**\n   * Test whether a set of params contains a restParam\n   * @param {Param[]} params\n   * @return {boolean} Returns true when the last parameter is a restParam\n   */\n  function hasRestParam(params) {\n    const param = last(params);\n    return param ? param.restParam : false;\n  }\n\n  /**\n   * Create a type test for a single parameter, which can have one or multiple\n   * types.\n   * @param {Param} param\n   * @return {function(x: *) : boolean} Returns a test function\n   */\n  function compileTest(param) {\n    if (!param || param.types.length === 0) {\n      // nothing to do\n      return ok;\n    } else if (param.types.length === 1) {\n      return findType(param.types[0].name).test;\n    } else if (param.types.length === 2) {\n      const test0 = findType(param.types[0].name).test;\n      const test1 = findType(param.types[1].name).test;\n      return function or(x) {\n        return test0(x) || test1(x);\n      };\n    } else {\n      // param.types.length > 2\n      const tests = param.types.map(function (type) {\n        return findType(type.name).test;\n      });\n      return function or(x) {\n        for (let i = 0; i < tests.length; i++) {\n          if (tests[i](x)) {\n            return true;\n          }\n        }\n        return false;\n      };\n    }\n  }\n\n  /**\n   * Create a test for all parameters of a signature\n   * @param {Param[]} params\n   * @return {function(args: Array<*>) : boolean}\n   */\n  function compileTests(params) {\n    let tests, test0, test1;\n    if (hasRestParam(params)) {\n      // variable arguments like '...number'\n      tests = initial(params).map(compileTest);\n      const varIndex = tests.length;\n      const lastTest = compileTest(last(params));\n      const testRestParam = function (args) {\n        for (let i = varIndex; i < args.length; i++) {\n          if (!lastTest(args[i])) {\n            return false;\n          }\n        }\n        return true;\n      };\n      return function testArgs(args) {\n        for (let i = 0; i < tests.length; i++) {\n          if (!tests[i](args[i])) {\n            return false;\n          }\n        }\n        return testRestParam(args) && args.length >= varIndex + 1;\n      };\n    } else {\n      // no variable arguments\n      if (params.length === 0) {\n        return function testArgs(args) {\n          return args.length === 0;\n        };\n      } else if (params.length === 1) {\n        test0 = compileTest(params[0]);\n        return function testArgs(args) {\n          return test0(args[0]) && args.length === 1;\n        };\n      } else if (params.length === 2) {\n        test0 = compileTest(params[0]);\n        test1 = compileTest(params[1]);\n        return function testArgs(args) {\n          return test0(args[0]) && test1(args[1]) && args.length === 2;\n        };\n      } else {\n        // arguments.length > 2\n        tests = params.map(compileTest);\n        return function testArgs(args) {\n          for (let i = 0; i < tests.length; i++) {\n            if (!tests[i](args[i])) {\n              return false;\n            }\n          }\n          return args.length === tests.length;\n        };\n      }\n    }\n  }\n\n  /**\n   * Find the parameter at a specific index of a Params list.\n   * Handles rest parameters.\n   * @param {Param[]} params\n   * @param {number} index\n   * @return {Param | null} Returns the matching parameter when found,\n   *                        null otherwise.\n   */\n  function getParamAtIndex(params, index) {\n    return index < params.length ? params[index] : hasRestParam(params) ? last(params) : null;\n  }\n\n  /**\n   * Get all type names of a parameter\n   * @param {Params[]} params\n   * @param {number} index\n   * @return {string[]} Returns an array with type names\n   */\n  function getTypeSetAtIndex(params, index) {\n    const param = getParamAtIndex(params, index);\n    if (!param) {\n      return new Set();\n    }\n    return paramTypeSet(param);\n  }\n\n  /**\n   * Test whether a type is an exact type or conversion\n   * @param {Type} type\n   * @return {boolean} Returns true when\n   */\n  function isExactType(type) {\n    return type.conversion === null || type.conversion === undefined;\n  }\n\n  /**\n   * Helper function for creating error messages: create an array with\n   * all available types on a specific argument index.\n   * @param {Signature[]} signatures\n   * @param {number} index\n   * @return {string[]} Returns an array with available types\n   */\n  function mergeExpectedParams(signatures, index) {\n    const typeSet = new Set();\n    signatures.forEach(signature => {\n      const paramSet = getTypeSetAtIndex(signature.params, index);\n      let name;\n      for (name of paramSet) {\n        typeSet.add(name);\n      }\n    });\n    return typeSet.has('any') ? ['any'] : Array.from(typeSet);\n  }\n\n  /**\n   * Create\n   * @param {string} name             The name of the function\n   * @param {array.<*>} args          The actual arguments passed to the function\n   * @param {Signature[]} signatures  A list with available signatures\n   * @return {TypeError} Returns a type error with additional data\n   *                     attached to it in the property `data`\n   */\n  function createError(name, args, signatures) {\n    let err, expected;\n    const _name = name || 'unnamed';\n\n    // test for wrong type at some index\n    let matchingSignatures = signatures;\n    let index;\n    for (index = 0; index < args.length; index++) {\n      const nextMatchingDefs = [];\n      matchingSignatures.forEach(signature => {\n        const param = getParamAtIndex(signature.params, index);\n        const test = compileTest(param);\n        if ((index < signature.params.length || hasRestParam(signature.params)) && test(args[index])) {\n          nextMatchingDefs.push(signature);\n        }\n      });\n      if (nextMatchingDefs.length === 0) {\n        // no matching signatures anymore, throw error \"wrong type\"\n        expected = mergeExpectedParams(matchingSignatures, index);\n        if (expected.length > 0) {\n          const actualTypes = findTypeNames(args[index]);\n          err = new TypeError('Unexpected type of argument in function ' + _name + ' (expected: ' + expected.join(' or ') + ', actual: ' + actualTypes.join(' | ') + ', index: ' + index + ')');\n          err.data = {\n            category: 'wrongType',\n            fn: _name,\n            index,\n            actual: actualTypes,\n            expected\n          };\n          return err;\n        }\n      } else {\n        matchingSignatures = nextMatchingDefs;\n      }\n    }\n\n    // test for too few arguments\n    const lengths = matchingSignatures.map(function (signature) {\n      return hasRestParam(signature.params) ? Infinity : signature.params.length;\n    });\n    if (args.length < Math.min.apply(null, lengths)) {\n      expected = mergeExpectedParams(matchingSignatures, index);\n      err = new TypeError('Too few arguments in function ' + _name + ' (expected: ' + expected.join(' or ') + ', index: ' + args.length + ')');\n      err.data = {\n        category: 'tooFewArgs',\n        fn: _name,\n        index: args.length,\n        expected\n      };\n      return err;\n    }\n\n    // test for too many arguments\n    const maxLength = Math.max.apply(null, lengths);\n    if (args.length > maxLength) {\n      err = new TypeError('Too many arguments in function ' + _name + ' (expected: ' + maxLength + ', actual: ' + args.length + ')');\n      err.data = {\n        category: 'tooManyArgs',\n        fn: _name,\n        index: args.length,\n        expectedLength: maxLength\n      };\n      return err;\n    }\n\n    // Generic error\n    const argTypes = [];\n    for (let i = 0; i < args.length; ++i) {\n      argTypes.push(findTypeNames(args[i]).join('|'));\n    }\n    err = new TypeError('Arguments of type \"' + argTypes.join(', ') + '\" do not match any of the defined signatures of function ' + _name + '.');\n    err.data = {\n      category: 'mismatch',\n      actual: argTypes\n    };\n    return err;\n  }\n\n  /**\n   * Find the lowest index of all exact types of a parameter (no conversions)\n   * @param {Param} param\n   * @return {number} Returns the index of the lowest type in typed.types\n   */\n  function getLowestTypeIndex(param) {\n    let min = typeList.length + 1;\n    for (let i = 0; i < param.types.length; i++) {\n      if (isExactType(param.types[i])) {\n        min = Math.min(min, param.types[i].typeIndex);\n      }\n    }\n    return min;\n  }\n\n  /**\n   * Find the lowest index of the conversion of all types of the parameter\n   * having a conversion\n   * @param {Param} param\n   * @return {number} Returns the lowest index of the conversions of this type\n   */\n  function getLowestConversionIndex(param) {\n    let min = nConversions + 1;\n    for (let i = 0; i < param.types.length; i++) {\n      if (!isExactType(param.types[i])) {\n        min = Math.min(min, param.types[i].conversionIndex);\n      }\n    }\n    return min;\n  }\n\n  /**\n   * Compare two params\n   * @param {Param} param1\n   * @param {Param} param2\n   * @return {number} returns -1 when param1 must get a lower\n   *                  index than param2, 1 when the opposite,\n   *                  or zero when both are equal\n   */\n  function compareParams(param1, param2) {\n    // We compare a number of metrics on a param in turn:\n    // 1) 'any' parameters are the least preferred\n    if (param1.hasAny) {\n      if (!param2.hasAny) {\n        return 1;\n      }\n    } else if (param2.hasAny) {\n      return -1;\n    }\n\n    // 2) Prefer non-rest to rest parameters\n    if (param1.restParam) {\n      if (!param2.restParam) {\n        return 1;\n      }\n    } else if (param2.restParam) {\n      return -1;\n    }\n\n    // 3) Prefer exact type match to conversions\n    if (param1.hasConversion) {\n      if (!param2.hasConversion) {\n        return 1;\n      }\n    } else if (param2.hasConversion) {\n      return -1;\n    }\n\n    // 4) Prefer lower type index:\n    const typeDiff = getLowestTypeIndex(param1) - getLowestTypeIndex(param2);\n    if (typeDiff < 0) {\n      return -1;\n    }\n    if (typeDiff > 0) {\n      return 1;\n    }\n\n    // 5) Prefer lower conversion index\n    const convDiff = getLowestConversionIndex(param1) - getLowestConversionIndex(param2);\n    if (convDiff < 0) {\n      return -1;\n    }\n    if (convDiff > 0) {\n      return 1;\n    }\n\n    // Don't have a basis for preference\n    return 0;\n  }\n\n  /**\n   * Compare two signatures\n   * @param {Signature} signature1\n   * @param {Signature} signature2\n   * @return {number} returns a negative number when param1 must get a lower\n   *                  index than param2, a positive number when the opposite,\n   *                  or zero when both are equal\n   */\n  function compareSignatures(signature1, signature2) {\n    const pars1 = signature1.params;\n    const pars2 = signature2.params;\n    const last1 = last(pars1);\n    const last2 = last(pars2);\n    const hasRest1 = hasRestParam(pars1);\n    const hasRest2 = hasRestParam(pars2);\n    // We compare a number of metrics on signatures in turn:\n    // 1) An \"any rest param\" is least preferred\n    if (hasRest1 && last1.hasAny) {\n      if (!hasRest2 || !last2.hasAny) {\n        return 1;\n      }\n    } else if (hasRest2 && last2.hasAny) {\n      return -1;\n    }\n\n    // 2) Minimize the number of 'any' parameters\n    let any1 = 0;\n    let conv1 = 0;\n    let par;\n    for (par of pars1) {\n      if (par.hasAny) ++any1;\n      if (par.hasConversion) ++conv1;\n    }\n    let any2 = 0;\n    let conv2 = 0;\n    for (par of pars2) {\n      if (par.hasAny) ++any2;\n      if (par.hasConversion) ++conv2;\n    }\n    if (any1 !== any2) {\n      return any1 - any2;\n    }\n\n    // 3) A conversion rest param is less preferred\n    if (hasRest1 && last1.hasConversion) {\n      if (!hasRest2 || !last2.hasConversion) {\n        return 1;\n      }\n    } else if (hasRest2 && last2.hasConversion) {\n      return -1;\n    }\n\n    // 4) Minimize the number of conversions\n    if (conv1 !== conv2) {\n      return conv1 - conv2;\n    }\n\n    // 5) Prefer no rest param\n    if (hasRest1) {\n      if (!hasRest2) {\n        return 1;\n      }\n    } else if (hasRest2) {\n      return -1;\n    }\n\n    // 6) Prefer shorter with rest param, longer without\n    const lengthCriterion = (pars1.length - pars2.length) * (hasRest1 ? -1 : 1);\n    if (lengthCriterion !== 0) {\n      return lengthCriterion;\n    }\n\n    // Signatures are identical in each of the above metrics.\n    // In particular, they are the same length.\n    // We can therefore compare the parameters one by one.\n    // First we count which signature has more preferred parameters.\n    const comparisons = [];\n    let tc = 0;\n    for (let i = 0; i < pars1.length; ++i) {\n      const thisComparison = compareParams(pars1[i], pars2[i]);\n      comparisons.push(thisComparison);\n      tc += thisComparison;\n    }\n    if (tc !== 0) {\n      return tc;\n    }\n\n    // They have the same number of preferred parameters, so go by the\n    // earliest parameter in which we have a preference.\n    // In other words, dispatch is driven somewhat more by earlier\n    // parameters than later ones.\n    let c;\n    for (c of comparisons) {\n      if (c !== 0) {\n        return c;\n      }\n    }\n\n    // It's a tossup:\n    return 0;\n  }\n\n  /**\n   * Produce a list of all conversions from distinct types to one of\n   * the given types.\n   *\n   * @param {string[]} typeNames\n   * @return {ConversionDef[]} Returns the conversions that are available\n   *                        resulting in any given type (if any)\n   */\n  function availableConversions(typeNames) {\n    if (typeNames.length === 0) {\n      return [];\n    }\n    const types = typeNames.map(findType);\n    if (typeNames.length > 1) {\n      types.sort((t1, t2) => t1.index - t2.index);\n    }\n    let matches = types[0].conversionsTo;\n    if (typeNames.length === 1) {\n      return matches;\n    }\n    matches = matches.concat([]); // shallow copy the matches\n    // Since the types are now in index order, we just want the first\n    // occurrence of any from type:\n    const knownTypes = new Set(typeNames);\n    for (let i = 1; i < types.length; ++i) {\n      let newMatch;\n      for (newMatch of types[i].conversionsTo) {\n        if (!knownTypes.has(newMatch.from)) {\n          matches.push(newMatch);\n          knownTypes.add(newMatch.from);\n        }\n      }\n    }\n    return matches;\n  }\n\n  /**\n   * Preprocess arguments before calling the original function:\n   * - if needed convert the parameters\n   * - in case of rest parameters, move the rest parameters into an Array\n   * @param {Param[]} params\n   * @param {function} fn\n   * @return {function} Returns a wrapped function\n   */\n  function compileArgsPreprocessing(params, fn) {\n    let fnConvert = fn;\n\n    // TODO: can we make this wrapper function smarter/simpler?\n\n    if (params.some(p => p.hasConversion)) {\n      const restParam = hasRestParam(params);\n      const compiledConversions = params.map(compileArgConversion);\n      fnConvert = function convertArgs() {\n        const args = [];\n        const last = restParam ? arguments.length - 1 : arguments.length;\n        for (let i = 0; i < last; i++) {\n          args[i] = compiledConversions[i](arguments[i]);\n        }\n        if (restParam) {\n          args[last] = arguments[last].map(compiledConversions[last]);\n        }\n        return fn.apply(this, args);\n      };\n    }\n    let fnPreprocess = fnConvert;\n    if (hasRestParam(params)) {\n      const offset = params.length - 1;\n      fnPreprocess = function preprocessRestParams() {\n        return fnConvert.apply(this, slice(arguments, 0, offset).concat([slice(arguments, offset)]));\n      };\n    }\n    return fnPreprocess;\n  }\n\n  /**\n   * Compile conversion for a parameter to the right type\n   * @param {Param} param\n   * @return {function} Returns the wrapped function that will convert arguments\n   *\n   */\n  function compileArgConversion(param) {\n    let test0, test1, conversion0, conversion1;\n    const tests = [];\n    const conversions = [];\n    param.types.forEach(function (type) {\n      if (type.conversion) {\n        tests.push(findType(type.conversion.from).test);\n        conversions.push(type.conversion.convert);\n      }\n    });\n\n    // create optimized conversion functions depending on the number of conversions\n    switch (conversions.length) {\n      case 0:\n        return function convertArg(arg) {\n          return arg;\n        };\n      case 1:\n        test0 = tests[0];\n        conversion0 = conversions[0];\n        return function convertArg(arg) {\n          if (test0(arg)) {\n            return conversion0(arg);\n          }\n          return arg;\n        };\n      case 2:\n        test0 = tests[0];\n        test1 = tests[1];\n        conversion0 = conversions[0];\n        conversion1 = conversions[1];\n        return function convertArg(arg) {\n          if (test0(arg)) {\n            return conversion0(arg);\n          }\n          if (test1(arg)) {\n            return conversion1(arg);\n          }\n          return arg;\n        };\n      default:\n        return function convertArg(arg) {\n          for (let i = 0; i < conversions.length; i++) {\n            if (tests[i](arg)) {\n              return conversions[i](arg);\n            }\n          }\n          return arg;\n        };\n    }\n  }\n\n  /**\n   * Split params with union types in to separate params.\n   *\n   * For example:\n   *\n   *     splitParams([['Array', 'Object'], ['string', 'RegExp'])\n   *     // returns:\n   *     // [\n   *     //   ['Array', 'string'],\n   *     //   ['Array', 'RegExp'],\n   *     //   ['Object', 'string'],\n   *     //   ['Object', 'RegExp']\n   *     // ]\n   *\n   * @param {Param[]} params\n   * @return {Param[]}\n   */\n  function splitParams(params) {\n    function _splitParams(params, index, paramsSoFar) {\n      if (index < params.length) {\n        const param = params[index];\n        let resultingParams = [];\n        if (param.restParam) {\n          // split the types of a rest parameter in two:\n          // one with only exact types, and one with exact types and conversions\n          const exactTypes = param.types.filter(isExactType);\n          if (exactTypes.length < param.types.length) {\n            resultingParams.push({\n              types: exactTypes,\n              name: '...' + exactTypes.map(t => t.name).join('|'),\n              hasAny: exactTypes.some(t => t.isAny),\n              hasConversion: false,\n              restParam: true\n            });\n          }\n          resultingParams.push(param);\n        } else {\n          // split all the types of a regular parameter into one type per param\n          resultingParams = param.types.map(function (type) {\n            return {\n              types: [type],\n              name: type.name,\n              hasAny: type.isAny,\n              hasConversion: type.conversion,\n              restParam: false\n            };\n          });\n        }\n\n        // recurse over the groups with types\n        return flatMap(resultingParams, function (nextParam) {\n          return _splitParams(params, index + 1, paramsSoFar.concat([nextParam]));\n        });\n      } else {\n        // we've reached the end of the parameters.\n        return [paramsSoFar];\n      }\n    }\n    return _splitParams(params, 0, []);\n  }\n\n  /**\n   * Test whether two param lists represent conflicting signatures\n   * @param {Param[]} params1\n   * @param {Param[]} params2\n   * @return {boolean} Returns true when the signatures conflict, false otherwise.\n   */\n  function conflicting(params1, params2) {\n    const ii = Math.max(params1.length, params2.length);\n    for (let i = 0; i < ii; i++) {\n      const typeSet1 = getTypeSetAtIndex(params1, i);\n      const typeSet2 = getTypeSetAtIndex(params2, i);\n      let overlap = false;\n      let name;\n      for (name of typeSet2) {\n        if (typeSet1.has(name)) {\n          overlap = true;\n          break;\n        }\n      }\n      if (!overlap) {\n        return false;\n      }\n    }\n    const len1 = params1.length;\n    const len2 = params2.length;\n    const restParam1 = hasRestParam(params1);\n    const restParam2 = hasRestParam(params2);\n    return restParam1 ? restParam2 ? len1 === len2 : len2 >= len1 : restParam2 ? len1 >= len2 : len1 === len2;\n  }\n\n  /**\n   * Helper function for `resolveReferences` that returns a copy of\n   * functionList wihe any prior resolutions cleared out, in case we are\n   * recycling signatures from a prior typed function construction.\n   *\n   * @param {Array.<function|typed-reference>} functionList\n   * @return {Array.<function|typed-reference>}\n   */\n  function clearResolutions(functionList) {\n    return functionList.map(fn => {\n      if (isReferToSelf(fn)) {\n        return referToSelf(fn.referToSelf.callback);\n      }\n      if (isReferTo(fn)) {\n        return makeReferTo(fn.referTo.references, fn.referTo.callback);\n      }\n      return fn;\n    });\n  }\n\n  /**\n   * Take a list of references, a list of functions functionList, and a\n   * signatureMap indexing signatures into functionList, and return\n   * the list of resolutions, or a false-y value if they don't all\n   * resolve in a valid way (yet).\n   *\n   * @param {string[]} references\n   * @param {Array<function|typed-reference} functionList\n   * @param {Object.<string, integer>} signatureMap\n   * @return {function[] | false} resolutions\n   */\n  function collectResolutions(references, functionList, signatureMap) {\n    const resolvedReferences = [];\n    let reference;\n    for (reference of references) {\n      let resolution = signatureMap[reference];\n      if (typeof resolution !== 'number') {\n        throw new TypeError('No definition for referenced signature \"' + reference + '\"');\n      }\n      resolution = functionList[resolution];\n      if (typeof resolution !== 'function') {\n        return false;\n      }\n      resolvedReferences.push(resolution);\n    }\n    return resolvedReferences;\n  }\n\n  /**\n   * Resolve any references in the functionList for the typed function\n   * itself. The signatureMap tells which index in the functionList a\n   * given signature should be mapped to (for use in resolving typed.referTo)\n   * and self provides the destions of a typed.referToSelf.\n   *\n   * @param {Array<function | typed-reference-object>} functionList\n   * @param {Object.<string, function>} signatureMap\n   * @param {function} self  The typed-function itself\n   * @return {Array<function>} The list of resolved functions\n   */\n  function resolveReferences(functionList, signatureMap, self) {\n    const resolvedFunctions = clearResolutions(functionList);\n    const isResolved = new Array(resolvedFunctions.length).fill(false);\n    let leftUnresolved = true;\n    while (leftUnresolved) {\n      leftUnresolved = false;\n      let nothingResolved = true;\n      for (let i = 0; i < resolvedFunctions.length; ++i) {\n        if (isResolved[i]) continue;\n        const fn = resolvedFunctions[i];\n        if (isReferToSelf(fn)) {\n          resolvedFunctions[i] = fn.referToSelf.callback(self);\n          // Preserve reference in case signature is reused someday:\n          resolvedFunctions[i].referToSelf = fn.referToSelf;\n          isResolved[i] = true;\n          nothingResolved = false;\n        } else if (isReferTo(fn)) {\n          const resolvedReferences = collectResolutions(fn.referTo.references, resolvedFunctions, signatureMap);\n          if (resolvedReferences) {\n            resolvedFunctions[i] = fn.referTo.callback.apply(this, resolvedReferences);\n            // Preserve reference in case signature is reused someday:\n            resolvedFunctions[i].referTo = fn.referTo;\n            isResolved[i] = true;\n            nothingResolved = false;\n          } else {\n            leftUnresolved = true;\n          }\n        }\n      }\n      if (nothingResolved && leftUnresolved) {\n        throw new SyntaxError('Circular reference detected in resolving typed.referTo');\n      }\n    }\n    return resolvedFunctions;\n  }\n\n  /**\n   * Validate whether any of the function bodies contains a self-reference\n   * usage like `this(...)` or `this.signatures`. This self-referencing is\n   * deprecated since typed-function v3. It has been replaced with\n   * the functions typed.referTo and typed.referToSelf.\n   * @param {Object.<string, function>} signaturesMap\n   */\n  function validateDeprecatedThis(signaturesMap) {\n    // TODO: remove this deprecation warning logic some day (it's introduced in v3)\n\n    // match occurrences like 'this(' and 'this.signatures'\n    const deprecatedThisRegex = /\\bthis(\\(|\\.signatures\\b)/;\n    Object.keys(signaturesMap).forEach(signature => {\n      const fn = signaturesMap[signature];\n      if (deprecatedThisRegex.test(fn.toString())) {\n        throw new SyntaxError('Using `this` to self-reference a function ' + 'is deprecated since typed-function@3. ' + 'Use typed.referTo and typed.referToSelf instead.');\n      }\n    });\n  }\n\n  /**\n   * Create a typed function\n   * @param {String} name               The name for the typed function\n   * @param {Object.<string, function>} rawSignaturesMap\n   *                                    An object with one or\n   *                                    multiple signatures as key, and the\n   *                                    function corresponding to the\n   *                                    signature as value.\n   * @return {function}  Returns the created typed function.\n   */\n  function createTypedFunction(name, rawSignaturesMap) {\n    typed.createCount++;\n    if (Object.keys(rawSignaturesMap).length === 0) {\n      throw new SyntaxError('No signatures provided');\n    }\n    if (typed.warnAgainstDeprecatedThis) {\n      validateDeprecatedThis(rawSignaturesMap);\n    }\n\n    // Main processing loop for signatures\n    const parsedParams = [];\n    const originalFunctions = [];\n    const signaturesMap = {};\n    const preliminarySignatures = []; // may have duplicates from conversions\n    let signature;\n    for (signature in rawSignaturesMap) {\n      // A) Protect against polluted Object prototype:\n      if (!Object.prototype.hasOwnProperty.call(rawSignaturesMap, signature)) {\n        continue;\n      }\n      // B) Parse the signature\n      const params = parseSignature(signature);\n      if (!params) continue;\n      // C) Check for conflicts\n      parsedParams.forEach(function (pp) {\n        if (conflicting(pp, params)) {\n          throw new TypeError('Conflicting signatures \"' + stringifyParams(pp) + '\" and \"' + stringifyParams(params) + '\".');\n        }\n      });\n      parsedParams.push(params);\n      // D) Store the provided function and add conversions\n      const functionIndex = originalFunctions.length;\n      originalFunctions.push(rawSignaturesMap[signature]);\n      const conversionParams = params.map(expandParam);\n      // E) Split the signatures and collect them up\n      let sp;\n      for (sp of splitParams(conversionParams)) {\n        const spName = stringifyParams(sp);\n        preliminarySignatures.push({\n          params: sp,\n          name: spName,\n          fn: functionIndex\n        });\n        if (sp.every(p => !p.hasConversion)) {\n          signaturesMap[spName] = functionIndex;\n        }\n      }\n    }\n    preliminarySignatures.sort(compareSignatures);\n\n    // Note the forward reference to theTypedFn\n    const resolvedFunctions = resolveReferences(originalFunctions, signaturesMap, theTypedFn);\n\n    // Fill in the proper function for each signature\n    let s;\n    for (s in signaturesMap) {\n      if (Object.prototype.hasOwnProperty.call(signaturesMap, s)) {\n        signaturesMap[s] = resolvedFunctions[signaturesMap[s]];\n      }\n    }\n    const signatures = [];\n    const internalSignatureMap = new Map(); // benchmarks faster than object\n    for (s of preliminarySignatures) {\n      // Note it's only safe to eliminate duplicates like this\n      // _after_ the signature sorting step above; otherwise we might\n      // remove the wrong one.\n      if (!internalSignatureMap.has(s.name)) {\n        s.fn = resolvedFunctions[s.fn];\n        signatures.push(s);\n        internalSignatureMap.set(s.name, s);\n      }\n    }\n\n    // we create a highly optimized checks for the first couple of signatures with max 2 arguments\n    const ok0 = signatures[0] && signatures[0].params.length <= 2 && !hasRestParam(signatures[0].params);\n    const ok1 = signatures[1] && signatures[1].params.length <= 2 && !hasRestParam(signatures[1].params);\n    const ok2 = signatures[2] && signatures[2].params.length <= 2 && !hasRestParam(signatures[2].params);\n    const ok3 = signatures[3] && signatures[3].params.length <= 2 && !hasRestParam(signatures[3].params);\n    const ok4 = signatures[4] && signatures[4].params.length <= 2 && !hasRestParam(signatures[4].params);\n    const ok5 = signatures[5] && signatures[5].params.length <= 2 && !hasRestParam(signatures[5].params);\n    const allOk = ok0 && ok1 && ok2 && ok3 && ok4 && ok5;\n\n    // compile the tests\n    for (let i = 0; i < signatures.length; ++i) {\n      signatures[i].test = compileTests(signatures[i].params);\n    }\n    const test00 = ok0 ? compileTest(signatures[0].params[0]) : notOk;\n    const test10 = ok1 ? compileTest(signatures[1].params[0]) : notOk;\n    const test20 = ok2 ? compileTest(signatures[2].params[0]) : notOk;\n    const test30 = ok3 ? compileTest(signatures[3].params[0]) : notOk;\n    const test40 = ok4 ? compileTest(signatures[4].params[0]) : notOk;\n    const test50 = ok5 ? compileTest(signatures[5].params[0]) : notOk;\n    const test01 = ok0 ? compileTest(signatures[0].params[1]) : notOk;\n    const test11 = ok1 ? compileTest(signatures[1].params[1]) : notOk;\n    const test21 = ok2 ? compileTest(signatures[2].params[1]) : notOk;\n    const test31 = ok3 ? compileTest(signatures[3].params[1]) : notOk;\n    const test41 = ok4 ? compileTest(signatures[4].params[1]) : notOk;\n    const test51 = ok5 ? compileTest(signatures[5].params[1]) : notOk;\n\n    // compile the functions\n    for (let i = 0; i < signatures.length; ++i) {\n      signatures[i].implementation = compileArgsPreprocessing(signatures[i].params, signatures[i].fn);\n    }\n    const fn0 = ok0 ? signatures[0].implementation : undef;\n    const fn1 = ok1 ? signatures[1].implementation : undef;\n    const fn2 = ok2 ? signatures[2].implementation : undef;\n    const fn3 = ok3 ? signatures[3].implementation : undef;\n    const fn4 = ok4 ? signatures[4].implementation : undef;\n    const fn5 = ok5 ? signatures[5].implementation : undef;\n    const len0 = ok0 ? signatures[0].params.length : -1;\n    const len1 = ok1 ? signatures[1].params.length : -1;\n    const len2 = ok2 ? signatures[2].params.length : -1;\n    const len3 = ok3 ? signatures[3].params.length : -1;\n    const len4 = ok4 ? signatures[4].params.length : -1;\n    const len5 = ok5 ? signatures[5].params.length : -1;\n\n    // simple and generic, but also slow\n    const iStart = allOk ? 6 : 0;\n    const iEnd = signatures.length;\n    // de-reference ahead for execution speed:\n    const tests = signatures.map(s => s.test);\n    const fns = signatures.map(s => s.implementation);\n    const generic = function generic() {\n      'use strict';\n\n      for (let i = iStart; i < iEnd; i++) {\n        if (tests[i](arguments)) {\n          return fns[i].apply(this, arguments);\n        }\n      }\n      return typed.onMismatch(name, arguments, signatures);\n    };\n\n    // create the typed function\n    // fast, specialized version. Falls back to the slower, generic one if needed\n    function theTypedFn(arg0, arg1) {\n      'use strict';\n\n      if (arguments.length === len0 && test00(arg0) && test01(arg1)) {\n        return fn0.apply(this, arguments);\n      }\n      if (arguments.length === len1 && test10(arg0) && test11(arg1)) {\n        return fn1.apply(this, arguments);\n      }\n      if (arguments.length === len2 && test20(arg0) && test21(arg1)) {\n        return fn2.apply(this, arguments);\n      }\n      if (arguments.length === len3 && test30(arg0) && test31(arg1)) {\n        return fn3.apply(this, arguments);\n      }\n      if (arguments.length === len4 && test40(arg0) && test41(arg1)) {\n        return fn4.apply(this, arguments);\n      }\n      if (arguments.length === len5 && test50(arg0) && test51(arg1)) {\n        return fn5.apply(this, arguments);\n      }\n      return generic.apply(this, arguments);\n    }\n\n    // attach name the typed function\n    try {\n      Object.defineProperty(theTypedFn, 'name', {\n        value: name\n      });\n    } catch (err) {\n      // old browsers do not support Object.defineProperty and some don't support setting the name property\n      // the function name is not essential for the functioning, it's mostly useful for debugging,\n      // so it's fine to have unnamed functions.\n    }\n\n    // attach signatures to the function.\n    // This property is close to the original collection of signatures\n    // used to create the typed-function, just with unions split:\n    theTypedFn.signatures = signaturesMap;\n\n    // Store internal data for functions like resolve, find, etc.\n    // Also serves as the flag that this is a typed-function\n    theTypedFn._typedFunctionData = {\n      signatures,\n      signatureMap: internalSignatureMap\n    };\n    return theTypedFn;\n  }\n\n  /**\n   * Action to take on mismatch\n   * @param {string} name      Name of function that was attempted to be called\n   * @param {Array} args       Actual arguments to the call\n   * @param {Array} signatures Known signatures of the named typed-function\n   */\n  function _onMismatch(name, args, signatures) {\n    throw createError(name, args, signatures);\n  }\n\n  /**\n   * Return all but the last items of an array or function Arguments\n   * @param {Array | Arguments} arr\n   * @return {Array}\n   */\n  function initial(arr) {\n    return slice(arr, 0, arr.length - 1);\n  }\n\n  /**\n   * return the last item of an array or function Arguments\n   * @param {Array | Arguments} arr\n   * @return {*}\n   */\n  function last(arr) {\n    return arr[arr.length - 1];\n  }\n\n  /**\n   * Slice an array or function Arguments\n   * @param {Array | Arguments | IArguments} arr\n   * @param {number} start\n   * @param {number} [end]\n   * @return {Array}\n   */\n  function slice(arr, start, end) {\n    return Array.prototype.slice.call(arr, start, end);\n  }\n\n  /**\n   * Return the first item from an array for which test(arr[i]) returns true\n   * @param {Array} arr\n   * @param {function} test\n   * @return {* | undefined} Returns the first matching item\n   *                         or undefined when there is no match\n   */\n  function findInArray(arr, test) {\n    for (let i = 0; i < arr.length; i++) {\n      if (test(arr[i])) {\n        return arr[i];\n      }\n    }\n    return undefined;\n  }\n\n  /**\n   * Flat map the result invoking a callback for every item in an array.\n   * https://gist.github.com/samgiles/762ee337dff48623e729\n   * @param {Array} arr\n   * @param {function} callback\n   * @return {Array}\n   */\n  function flatMap(arr, callback) {\n    return Array.prototype.concat.apply([], arr.map(callback));\n  }\n\n  /**\n   * Create a reference callback to one or multiple signatures\n   *\n   * Syntax:\n   *\n   *     typed.referTo(signature1, signature2, ..., function callback(fn1, fn2, ...) {\n   *       // ...\n   *     })\n   *\n   * @returns {{referTo: {references: string[], callback}}}\n   */\n  function referTo() {\n    const references = initial(arguments).map(s => stringifyParams(parseSignature(s)));\n    const callback = last(arguments);\n    if (typeof callback !== 'function') {\n      throw new TypeError('Callback function expected as last argument');\n    }\n    return makeReferTo(references, callback);\n  }\n  function makeReferTo(references, callback) {\n    return {\n      referTo: {\n        references,\n        callback\n      }\n    };\n  }\n\n  /**\n   * Create a reference callback to the typed-function itself\n   *\n   * @param {(self: function) => function} callback\n   * @returns {{referToSelf: { callback: function }}}\n   */\n  function referToSelf(callback) {\n    if (typeof callback !== 'function') {\n      throw new TypeError('Callback function expected as first argument');\n    }\n    return {\n      referToSelf: {\n        callback\n      }\n    };\n  }\n\n  /**\n   * Test whether something is a referTo object, holding a list with reference\n   * signatures and a callback.\n   *\n   * @param {Object | function} objectOrFn\n   * @returns {boolean}\n   */\n  function isReferTo(objectOrFn) {\n    return objectOrFn && typeof objectOrFn.referTo === 'object' && Array.isArray(objectOrFn.referTo.references) && typeof objectOrFn.referTo.callback === 'function';\n  }\n\n  /**\n   * Test whether something is a referToSelf object, holding a callback where\n   * to pass `self`.\n   *\n   * @param {Object | function} objectOrFn\n   * @returns {boolean}\n   */\n  function isReferToSelf(objectOrFn) {\n    return objectOrFn && typeof objectOrFn.referToSelf === 'object' && typeof objectOrFn.referToSelf.callback === 'function';\n  }\n\n  /**\n   * Check if name is (A) new, (B) a match, or (C) a mismatch; and throw\n   * an error in case (C).\n   *\n   * @param { string | undefined } nameSoFar\n   * @param { string | undefined } newName\n   * @returns { string } updated name\n   */\n  function checkName(nameSoFar, newName) {\n    if (!nameSoFar) {\n      return newName;\n    }\n    if (newName && newName !== nameSoFar) {\n      const err = new Error('Function names do not match (expected: ' + nameSoFar + ', actual: ' + newName + ')');\n      err.data = {\n        actual: newName,\n        expected: nameSoFar\n      };\n      throw err;\n    }\n    return nameSoFar;\n  }\n\n  /**\n   * Retrieve the implied name from an object with signature keys\n   * and function values, checking whether all value names match\n   *\n   * @param { {string: function} } obj\n   */\n  function getObjectName(obj) {\n    let name;\n    for (const key in obj) {\n      // Only pay attention to own properties, and only if their values\n      // are typed functions or functions with a signature property\n      if (Object.prototype.hasOwnProperty.call(obj, key) && (isTypedFunction(obj[key]) || typeof obj[key].signature === 'string')) {\n        name = checkName(name, obj[key].name);\n      }\n    }\n    return name;\n  }\n\n  /**\n   * Copy all of the signatures from the second argument into the first,\n   * which is modified by side effect, checking for conflicts\n   *\n   * @param {Object.<string, function|typed-reference>} dest\n   * @param {Object.<string, function|typed-reference>} source\n   */\n  function mergeSignatures(dest, source) {\n    let key;\n    for (key in source) {\n      if (Object.prototype.hasOwnProperty.call(source, key)) {\n        if (key in dest) {\n          if (source[key] !== dest[key]) {\n            const err = new Error('Signature \"' + key + '\" is defined twice');\n            err.data = {\n              signature: key,\n              sourceFunction: source[key],\n              destFunction: dest[key]\n            };\n            throw err;\n          }\n          // else: both signatures point to the same function, that's fine\n        }\n        dest[key] = source[key];\n      }\n    }\n  }\n  const saveTyped = typed;\n\n  /**\n   * Originally the main function was a typed function itself, but then\n   * it might not be able to generate error messages if the client\n   * replaced the type system with different names.\n   *\n   * Main entry: typed([name], functions/objects with signatures...)\n   *\n   * Assembles and returns a new typed-function from the given items\n   * that provide signatures and implementations, each of which may be\n   * * a plain object mapping (string) signatures to implementing functions,\n   * * a previously constructed typed function, or\n   * * any other single function with a string-valued property `signature`.\n    * The name of the resulting typed-function will be given by the\n   * string-valued name argument if present, or if not, by the name\n   * of any of the arguments that have one, as long as any that do are\n   * consistent with each other. If no name is specified, the name will be\n   * an empty string.\n   *\n   * @param {string} maybeName [optional]\n   * @param {(function|object)[]} signature providers\n   * @returns {typed-function}\n   */\n  typed = function (maybeName) {\n    const named = typeof maybeName === 'string';\n    const start = named ? 1 : 0;\n    let name = named ? maybeName : '';\n    const allSignatures = {};\n    for (let i = start; i < arguments.length; ++i) {\n      const item = arguments[i];\n      let theseSignatures = {};\n      let thisName;\n      if (typeof item === 'function') {\n        thisName = item.name;\n        if (typeof item.signature === 'string') {\n          // Case 1: Ordinary function with a string 'signature' property\n          theseSignatures[item.signature] = item;\n        } else if (isTypedFunction(item)) {\n          // Case 2: Existing typed function\n          theseSignatures = item.signatures;\n        }\n      } else if (isPlainObject(item)) {\n        // Case 3: Plain object, assume keys = signatures, values = functions\n        theseSignatures = item;\n        if (!named) {\n          thisName = getObjectName(item);\n        }\n      }\n      if (Object.keys(theseSignatures).length === 0) {\n        const err = new TypeError('Argument to \\'typed\\' at index ' + i + ' is not a (typed) function, ' + 'nor an object with signatures as keys and functions as values.');\n        err.data = {\n          index: i,\n          argument: item\n        };\n        throw err;\n      }\n      if (!named) {\n        name = checkName(name, thisName);\n      }\n      mergeSignatures(allSignatures, theseSignatures);\n    }\n    return createTypedFunction(name || '', allSignatures);\n  };\n  typed.create = create;\n  typed.createCount = saveTyped.createCount;\n  typed.onMismatch = _onMismatch;\n  typed.throwMismatchError = _onMismatch;\n  typed.createError = createError;\n  typed.clear = clear;\n  typed.clearConversions = clearConversions;\n  typed.addTypes = addTypes;\n  typed._findType = findType; // For unit testing only\n  typed.referTo = referTo;\n  typed.referToSelf = referToSelf;\n  typed.convert = convert;\n  typed.findSignature = findSignature;\n  typed.find = find;\n  typed.isTypedFunction = isTypedFunction;\n  typed.warnAgainstDeprecatedThis = true;\n\n  /**\n   * add a type (convenience wrapper for typed.addTypes)\n   * @param {{name: string, test: function}} type\n   * @param {boolean} [beforeObjectTest=true]\n   *                          If true, the new test will be inserted before\n   *                          the test with name 'Object' (if any), since\n   *                          tests for Object match Array and classes too.\n   */\n  typed.addType = function (type, beforeObjectTest) {\n    let before = 'any';\n    if (beforeObjectTest !== false && typeMap.has('Object')) {\n      before = 'Object';\n    }\n    typed.addTypes([type], before);\n  };\n\n  /**\n   * Verify that the ConversionDef conversion has a valid format.\n   *\n   * @param {conversionDef} conversion\n   * @return {void}\n   * @throws {TypeError|SyntaxError}\n   */\n  function _validateConversion(conversion) {\n    if (!conversion || typeof conversion.from !== 'string' || typeof conversion.to !== 'string' || typeof conversion.convert !== 'function') {\n      throw new TypeError('Object with properties {from: string, to: string, convert: function} expected');\n    }\n    if (conversion.to === conversion.from) {\n      throw new SyntaxError('Illegal to define conversion from \"' + conversion.from + '\" to itself.');\n    }\n  }\n\n  /**\n   * Add a conversion\n   *\n   * @param {ConversionDef} conversion\n   * @param {{override: boolean}} [options]\n   * @returns {void}\n   * @throws {TypeError}\n   */\n  typed.addConversion = function (conversion) {\n    let options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {\n      override: false\n    };\n    _validateConversion(conversion);\n    const to = findType(conversion.to);\n    const existing = to.conversionsTo.find(other => other.from === conversion.from);\n    if (existing) {\n      if (options && options.override) {\n        typed.removeConversion({\n          from: existing.from,\n          to: conversion.to,\n          convert: existing.convert\n        });\n      } else {\n        throw new Error('There is already a conversion from \"' + conversion.from + '\" to \"' + to.name + '\"');\n      }\n    }\n    to.conversionsTo.push({\n      from: conversion.from,\n      convert: conversion.convert,\n      index: nConversions++\n    });\n  };\n\n  /**\n   * Convenience wrapper to call addConversion on each conversion in a list.\n   *\n   * @param {ConversionDef[]} conversions\n   * @param {{override: boolean}} [options]\n   * @returns {void}\n   * @throws {TypeError}\n   */\n  typed.addConversions = function (conversions, options) {\n    conversions.forEach(conversion => typed.addConversion(conversion, options));\n  };\n\n  /**\n   * Remove the specified conversion. The format is the same as for\n   * addConversion, and the convert function must match or an error\n   * is thrown.\n   *\n   * @param {{from: string, to: string, convert: function}} conversion\n   * @returns {void}\n   * @throws {TypeError|SyntaxError|Error}\n   */\n  typed.removeConversion = function (conversion) {\n    _validateConversion(conversion);\n    const to = findType(conversion.to);\n    const existingConversion = findInArray(to.conversionsTo, c => c.from === conversion.from);\n    if (!existingConversion) {\n      throw new Error('Attempt to remove nonexistent conversion from ' + conversion.from + ' to ' + conversion.to);\n    }\n    if (existingConversion.convert !== conversion.convert) {\n      throw new Error('Conversion to remove does not match existing conversion');\n    }\n    const index = to.conversionsTo.indexOf(existingConversion);\n    to.conversionsTo.splice(index, 1);\n  };\n\n  /**\n   * Produce the specific signature that a typed function\n   * will execute on the given arguments. Here, a \"signature\" is an\n   * object with properties 'params', 'test', 'fn', and 'implementation'.\n   * This last property is a function that converts params as necessary\n   * and then calls 'fn'. Returns null if there is no matching signature.\n   * @param {typed-function} tf\n   * @param {any[]} argList\n   * @returns {{params: string, test: function, fn: function, implementation: function}}\n   */\n  typed.resolve = function (tf, argList) {\n    if (!isTypedFunction(tf)) {\n      throw new TypeError(NOT_TYPED_FUNCTION);\n    }\n    const sigs = tf._typedFunctionData.signatures;\n    for (let i = 0; i < sigs.length; ++i) {\n      if (sigs[i].test(argList)) {\n        return sigs[i];\n      }\n    }\n    return null;\n  };\n  return typed;\n}\nexport default create();\n//# sourceMappingURL=typed-function.mjs.map"], "names": [], "mappings": ";;;;;;EAAA,SAAS,EAAE,GAAG;EACd,EAAE,OAAO,IAAI,CAAC;EACd,CAAC;EACD,SAAS,KAAK,GAAG;EACjB,EAAE,OAAO,KAAK,CAAC;EACf,CAAC;EACD,SAAS,KAAK,GAAG;EACjB,EAAE,OAAO,SAAS,CAAC;EACnB,CAAC;EACD,MAAM,kBAAkB,GAAG,mCAAmC,CAAC;AAC/D;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AACA;EACA;EACA;EACA;EACA,SAAS,MAAM,GAAG;EAClB;AACA;EACA;EACA;EACA;EACA,EAAE,SAAS,aAAa,CAAC,CAAC,EAAE;EAC5B,IAAI,OAAO,OAAO,CAAC,KAAK,QAAQ,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,CAAC,WAAW,KAAK,MAAM,CAAC;EAC3E,GAAG;EACH,EAAE,MAAM,MAAM,GAAG,CAAC;EAClB,IAAI,IAAI,EAAE,QAAQ;EAClB,IAAI,IAAI,EAAE,UAAU,CAAC,EAAE;EACvB,MAAM,OAAO,OAAO,CAAC,KAAK,QAAQ,CAAC;EACnC,KAAK;EACL,GAAG,EAAE;EACL,IAAI,IAAI,EAAE,QAAQ;EAClB,IAAI,IAAI,EAAE,UAAU,CAAC,EAAE;EACvB,MAAM,OAAO,OAAO,CAAC,KAAK,QAAQ,CAAC;EACnC,KAAK;EACL,GAAG,EAAE;EACL,IAAI,IAAI,EAAE,SAAS;EACnB,IAAI,IAAI,EAAE,UAAU,CAAC,EAAE;EACvB,MAAM,OAAO,OAAO,CAAC,KAAK,SAAS,CAAC;EACpC,KAAK;EACL,GAAG,EAAE;EACL,IAAI,IAAI,EAAE,UAAU;EACpB,IAAI,IAAI,EAAE,UAAU,CAAC,EAAE;EACvB,MAAM,OAAO,OAAO,CAAC,KAAK,UAAU,CAAC;EACrC,KAAK;EACL,GAAG,EAAE;EACL,IAAI,IAAI,EAAE,OAAO;EACjB,IAAI,IAAI,EAAE,KAAK,CAAC,OAAO;EACvB,GAAG,EAAE;EACL,IAAI,IAAI,EAAE,MAAM;EAChB,IAAI,IAAI,EAAE,UAAU,CAAC,EAAE;EACvB,MAAM,OAAO,CAAC,YAAY,IAAI,CAAC;EAC/B,KAAK;EACL,GAAG,EAAE;EACL,IAAI,IAAI,EAAE,QAAQ;EAClB,IAAI,IAAI,EAAE,UAAU,CAAC,EAAE;EACvB,MAAM,OAAO,CAAC,YAAY,MAAM,CAAC;EACjC,KAAK;EACL,GAAG,EAAE;EACL,IAAI,IAAI,EAAE,QAAQ;EAClB,IAAI,IAAI,EAAE,aAAa;EACvB,GAAG,EAAE;EACL,IAAI,IAAI,EAAE,MAAM;EAChB,IAAI,IAAI,EAAE,UAAU,CAAC,EAAE;EACvB,MAAM,OAAO,CAAC,KAAK,IAAI,CAAC;EACxB,KAAK;EACL,GAAG,EAAE;EACL,IAAI,IAAI,EAAE,WAAW;EACrB,IAAI,IAAI,EAAE,UAAU,CAAC,EAAE;EACvB,MAAM,OAAO,CAAC,KAAK,SAAS,CAAC;EAC7B,KAAK;EACL,GAAG,CAAC,CAAC;EACL,EAAE,MAAM,OAAO,GAAG;EAClB,IAAI,IAAI,EAAE,KAAK;EACf,IAAI,IAAI,EAAE,EAAE;EACZ,IAAI,KAAK,EAAE,IAAI;EACf,GAAG,CAAC;AACJ;EACA;EACA;EACA;EACA;EACA;EACA,EAAE,IAAI,OAAO,CAAC;EACd,EAAE,IAAI,QAAQ,CAAC;AACf;EACA;EACA,EAAE,IAAI,YAAY,GAAG,CAAC,CAAC;EACvB;AACA;EACA;EACA,EAAE,IAAI,KAAK,GAAG;EACd,IAAI,WAAW,EAAE,CAAC;EAClB,GAAG,CAAC;AACJ;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,EAAE,SAAS,QAAQ,CAAC,QAAQ,EAAE;EAC9B,IAAI,MAAM,IAAI,GAAG,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;EACvC,IAAI,IAAI,IAAI,EAAE;EACd,MAAM,OAAO,IAAI,CAAC;EAClB,KAAK;EACL;EACA,IAAI,IAAI,OAAO,GAAG,gBAAgB,GAAG,QAAQ,GAAG,GAAG,CAAC;EACpD,IAAI,MAAM,IAAI,GAAG,QAAQ,CAAC,WAAW,EAAE,CAAC;EACxC,IAAI,IAAI,SAAS,CAAC;EAClB,IAAI,KAAK,SAAS,IAAI,QAAQ,EAAE;EAChC,MAAM,IAAI,SAAS,CAAC,WAAW,EAAE,KAAK,IAAI,EAAE;EAC5C,QAAQ,OAAO,IAAI,kBAAkB,GAAG,SAAS,GAAG,KAAK,CAAC;EAC1D,QAAQ,MAAM;EACd,OAAO;EACP,KAAK;EACL,IAAI,MAAM,IAAI,SAAS,CAAC,OAAO,CAAC,CAAC;EACjC,GAAG;AACH;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,EAAE,SAAS,QAAQ,CAAC,KAAK,EAAE;EAC3B,IAAI,IAAI,UAAU,GAAG,SAAS,CAAC,MAAM,GAAG,CAAC,IAAI,SAAS,CAAC,CAAC,CAAC,KAAK,SAAS,GAAG,SAAS,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC;EAC/F,IAAI,MAAM,WAAW,GAAG,UAAU,GAAG,QAAQ,CAAC,UAAU,CAAC,CAAC,KAAK,GAAG,QAAQ,CAAC,MAAM,CAAC;EAClF,IAAI,MAAM,QAAQ,GAAG,EAAE,CAAC;EACxB,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE;EAC3C,MAAM,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,OAAO,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,QAAQ,IAAI,OAAO,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,UAAU,EAAE;EACjG,QAAQ,MAAM,IAAI,SAAS,CAAC,gEAAgE,CAAC,CAAC;EAC9F,OAAO;EACP,MAAM,MAAM,QAAQ,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;EACrC,MAAM,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE;EACjC,QAAQ,MAAM,IAAI,SAAS,CAAC,uBAAuB,GAAG,QAAQ,GAAG,GAAG,CAAC,CAAC;EACtE,OAAO;EACP,MAAM,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;EAC9B,MAAM,OAAO,CAAC,GAAG,CAAC,QAAQ,EAAE;EAC5B,QAAQ,IAAI,EAAE,QAAQ;EACtB,QAAQ,IAAI,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI;EAC3B,QAAQ,KAAK,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK;EAC7B,QAAQ,KAAK,EAAE,WAAW,GAAG,CAAC;EAC9B,QAAQ,aAAa,EAAE,EAAE;EACzB,OAAO,CAAC,CAAC;EACT,KAAK;EACL;EACA,IAAI,MAAM,aAAa,GAAG,QAAQ,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC;EACtD,IAAI,QAAQ,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC;EACrF;EACA,IAAI,KAAK,IAAI,CAAC,GAAG,WAAW,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE;EAC1E,MAAM,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC;EACzC,KAAK;EACL,GAAG;AACH;EACA;EACA;EACA;EACA;EACA;EACA;EACA,EAAE,SAAS,KAAK,GAAG;EACnB,IAAI,OAAO,GAAG,IAAI,GAAG,EAAE,CAAC;EACxB,IAAI,QAAQ,GAAG,EAAE,CAAC;EAClB,IAAI,YAAY,GAAG,CAAC,CAAC;EACrB,IAAI,QAAQ,CAAC,CAAC,OAAO,CAAC,EAAE,KAAK,CAAC,CAAC;EAC/B,GAAG;AACH;EACA;EACA,EAAE,KAAK,EAAE,CAAC;EACV,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC;AACnB;EACA;EACA;EACA;EACA,EAAE,SAAS,gBAAgB,GAAG;EAC9B,IAAI,IAAI,QAAQ,CAAC;EACjB,IAAI,KAAK,QAAQ,IAAI,QAAQ,EAAE;EAC/B,MAAM,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,aAAa,GAAG,EAAE,CAAC;EAC/C,KAAK;EACL,IAAI,YAAY,GAAG,CAAC,CAAC;EACrB,GAAG;AACH;EACA;EACA;EACA;EACA;EACA;EACA;EACA,EAAE,SAAS,aAAa,CAAC,KAAK,EAAE;EAChC,IAAI,MAAM,OAAO,GAAG,QAAQ,CAAC,MAAM,CAAC,IAAI,IAAI;EAC5C,MAAM,MAAM,IAAI,GAAG,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;EACrC,MAAM,OAAO,CAAC,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;EAC7C,KAAK,CAAC,CAAC;EACP,IAAI,IAAI,OAAO,CAAC,MAAM,EAAE;EACxB,MAAM,OAAO,OAAO,CAAC;EACrB,KAAK;EACL,IAAI,OAAO,CAAC,KAAK,CAAC,CAAC;EACnB,GAAG;AACH;EACA;EACA;EACA;EACA;EACA;EACA,EAAE,SAAS,eAAe,CAAC,MAAM,EAAE;EACnC,IAAI,OAAO,MAAM,IAAI,OAAO,MAAM,KAAK,UAAU,IAAI,oBAAoB,IAAI,MAAM,CAAC;EACpF,GAAG;AACH;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,EAAE,SAAS,aAAa,CAAC,EAAE,EAAE,SAAS,EAAE,OAAO,EAAE;EACjD,IAAI,IAAI,CAAC,eAAe,CAAC,EAAE,CAAC,EAAE;EAC9B,MAAM,MAAM,IAAI,SAAS,CAAC,kBAAkB,CAAC,CAAC;EAC9C,KAAK;AACL;EACA;EACA,IAAI,MAAM,KAAK,GAAG,OAAO,IAAI,OAAO,CAAC,KAAK,CAAC;EAC3C,IAAI,MAAM,eAAe,GAAG,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,GAAG,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,SAAS,CAAC;EACvF,IAAI,MAAM,MAAM,GAAG,cAAc,CAAC,eAAe,CAAC,CAAC;EACnD,IAAI,MAAM,kBAAkB,GAAG,eAAe,CAAC,MAAM,CAAC,CAAC;AACvD;EACA;EACA,IAAI,IAAI,CAAC,KAAK,IAAI,kBAAkB,IAAI,EAAE,CAAC,UAAU,EAAE;EACvD;EACA,MAAM,MAAM,KAAK,GAAG,EAAE,CAAC,kBAAkB,CAAC,YAAY,CAAC,GAAG,CAAC,kBAAkB,CAAC,CAAC;EAC/E,MAAM,IAAI,KAAK,EAAE;EACjB,QAAQ,OAAO,KAAK,CAAC;EACrB,OAAO;EACP,KAAK;AACL;EACA;EACA;EACA;EACA;EACA,IAAI,MAAM,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC;EAClC,IAAI,IAAI,mBAAmB,CAAC;EAC5B,IAAI,IAAI,KAAK,EAAE;EACf,MAAM,mBAAmB,GAAG,EAAE,CAAC;EAC/B,MAAM,IAAI,IAAI,CAAC;EACf,MAAM,KAAK,IAAI,IAAI,EAAE,CAAC,UAAU,EAAE;EAClC,QAAQ,mBAAmB,CAAC,IAAI,CAAC,EAAE,CAAC,kBAAkB,CAAC,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC;EAC/E,OAAO;EACP,KAAK,MAAM;EACX,MAAM,mBAAmB,GAAG,EAAE,CAAC,kBAAkB,CAAC,UAAU,CAAC;EAC7D,KAAK;EACL,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,EAAE,EAAE,CAAC,EAAE;EACtC,MAAM,MAAM,IAAI,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;EAC7B,MAAM,MAAM,kBAAkB,GAAG,EAAE,CAAC;EACpC,MAAM,IAAI,WAAW,CAAC;EACtB,MAAM,KAAK,WAAW,IAAI,mBAAmB,EAAE;EAC/C,QAAQ,MAAM,IAAI,GAAG,eAAe,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;EAC5D,QAAQ,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;EACxD,UAAU,SAAS;EACnB,SAAS;EACT,QAAQ,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;EAC1B;EACA,UAAU,MAAM,SAAS,GAAG,YAAY,CAAC,IAAI,CAAC,CAAC;EAC/C,UAAU,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,EAAE;EACpE,YAAY,SAAS;EACrB,WAAW;EACX,SAAS;EACT;EACA,QAAQ,kBAAkB,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;EAC7C,OAAO;EACP,MAAM,mBAAmB,GAAG,kBAAkB,CAAC;EAC/C,MAAM,IAAI,mBAAmB,CAAC,MAAM,KAAK,CAAC,EAAE,MAAM;EAClD,KAAK;EACL;EACA,IAAI,IAAI,SAAS,CAAC;EAClB,IAAI,KAAK,SAAS,IAAI,mBAAmB,EAAE;EAC3C,MAAM,IAAI,SAAS,CAAC,MAAM,CAAC,MAAM,IAAI,OAAO,EAAE;EAC9C,QAAQ,OAAO,SAAS,CAAC;EACzB,OAAO;EACP,KAAK;EACL,IAAI,MAAM,IAAI,SAAS,CAAC,kCAAkC,IAAI,EAAE,CAAC,IAAI,IAAI,SAAS,CAAC,GAAG,GAAG,GAAG,eAAe,CAAC,MAAM,EAAE,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC;EAClI,GAAG;AACH;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,EAAE,SAAS,IAAI,CAAC,EAAE,EAAE,SAAS,EAAE,OAAO,EAAE;EACxC,IAAI,OAAO,aAAa,CAAC,EAAE,EAAE,SAAS,EAAE,OAAO,CAAC,CAAC,cAAc,CAAC;EAChE,GAAG;AACH;EACA;EACA;EACA;EACA;EACA;EACA;EACA,EAAE,SAAS,OAAO,CAAC,KAAK,EAAE,QAAQ,EAAE;EACpC;EACA,IAAI,MAAM,IAAI,GAAG,QAAQ,CAAC,QAAQ,CAAC,CAAC;EACpC,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;EAC1B,MAAM,OAAO,KAAK,CAAC;EACnB,KAAK;EACL,IAAI,MAAM,WAAW,GAAG,IAAI,CAAC,aAAa,CAAC;EAC3C,IAAI,IAAI,WAAW,CAAC,MAAM,KAAK,CAAC,EAAE;EAClC,MAAM,MAAM,IAAI,KAAK,CAAC,8BAA8B,GAAG,QAAQ,GAAG,WAAW,CAAC,CAAC;EAC/E,KAAK;EACL,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,WAAW,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;EACjD,MAAM,MAAM,QAAQ,GAAG,QAAQ,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;EACrD,MAAM,IAAI,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;EAChC,QAAQ,OAAO,WAAW,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;EAC7C,OAAO;EACP,KAAK;EACL,IAAI,MAAM,IAAI,KAAK,CAAC,iBAAiB,GAAG,KAAK,GAAG,MAAM,GAAG,QAAQ,CAAC,CAAC;EACnE,GAAG;AACH;EACA;EACA;EACA;EACA;EACA;EACA;EACA,EAAE,SAAS,eAAe,CAAC,MAAM,EAAE;EACnC,IAAI,IAAI,SAAS,GAAG,SAAS,CAAC,MAAM,GAAG,CAAC,IAAI,SAAS,CAAC,CAAC,CAAC,KAAK,SAAS,GAAG,SAAS,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC;EAC5F,IAAI,OAAO,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;EACnD,GAAG;AACH;EACA;EACA;EACA;EACA;EACA;EACA,EAAE,SAAS,UAAU,CAAC,KAAK,EAAE;EAC7B,IAAI,MAAM,SAAS,GAAG,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;EACjD,IAAI,MAAM,KAAK,GAAG,CAAC,SAAS,GAAG,KAAK,GAAG,KAAK,CAAC,MAAM,GAAG,CAAC,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC;EACjF,IAAI,MAAM,QAAQ,GAAG,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,QAAQ,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;EACnE,IAAI,IAAI,MAAM,GAAG,KAAK,CAAC;EACvB,IAAI,IAAI,SAAS,GAAG,SAAS,GAAG,KAAK,GAAG,EAAE,CAAC;EAC3C,IAAI,MAAM,UAAU,GAAG,QAAQ,CAAC,GAAG,CAAC,UAAU,IAAI,EAAE;EACpD,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,IAAI,MAAM,CAAC;EACpC,MAAM,SAAS,IAAI,IAAI,CAAC,IAAI,GAAG,GAAG,CAAC;EACnC,MAAM,OAAO;EACb,QAAQ,IAAI,EAAE,IAAI,CAAC,IAAI;EACvB,QAAQ,SAAS,EAAE,IAAI,CAAC,KAAK;EAC7B,QAAQ,IAAI,EAAE,IAAI,CAAC,IAAI;EACvB,QAAQ,KAAK,EAAE,IAAI,CAAC,KAAK;EACzB,QAAQ,UAAU,EAAE,IAAI;EACxB,QAAQ,eAAe,EAAE,CAAC,CAAC;EAC3B,OAAO,CAAC;EACR,KAAK,CAAC,CAAC;EACP,IAAI,OAAO;EACX,MAAM,KAAK,EAAE,UAAU;EACvB,MAAM,IAAI,EAAE,SAAS,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EAClC;EACA,MAAM,MAAM;EACZ,MAAM,aAAa,EAAE,KAAK;EAC1B,MAAM,SAAS;EACf,KAAK,CAAC;EACN,GAAG;AACH;EACA;EACA;EACA;EACA;EACA;EACA;EACA,EAAE,SAAS,WAAW,CAAC,KAAK,EAAE;EAC9B,IAAI,MAAM,SAAS,GAAG,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC;EACnD,IAAI,MAAM,mBAAmB,GAAG,oBAAoB,CAAC,SAAS,CAAC,CAAC;EAChE,IAAI,IAAI,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC;EAC9B,IAAI,IAAI,OAAO,GAAG,KAAK,CAAC,IAAI,CAAC;EAC7B,IAAI,MAAM,gBAAgB,GAAG,mBAAmB,CAAC,GAAG,CAAC,UAAU,UAAU,EAAE;EAC3E,MAAM,MAAM,IAAI,GAAG,QAAQ,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;EAC7C,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,IAAI,MAAM,CAAC;EACpC,MAAM,OAAO,IAAI,GAAG,GAAG,UAAU,CAAC,IAAI,CAAC;EACvC,MAAM,OAAO;EACb,QAAQ,IAAI,EAAE,UAAU,CAAC,IAAI;EAC7B,QAAQ,SAAS,EAAE,IAAI,CAAC,KAAK;EAC7B,QAAQ,IAAI,EAAE,IAAI,CAAC,IAAI;EACvB,QAAQ,KAAK,EAAE,IAAI,CAAC,KAAK;EACzB,QAAQ,UAAU;EAClB,QAAQ,eAAe,EAAE,UAAU,CAAC,KAAK;EACzC,OAAO,CAAC;EACR,KAAK,CAAC,CAAC;EACP,IAAI,OAAO;EACX,MAAM,KAAK,EAAE,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC,gBAAgB,CAAC;EACjD,MAAM,IAAI,EAAE,OAAO;EACnB,MAAM,MAAM;EACZ,MAAM,aAAa,EAAE,gBAAgB,CAAC,MAAM,GAAG,CAAC;EAChD,MAAM,SAAS,EAAE,KAAK,CAAC,SAAS;EAChC,KAAK,CAAC;EACN,GAAG;AACH;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,EAAE,SAAS,YAAY,CAAC,KAAK,EAAE;EAC/B,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE;EACxB,MAAM,KAAK,CAAC,OAAO,GAAG,IAAI,GAAG,EAAE,CAAC;EAChC,MAAM,KAAK,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,IAAI,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;EAChE,KAAK;EACL,IAAI,OAAO,KAAK,CAAC,OAAO,CAAC;EACzB,GAAG;AACH;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,EAAE,SAAS,cAAc,CAAC,YAAY,EAAE;EACxC,IAAI,MAAM,MAAM,GAAG,EAAE,CAAC;EACtB,IAAI,IAAI,OAAO,YAAY,KAAK,QAAQ,EAAE;EAC1C,MAAM,MAAM,IAAI,SAAS,CAAC,4BAA4B,CAAC,CAAC;EACxD,KAAK;EACL,IAAI,MAAM,SAAS,GAAG,YAAY,CAAC,IAAI,EAAE,CAAC;EAC1C,IAAI,IAAI,SAAS,KAAK,EAAE,EAAE;EAC1B,MAAM,OAAO,MAAM,CAAC;EACpB,KAAK;EACL,IAAI,MAAM,SAAS,GAAG,SAAS,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;EAC3C,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE;EAC/C,MAAM,MAAM,WAAW,GAAG,UAAU,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC;EAC1D,MAAM,IAAI,WAAW,CAAC,SAAS,IAAI,CAAC,KAAK,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE;EAC/D,QAAQ,MAAM,IAAI,WAAW,CAAC,6BAA6B,GAAG,SAAS,CAAC,CAAC,CAAC,GAAG,KAAK,GAAG,qCAAqC,CAAC,CAAC;EAC5H,OAAO;EACP;EACA,MAAM,IAAI,WAAW,CAAC,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE;EAC1C,QAAQ,OAAO,IAAI,CAAC;EACpB,OAAO;EACP,MAAM,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;EAC/B,KAAK;EACL,IAAI,OAAO,MAAM,CAAC;EAClB,GAAG;AACH;EACA;EACA;EACA;EACA;EACA;EACA,EAAE,SAAS,YAAY,CAAC,MAAM,EAAE;EAChC,IAAI,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC;EAC/B,IAAI,OAAO,KAAK,GAAG,KAAK,CAAC,SAAS,GAAG,KAAK,CAAC;EAC3C,GAAG;AACH;EACA;EACA;EACA;EACA;EACA;EACA;EACA,EAAE,SAAS,WAAW,CAAC,KAAK,EAAE;EAC9B,IAAI,IAAI,CAAC,KAAK,IAAI,KAAK,CAAC,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE;EAC5C;EACA,MAAM,OAAO,EAAE,CAAC;EAChB,KAAK,MAAM,IAAI,KAAK,CAAC,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE;EACzC,MAAM,OAAO,QAAQ,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC;EAChD,KAAK,MAAM,IAAI,KAAK,CAAC,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE;EACzC,MAAM,MAAM,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC;EACvD,MAAM,MAAM,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC;EACvD,MAAM,OAAO,SAAS,EAAE,CAAC,CAAC,EAAE;EAC5B,QAAQ,OAAO,KAAK,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC;EACpC,OAAO,CAAC;EACR,KAAK,MAAM;EACX;EACA,MAAM,MAAM,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,UAAU,IAAI,EAAE;EACpD,QAAQ,OAAO,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC;EACxC,OAAO,CAAC,CAAC;EACT,MAAM,OAAO,SAAS,EAAE,CAAC,CAAC,EAAE;EAC5B,QAAQ,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;EAC/C,UAAU,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EAC3B,YAAY,OAAO,IAAI,CAAC;EACxB,WAAW;EACX,SAAS;EACT,QAAQ,OAAO,KAAK,CAAC;EACrB,OAAO,CAAC;EACR,KAAK;EACL,GAAG;AACH;EACA;EACA;EACA;EACA;EACA;EACA,EAAE,SAAS,YAAY,CAAC,MAAM,EAAE;EAChC,IAAI,IAAI,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;EAC5B,IAAI,IAAI,YAAY,CAAC,MAAM,CAAC,EAAE;EAC9B;EACA,MAAM,KAAK,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;EAC/C,MAAM,MAAM,QAAQ,GAAG,KAAK,CAAC,MAAM,CAAC;EACpC,MAAM,MAAM,QAAQ,GAAG,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;EACjD,MAAM,MAAM,aAAa,GAAG,UAAU,IAAI,EAAE;EAC5C,QAAQ,KAAK,IAAI,CAAC,GAAG,QAAQ,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;EACrD,UAAU,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE;EAClC,YAAY,OAAO,KAAK,CAAC;EACzB,WAAW;EACX,SAAS;EACT,QAAQ,OAAO,IAAI,CAAC;EACpB,OAAO,CAAC;EACR,MAAM,OAAO,SAAS,QAAQ,CAAC,IAAI,EAAE;EACrC,QAAQ,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;EAC/C,UAAU,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE;EAClC,YAAY,OAAO,KAAK,CAAC;EACzB,WAAW;EACX,SAAS;EACT,QAAQ,OAAO,aAAa,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,MAAM,IAAI,QAAQ,GAAG,CAAC,CAAC;EAClE,OAAO,CAAC;EACR,KAAK,MAAM;EACX;EACA,MAAM,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE;EAC/B,QAAQ,OAAO,SAAS,QAAQ,CAAC,IAAI,EAAE;EACvC,UAAU,OAAO,IAAI,CAAC,MAAM,KAAK,CAAC,CAAC;EACnC,SAAS,CAAC;EACV,OAAO,MAAM,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE;EACtC,QAAQ,KAAK,GAAG,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;EACvC,QAAQ,OAAO,SAAS,QAAQ,CAAC,IAAI,EAAE;EACvC,UAAU,OAAO,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,CAAC;EACrD,SAAS,CAAC;EACV,OAAO,MAAM,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE;EACtC,QAAQ,KAAK,GAAG,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;EACvC,QAAQ,KAAK,GAAG,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;EACvC,QAAQ,OAAO,SAAS,QAAQ,CAAC,IAAI,EAAE;EACvC,UAAU,OAAO,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,CAAC;EACvE,SAAS,CAAC;EACV,OAAO,MAAM;EACb;EACA,QAAQ,KAAK,GAAG,MAAM,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;EACxC,QAAQ,OAAO,SAAS,QAAQ,CAAC,IAAI,EAAE;EACvC,UAAU,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;EACjD,YAAY,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE;EACpC,cAAc,OAAO,KAAK,CAAC;EAC3B,aAAa;EACb,WAAW;EACX,UAAU,OAAO,IAAI,CAAC,MAAM,KAAK,KAAK,CAAC,MAAM,CAAC;EAC9C,SAAS,CAAC;EACV,OAAO;EACP,KAAK;EACL,GAAG;AACH;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,EAAE,SAAS,eAAe,CAAC,MAAM,EAAE,KAAK,EAAE;EAC1C,IAAI,OAAO,KAAK,GAAG,MAAM,CAAC,MAAM,GAAG,MAAM,CAAC,KAAK,CAAC,GAAG,YAAY,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC;EAC9F,GAAG;AACH;EACA;EACA;EACA;EACA;EACA;EACA;EACA,EAAE,SAAS,iBAAiB,CAAC,MAAM,EAAE,KAAK,EAAE;EAC5C,IAAI,MAAM,KAAK,GAAG,eAAe,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;EACjD,IAAI,IAAI,CAAC,KAAK,EAAE;EAChB,MAAM,OAAO,IAAI,GAAG,EAAE,CAAC;EACvB,KAAK;EACL,IAAI,OAAO,YAAY,CAAC,KAAK,CAAC,CAAC;EAC/B,GAAG;AACH;EACA;EACA;EACA;EACA;EACA;EACA,EAAE,SAAS,WAAW,CAAC,IAAI,EAAE;EAC7B,IAAI,OAAO,IAAI,CAAC,UAAU,KAAK,IAAI,IAAI,IAAI,CAAC,UAAU,KAAK,SAAS,CAAC;EACrE,GAAG;AACH;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,EAAE,SAAS,mBAAmB,CAAC,UAAU,EAAE,KAAK,EAAE;EAClD,IAAI,MAAM,OAAO,GAAG,IAAI,GAAG,EAAE,CAAC;EAC9B,IAAI,UAAU,CAAC,OAAO,CAAC,SAAS,IAAI;EACpC,MAAM,MAAM,QAAQ,GAAG,iBAAiB,CAAC,SAAS,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;EAClE,MAAM,IAAI,IAAI,CAAC;EACf,MAAM,KAAK,IAAI,IAAI,QAAQ,EAAE;EAC7B,QAAQ,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;EAC1B,OAAO;EACP,KAAK,CAAC,CAAC;EACP,IAAI,OAAO,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;EAC9D,GAAG;AACH;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,EAAE,SAAS,WAAW,CAAC,IAAI,EAAE,IAAI,EAAE,UAAU,EAAE;EAC/C,IAAI,IAAI,GAAG,EAAE,QAAQ,CAAC;EACtB,IAAI,MAAM,KAAK,GAAG,IAAI,IAAI,SAAS,CAAC;AACpC;EACA;EACA,IAAI,IAAI,kBAAkB,GAAG,UAAU,CAAC;EACxC,IAAI,IAAI,KAAK,CAAC;EACd,IAAI,KAAK,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;EAClD,MAAM,MAAM,gBAAgB,GAAG,EAAE,CAAC;EAClC,MAAM,kBAAkB,CAAC,OAAO,CAAC,SAAS,IAAI;EAC9C,QAAQ,MAAM,KAAK,GAAG,eAAe,CAAC,SAAS,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;EAC/D,QAAQ,MAAM,IAAI,GAAG,WAAW,CAAC,KAAK,CAAC,CAAC;EACxC,QAAQ,IAAI,CAAC,KAAK,GAAG,SAAS,CAAC,MAAM,CAAC,MAAM,IAAI,YAAY,CAAC,SAAS,CAAC,MAAM,CAAC,KAAK,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE;EACtG,UAAU,gBAAgB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;EAC3C,SAAS;EACT,OAAO,CAAC,CAAC;EACT,MAAM,IAAI,gBAAgB,CAAC,MAAM,KAAK,CAAC,EAAE;EACzC;EACA,QAAQ,QAAQ,GAAG,mBAAmB,CAAC,kBAAkB,EAAE,KAAK,CAAC,CAAC;EAClE,QAAQ,IAAI,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE;EACjC,UAAU,MAAM,WAAW,GAAG,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;EACzD,UAAU,GAAG,GAAG,IAAI,SAAS,CAAC,0CAA0C,GAAG,KAAK,GAAG,cAAc,GAAG,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,YAAY,GAAG,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,WAAW,GAAG,KAAK,GAAG,GAAG,CAAC,CAAC;EAChM,UAAU,GAAG,CAAC,IAAI,GAAG;EACrB,YAAY,QAAQ,EAAE,WAAW;EACjC,YAAY,EAAE,EAAE,KAAK;EACrB,YAAY,KAAK;EACjB,YAAY,MAAM,EAAE,WAAW;EAC/B,YAAY,QAAQ;EACpB,WAAW,CAAC;EACZ,UAAU,OAAO,GAAG,CAAC;EACrB,SAAS;EACT,OAAO,MAAM;EACb,QAAQ,kBAAkB,GAAG,gBAAgB,CAAC;EAC9C,OAAO;EACP,KAAK;AACL;EACA;EACA,IAAI,MAAM,OAAO,GAAG,kBAAkB,CAAC,GAAG,CAAC,UAAU,SAAS,EAAE;EAChE,MAAM,OAAO,YAAY,CAAC,SAAS,CAAC,MAAM,CAAC,GAAG,QAAQ,GAAG,SAAS,CAAC,MAAM,CAAC,MAAM,CAAC;EACjF,KAAK,CAAC,CAAC;EACP,IAAI,IAAI,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,EAAE,OAAO,CAAC,EAAE;EACrD,MAAM,QAAQ,GAAG,mBAAmB,CAAC,kBAAkB,EAAE,KAAK,CAAC,CAAC;EAChE,MAAM,GAAG,GAAG,IAAI,SAAS,CAAC,gCAAgC,GAAG,KAAK,GAAG,cAAc,GAAG,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,WAAW,GAAG,IAAI,CAAC,MAAM,GAAG,GAAG,CAAC,CAAC;EAC/I,MAAM,GAAG,CAAC,IAAI,GAAG;EACjB,QAAQ,QAAQ,EAAE,YAAY;EAC9B,QAAQ,EAAE,EAAE,KAAK;EACjB,QAAQ,KAAK,EAAE,IAAI,CAAC,MAAM;EAC1B,QAAQ,QAAQ;EAChB,OAAO,CAAC;EACR,MAAM,OAAO,GAAG,CAAC;EACjB,KAAK;AACL;EACA;EACA,IAAI,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;EACpD,IAAI,IAAI,IAAI,CAAC,MAAM,GAAG,SAAS,EAAE;EACjC,MAAM,GAAG,GAAG,IAAI,SAAS,CAAC,iCAAiC,GAAG,KAAK,GAAG,cAAc,GAAG,SAAS,GAAG,YAAY,GAAG,IAAI,CAAC,MAAM,GAAG,GAAG,CAAC,CAAC;EACrI,MAAM,GAAG,CAAC,IAAI,GAAG;EACjB,QAAQ,QAAQ,EAAE,aAAa;EAC/B,QAAQ,EAAE,EAAE,KAAK;EACjB,QAAQ,KAAK,EAAE,IAAI,CAAC,MAAM;EAC1B,QAAQ,cAAc,EAAE,SAAS;EACjC,OAAO,CAAC;EACR,MAAM,OAAO,GAAG,CAAC;EACjB,KAAK;AACL;EACA;EACA,IAAI,MAAM,QAAQ,GAAG,EAAE,CAAC;EACxB,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE;EAC1C,MAAM,QAAQ,CAAC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;EACtD,KAAK;EACL,IAAI,GAAG,GAAG,IAAI,SAAS,CAAC,qBAAqB,GAAG,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,2DAA2D,GAAG,KAAK,GAAG,GAAG,CAAC,CAAC;EACjJ,IAAI,GAAG,CAAC,IAAI,GAAG;EACf,MAAM,QAAQ,EAAE,UAAU;EAC1B,MAAM,MAAM,EAAE,QAAQ;EACtB,KAAK,CAAC;EACN,IAAI,OAAO,GAAG,CAAC;EACf,GAAG;AACH;EACA;EACA;EACA;EACA;EACA;EACA,EAAE,SAAS,kBAAkB,CAAC,KAAK,EAAE;EACrC,IAAI,IAAI,GAAG,GAAG,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC;EAClC,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;EACjD,MAAM,IAAI,WAAW,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE;EACvC,QAAQ,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC;EACtD,OAAO;EACP,KAAK;EACL,IAAI,OAAO,GAAG,CAAC;EACf,GAAG;AACH;EACA;EACA;EACA;EACA;EACA;EACA;EACA,EAAE,SAAS,wBAAwB,CAAC,KAAK,EAAE;EAC3C,IAAI,IAAI,GAAG,GAAG,YAAY,GAAG,CAAC,CAAC;EAC/B,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;EACjD,MAAM,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE;EACxC,QAAQ,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC;EAC5D,OAAO;EACP,KAAK;EACL,IAAI,OAAO,GAAG,CAAC;EACf,GAAG;AACH;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,EAAE,SAAS,aAAa,CAAC,MAAM,EAAE,MAAM,EAAE;EACzC;EACA;EACA,IAAI,IAAI,MAAM,CAAC,MAAM,EAAE;EACvB,MAAM,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE;EAC1B,QAAQ,OAAO,CAAC,CAAC;EACjB,OAAO;EACP,KAAK,MAAM,IAAI,MAAM,CAAC,MAAM,EAAE;EAC9B,MAAM,OAAO,CAAC,CAAC,CAAC;EAChB,KAAK;AACL;EACA;EACA,IAAI,IAAI,MAAM,CAAC,SAAS,EAAE;EAC1B,MAAM,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE;EAC7B,QAAQ,OAAO,CAAC,CAAC;EACjB,OAAO;EACP,KAAK,MAAM,IAAI,MAAM,CAAC,SAAS,EAAE;EACjC,MAAM,OAAO,CAAC,CAAC,CAAC;EAChB,KAAK;AACL;EACA;EACA,IAAI,IAAI,MAAM,CAAC,aAAa,EAAE;EAC9B,MAAM,IAAI,CAAC,MAAM,CAAC,aAAa,EAAE;EACjC,QAAQ,OAAO,CAAC,CAAC;EACjB,OAAO;EACP,KAAK,MAAM,IAAI,MAAM,CAAC,aAAa,EAAE;EACrC,MAAM,OAAO,CAAC,CAAC,CAAC;EAChB,KAAK;AACL;EACA;EACA,IAAI,MAAM,QAAQ,GAAG,kBAAkB,CAAC,MAAM,CAAC,GAAG,kBAAkB,CAAC,MAAM,CAAC,CAAC;EAC7E,IAAI,IAAI,QAAQ,GAAG,CAAC,EAAE;EACtB,MAAM,OAAO,CAAC,CAAC,CAAC;EAChB,KAAK;EACL,IAAI,IAAI,QAAQ,GAAG,CAAC,EAAE;EACtB,MAAM,OAAO,CAAC,CAAC;EACf,KAAK;AACL;EACA;EACA,IAAI,MAAM,QAAQ,GAAG,wBAAwB,CAAC,MAAM,CAAC,GAAG,wBAAwB,CAAC,MAAM,CAAC,CAAC;EACzF,IAAI,IAAI,QAAQ,GAAG,CAAC,EAAE;EACtB,MAAM,OAAO,CAAC,CAAC,CAAC;EAChB,KAAK;EACL,IAAI,IAAI,QAAQ,GAAG,CAAC,EAAE;EACtB,MAAM,OAAO,CAAC,CAAC;EACf,KAAK;AACL;EACA;EACA,IAAI,OAAO,CAAC,CAAC;EACb,GAAG;AACH;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,EAAE,SAAS,iBAAiB,CAAC,UAAU,EAAE,UAAU,EAAE;EACrD,IAAI,MAAM,KAAK,GAAG,UAAU,CAAC,MAAM,CAAC;EACpC,IAAI,MAAM,KAAK,GAAG,UAAU,CAAC,MAAM,CAAC;EACpC,IAAI,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC;EAC9B,IAAI,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC;EAC9B,IAAI,MAAM,QAAQ,GAAG,YAAY,CAAC,KAAK,CAAC,CAAC;EACzC,IAAI,MAAM,QAAQ,GAAG,YAAY,CAAC,KAAK,CAAC,CAAC;EACzC;EACA;EACA,IAAI,IAAI,QAAQ,IAAI,KAAK,CAAC,MAAM,EAAE;EAClC,MAAM,IAAI,CAAC,QAAQ,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE;EACtC,QAAQ,OAAO,CAAC,CAAC;EACjB,OAAO;EACP,KAAK,MAAM,IAAI,QAAQ,IAAI,KAAK,CAAC,MAAM,EAAE;EACzC,MAAM,OAAO,CAAC,CAAC,CAAC;EAChB,KAAK;AACL;EACA;EACA,IAAI,IAAI,IAAI,GAAG,CAAC,CAAC;EACjB,IAAI,IAAI,KAAK,GAAG,CAAC,CAAC;EAClB,IAAI,IAAI,GAAG,CAAC;EACZ,IAAI,KAAK,GAAG,IAAI,KAAK,EAAE;EACvB,MAAM,IAAI,GAAG,CAAC,MAAM,EAAE,EAAE,IAAI,CAAC;EAC7B,MAAM,IAAI,GAAG,CAAC,aAAa,EAAE,EAAE,KAAK,CAAC;EACrC,KAAK;EACL,IAAI,IAAI,IAAI,GAAG,CAAC,CAAC;EACjB,IAAI,IAAI,KAAK,GAAG,CAAC,CAAC;EAClB,IAAI,KAAK,GAAG,IAAI,KAAK,EAAE;EACvB,MAAM,IAAI,GAAG,CAAC,MAAM,EAAE,EAAE,IAAI,CAAC;EAC7B,MAAM,IAAI,GAAG,CAAC,aAAa,EAAE,EAAE,KAAK,CAAC;EACrC,KAAK;EACL,IAAI,IAAI,IAAI,KAAK,IAAI,EAAE;EACvB,MAAM,OAAO,IAAI,GAAG,IAAI,CAAC;EACzB,KAAK;AACL;EACA;EACA,IAAI,IAAI,QAAQ,IAAI,KAAK,CAAC,aAAa,EAAE;EACzC,MAAM,IAAI,CAAC,QAAQ,IAAI,CAAC,KAAK,CAAC,aAAa,EAAE;EAC7C,QAAQ,OAAO,CAAC,CAAC;EACjB,OAAO;EACP,KAAK,MAAM,IAAI,QAAQ,IAAI,KAAK,CAAC,aAAa,EAAE;EAChD,MAAM,OAAO,CAAC,CAAC,CAAC;EAChB,KAAK;AACL;EACA;EACA,IAAI,IAAI,KAAK,KAAK,KAAK,EAAE;EACzB,MAAM,OAAO,KAAK,GAAG,KAAK,CAAC;EAC3B,KAAK;AACL;EACA;EACA,IAAI,IAAI,QAAQ,EAAE;EAClB,MAAM,IAAI,CAAC,QAAQ,EAAE;EACrB,QAAQ,OAAO,CAAC,CAAC;EACjB,OAAO;EACP,KAAK,MAAM,IAAI,QAAQ,EAAE;EACzB,MAAM,OAAO,CAAC,CAAC,CAAC;EAChB,KAAK;AACL;EACA;EACA,IAAI,MAAM,eAAe,GAAG,CAAC,KAAK,CAAC,MAAM,GAAG,KAAK,CAAC,MAAM,KAAK,QAAQ,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;EAChF,IAAI,IAAI,eAAe,KAAK,CAAC,EAAE;EAC/B,MAAM,OAAO,eAAe,CAAC;EAC7B,KAAK;AACL;EACA;EACA;EACA;EACA;EACA,IAAI,MAAM,WAAW,GAAG,EAAE,CAAC;EAC3B,IAAI,IAAI,EAAE,GAAG,CAAC,CAAC;EACf,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE;EAC3C,MAAM,MAAM,cAAc,GAAG,aAAa,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;EAC/D,MAAM,WAAW,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;EACvC,MAAM,EAAE,IAAI,cAAc,CAAC;EAC3B,KAAK;EACL,IAAI,IAAI,EAAE,KAAK,CAAC,EAAE;EAClB,MAAM,OAAO,EAAE,CAAC;EAChB,KAAK;AACL;EACA;EACA;EACA;EACA;EACA,IAAI,IAAI,CAAC,CAAC;EACV,IAAI,KAAK,CAAC,IAAI,WAAW,EAAE;EAC3B,MAAM,IAAI,CAAC,KAAK,CAAC,EAAE;EACnB,QAAQ,OAAO,CAAC,CAAC;EACjB,OAAO;EACP,KAAK;AACL;EACA;EACA,IAAI,OAAO,CAAC,CAAC;EACb,GAAG;AACH;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,EAAE,SAAS,oBAAoB,CAAC,SAAS,EAAE;EAC3C,IAAI,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE;EAChC,MAAM,OAAO,EAAE,CAAC;EAChB,KAAK;EACL,IAAI,MAAM,KAAK,GAAG,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;EAC1C,IAAI,IAAI,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE;EAC9B,MAAM,KAAK,CAAC,IAAI,CAAC,CAAC,EAAE,EAAE,EAAE,KAAK,EAAE,CAAC,KAAK,GAAG,EAAE,CAAC,KAAK,CAAC,CAAC;EAClD,KAAK;EACL,IAAI,IAAI,OAAO,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC;EACzC,IAAI,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE;EAChC,MAAM,OAAO,OAAO,CAAC;EACrB,KAAK;EACL,IAAI,OAAO,GAAG,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;EACjC;EACA;EACA,IAAI,MAAM,UAAU,GAAG,IAAI,GAAG,CAAC,SAAS,CAAC,CAAC;EAC1C,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE;EAC3C,MAAM,IAAI,QAAQ,CAAC;EACnB,MAAM,KAAK,QAAQ,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC,aAAa,EAAE;EAC/C,QAAQ,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;EAC5C,UAAU,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;EACjC,UAAU,UAAU,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;EACxC,SAAS;EACT,OAAO;EACP,KAAK;EACL,IAAI,OAAO,OAAO,CAAC;EACnB,GAAG;AACH;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,EAAE,SAAS,wBAAwB,CAAC,MAAM,EAAE,EAAE,EAAE;EAChD,IAAI,IAAI,SAAS,GAAG,EAAE,CAAC;AACvB;EACA;AACA;EACA,IAAI,IAAI,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,aAAa,CAAC,EAAE;EAC3C,MAAM,MAAM,SAAS,GAAG,YAAY,CAAC,MAAM,CAAC,CAAC;EAC7C,MAAM,MAAM,mBAAmB,GAAG,MAAM,CAAC,GAAG,CAAC,oBAAoB,CAAC,CAAC;EACnE,MAAM,SAAS,GAAG,SAAS,WAAW,GAAG;EACzC,QAAQ,MAAM,IAAI,GAAG,EAAE,CAAC;EACxB,QAAQ,MAAM,IAAI,GAAG,SAAS,GAAG,SAAS,CAAC,MAAM,GAAG,CAAC,GAAG,SAAS,CAAC,MAAM,CAAC;EACzE,QAAQ,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,EAAE,EAAE;EACvC,UAAU,IAAI,CAAC,CAAC,CAAC,GAAG,mBAAmB,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;EACzD,SAAS;EACT,QAAQ,IAAI,SAAS,EAAE;EACvB,UAAU,IAAI,CAAC,IAAI,CAAC,GAAG,SAAS,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC,CAAC;EACtE,SAAS;EACT,QAAQ,OAAO,EAAE,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;EACpC,OAAO,CAAC;EACR,KAAK;EACL,IAAI,IAAI,YAAY,GAAG,SAAS,CAAC;EACjC,IAAI,IAAI,YAAY,CAAC,MAAM,CAAC,EAAE;EAC9B,MAAM,MAAM,MAAM,GAAG,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC;EACvC,MAAM,YAAY,GAAG,SAAS,oBAAoB,GAAG;EACrD,QAAQ,OAAO,SAAS,CAAC,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,SAAS,EAAE,CAAC,EAAE,MAAM,CAAC,CAAC,MAAM,CAAC,CAAC,KAAK,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;EACrG,OAAO,CAAC;EACR,KAAK;EACL,IAAI,OAAO,YAAY,CAAC;EACxB,GAAG;AACH;EACA;EACA;EACA;EACA;EACA;EACA;EACA,EAAE,SAAS,oBAAoB,CAAC,KAAK,EAAE;EACvC,IAAI,IAAI,KAAK,EAAE,KAAK,EAAE,WAAW,EAAE,WAAW,CAAC;EAC/C,IAAI,MAAM,KAAK,GAAG,EAAE,CAAC;EACrB,IAAI,MAAM,WAAW,GAAG,EAAE,CAAC;EAC3B,IAAI,KAAK,CAAC,KAAK,CAAC,OAAO,CAAC,UAAU,IAAI,EAAE;EACxC,MAAM,IAAI,IAAI,CAAC,UAAU,EAAE;EAC3B,QAAQ,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC;EACxD,QAAQ,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;EAClD,OAAO;EACP,KAAK,CAAC,CAAC;AACP;EACA;EACA,IAAI,QAAQ,WAAW,CAAC,MAAM;EAC9B,MAAM,KAAK,CAAC;EACZ,QAAQ,OAAO,SAAS,UAAU,CAAC,GAAG,EAAE;EACxC,UAAU,OAAO,GAAG,CAAC;EACrB,SAAS,CAAC;EACV,MAAM,KAAK,CAAC;EACZ,QAAQ,KAAK,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;EACzB,QAAQ,WAAW,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC;EACrC,QAAQ,OAAO,SAAS,UAAU,CAAC,GAAG,EAAE;EACxC,UAAU,IAAI,KAAK,CAAC,GAAG,CAAC,EAAE;EAC1B,YAAY,OAAO,WAAW,CAAC,GAAG,CAAC,CAAC;EACpC,WAAW;EACX,UAAU,OAAO,GAAG,CAAC;EACrB,SAAS,CAAC;EACV,MAAM,KAAK,CAAC;EACZ,QAAQ,KAAK,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;EACzB,QAAQ,KAAK,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;EACzB,QAAQ,WAAW,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC;EACrC,QAAQ,WAAW,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC;EACrC,QAAQ,OAAO,SAAS,UAAU,CAAC,GAAG,EAAE;EACxC,UAAU,IAAI,KAAK,CAAC,GAAG,CAAC,EAAE;EAC1B,YAAY,OAAO,WAAW,CAAC,GAAG,CAAC,CAAC;EACpC,WAAW;EACX,UAAU,IAAI,KAAK,CAAC,GAAG,CAAC,EAAE;EAC1B,YAAY,OAAO,WAAW,CAAC,GAAG,CAAC,CAAC;EACpC,WAAW;EACX,UAAU,OAAO,GAAG,CAAC;EACrB,SAAS,CAAC;EACV,MAAM;EACN,QAAQ,OAAO,SAAS,UAAU,CAAC,GAAG,EAAE;EACxC,UAAU,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,WAAW,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;EACvD,YAAY,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE;EAC/B,cAAc,OAAO,WAAW,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;EACzC,aAAa;EACb,WAAW;EACX,UAAU,OAAO,GAAG,CAAC;EACrB,SAAS,CAAC;EACV,KAAK;EACL,GAAG;AACH;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,EAAE,SAAS,WAAW,CAAC,MAAM,EAAE;EAC/B,IAAI,SAAS,YAAY,CAAC,MAAM,EAAE,KAAK,EAAE,WAAW,EAAE;EACtD,MAAM,IAAI,KAAK,GAAG,MAAM,CAAC,MAAM,EAAE;EACjC,QAAQ,MAAM,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC;EACpC,QAAQ,IAAI,eAAe,GAAG,EAAE,CAAC;EACjC,QAAQ,IAAI,KAAK,CAAC,SAAS,EAAE;EAC7B;EACA;EACA,UAAU,MAAM,UAAU,GAAG,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC;EAC7D,UAAU,IAAI,UAAU,CAAC,MAAM,GAAG,KAAK,CAAC,KAAK,CAAC,MAAM,EAAE;EACtD,YAAY,eAAe,CAAC,IAAI,CAAC;EACjC,cAAc,KAAK,EAAE,UAAU;EAC/B,cAAc,IAAI,EAAE,KAAK,GAAG,UAAU,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC;EACjE,cAAc,MAAM,EAAE,UAAU,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC;EACnD,cAAc,aAAa,EAAE,KAAK;EAClC,cAAc,SAAS,EAAE,IAAI;EAC7B,aAAa,CAAC,CAAC;EACf,WAAW;EACX,UAAU,eAAe,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;EACtC,SAAS,MAAM;EACf;EACA,UAAU,eAAe,GAAG,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,UAAU,IAAI,EAAE;EAC5D,YAAY,OAAO;EACnB,cAAc,KAAK,EAAE,CAAC,IAAI,CAAC;EAC3B,cAAc,IAAI,EAAE,IAAI,CAAC,IAAI;EAC7B,cAAc,MAAM,EAAE,IAAI,CAAC,KAAK;EAChC,cAAc,aAAa,EAAE,IAAI,CAAC,UAAU;EAC5C,cAAc,SAAS,EAAE,KAAK;EAC9B,aAAa,CAAC;EACd,WAAW,CAAC,CAAC;EACb,SAAS;AACT;EACA;EACA,QAAQ,OAAO,OAAO,CAAC,eAAe,EAAE,UAAU,SAAS,EAAE;EAC7D,UAAU,OAAO,YAAY,CAAC,MAAM,EAAE,KAAK,GAAG,CAAC,EAAE,WAAW,CAAC,MAAM,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;EAClF,SAAS,CAAC,CAAC;EACX,OAAO,MAAM;EACb;EACA,QAAQ,OAAO,CAAC,WAAW,CAAC,CAAC;EAC7B,OAAO;EACP,KAAK;EACL,IAAI,OAAO,YAAY,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;EACvC,GAAG;AACH;EACA;EACA;EACA;EACA;EACA;EACA;EACA,EAAE,SAAS,WAAW,CAAC,OAAO,EAAE,OAAO,EAAE;EACzC,IAAI,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,MAAM,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC;EACxD,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,EAAE;EACjC,MAAM,MAAM,QAAQ,GAAG,iBAAiB,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC;EACrD,MAAM,MAAM,QAAQ,GAAG,iBAAiB,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC;EACrD,MAAM,IAAI,OAAO,GAAG,KAAK,CAAC;EAC1B,MAAM,IAAI,IAAI,CAAC;EACf,MAAM,KAAK,IAAI,IAAI,QAAQ,EAAE;EAC7B,QAAQ,IAAI,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;EAChC,UAAU,OAAO,GAAG,IAAI,CAAC;EACzB,UAAU,MAAM;EAChB,SAAS;EACT,OAAO;EACP,MAAM,IAAI,CAAC,OAAO,EAAE;EACpB,QAAQ,OAAO,KAAK,CAAC;EACrB,OAAO;EACP,KAAK;EACL,IAAI,MAAM,IAAI,GAAG,OAAO,CAAC,MAAM,CAAC;EAChC,IAAI,MAAM,IAAI,GAAG,OAAO,CAAC,MAAM,CAAC;EAChC,IAAI,MAAM,UAAU,GAAG,YAAY,CAAC,OAAO,CAAC,CAAC;EAC7C,IAAI,MAAM,UAAU,GAAG,YAAY,CAAC,OAAO,CAAC,CAAC;EAC7C,IAAI,OAAO,UAAU,GAAG,UAAU,GAAG,IAAI,KAAK,IAAI,GAAG,IAAI,IAAI,IAAI,GAAG,UAAU,GAAG,IAAI,IAAI,IAAI,GAAG,IAAI,KAAK,IAAI,CAAC;EAC9G,GAAG;AACH;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,EAAE,SAAS,gBAAgB,CAAC,YAAY,EAAE;EAC1C,IAAI,OAAO,YAAY,CAAC,GAAG,CAAC,EAAE,IAAI;EAClC,MAAM,IAAI,aAAa,CAAC,EAAE,CAAC,EAAE;EAC7B,QAAQ,OAAO,WAAW,CAAC,EAAE,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;EACpD,OAAO;EACP,MAAM,IAAI,SAAS,CAAC,EAAE,CAAC,EAAE;EACzB,QAAQ,OAAO,WAAW,CAAC,EAAE,CAAC,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;EACvE,OAAO;EACP,MAAM,OAAO,EAAE,CAAC;EAChB,KAAK,CAAC,CAAC;EACP,GAAG;AACH;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,EAAE,SAAS,kBAAkB,CAAC,UAAU,EAAE,YAAY,EAAE,YAAY,EAAE;EACtE,IAAI,MAAM,kBAAkB,GAAG,EAAE,CAAC;EAClC,IAAI,IAAI,SAAS,CAAC;EAClB,IAAI,KAAK,SAAS,IAAI,UAAU,EAAE;EAClC,MAAM,IAAI,UAAU,GAAG,YAAY,CAAC,SAAS,CAAC,CAAC;EAC/C,MAAM,IAAI,OAAO,UAAU,KAAK,QAAQ,EAAE;EAC1C,QAAQ,MAAM,IAAI,SAAS,CAAC,0CAA0C,GAAG,SAAS,GAAG,GAAG,CAAC,CAAC;EAC1F,OAAO;EACP,MAAM,UAAU,GAAG,YAAY,CAAC,UAAU,CAAC,CAAC;EAC5C,MAAM,IAAI,OAAO,UAAU,KAAK,UAAU,EAAE;EAC5C,QAAQ,OAAO,KAAK,CAAC;EACrB,OAAO;EACP,MAAM,kBAAkB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;EAC1C,KAAK;EACL,IAAI,OAAO,kBAAkB,CAAC;EAC9B,GAAG;AACH;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,EAAE,SAAS,iBAAiB,CAAC,YAAY,EAAE,YAAY,EAAE,IAAI,EAAE;EAC/D,IAAI,MAAM,iBAAiB,GAAG,gBAAgB,CAAC,YAAY,CAAC,CAAC;EAC7D,IAAI,MAAM,UAAU,GAAG,IAAI,KAAK,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;EACvE,IAAI,IAAI,cAAc,GAAG,IAAI,CAAC;EAC9B,IAAI,OAAO,cAAc,EAAE;EAC3B,MAAM,cAAc,GAAG,KAAK,CAAC;EAC7B,MAAM,IAAI,eAAe,GAAG,IAAI,CAAC;EACjC,MAAM,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,iBAAiB,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE;EACzD,QAAQ,IAAI,UAAU,CAAC,CAAC,CAAC,EAAE,SAAS;EACpC,QAAQ,MAAM,EAAE,GAAG,iBAAiB,CAAC,CAAC,CAAC,CAAC;EACxC,QAAQ,IAAI,aAAa,CAAC,EAAE,CAAC,EAAE;EAC/B,UAAU,iBAAiB,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,WAAW,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;EAC/D;EACA,UAAU,iBAAiB,CAAC,CAAC,CAAC,CAAC,WAAW,GAAG,EAAE,CAAC,WAAW,CAAC;EAC5D,UAAU,UAAU,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;EAC/B,UAAU,eAAe,GAAG,KAAK,CAAC;EAClC,SAAS,MAAM,IAAI,SAAS,CAAC,EAAE,CAAC,EAAE;EAClC,UAAU,MAAM,kBAAkB,GAAG,kBAAkB,CAAC,EAAE,CAAC,OAAO,CAAC,UAAU,EAAE,iBAAiB,EAAE,YAAY,CAAC,CAAC;EAChH,UAAU,IAAI,kBAAkB,EAAE;EAClC,YAAY,iBAAiB,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,kBAAkB,CAAC,CAAC;EACvF;EACA,YAAY,iBAAiB,CAAC,CAAC,CAAC,CAAC,OAAO,GAAG,EAAE,CAAC,OAAO,CAAC;EACtD,YAAY,UAAU,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;EACjC,YAAY,eAAe,GAAG,KAAK,CAAC;EACpC,WAAW,MAAM;EACjB,YAAY,cAAc,GAAG,IAAI,CAAC;EAClC,WAAW;EACX,SAAS;EACT,OAAO;EACP,MAAM,IAAI,eAAe,IAAI,cAAc,EAAE;EAC7C,QAAQ,MAAM,IAAI,WAAW,CAAC,wDAAwD,CAAC,CAAC;EACxF,OAAO;EACP,KAAK;EACL,IAAI,OAAO,iBAAiB,CAAC;EAC7B,GAAG;AACH;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,EAAE,SAAS,sBAAsB,CAAC,aAAa,EAAE;EACjD;AACA;EACA;EACA,IAAI,MAAM,mBAAmB,GAAG,2BAA2B,CAAC;EAC5D,IAAI,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,OAAO,CAAC,SAAS,IAAI;EACpD,MAAM,MAAM,EAAE,GAAG,aAAa,CAAC,SAAS,CAAC,CAAC;EAC1C,MAAM,IAAI,mBAAmB,CAAC,IAAI,CAAC,EAAE,CAAC,QAAQ,EAAE,CAAC,EAAE;EACnD,QAAQ,MAAM,IAAI,WAAW,CAAC,4CAA4C,GAAG,wCAAwC,GAAG,kDAAkD,CAAC,CAAC;EAC5K,OAAO;EACP,KAAK,CAAC,CAAC;EACP,GAAG;AACH;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,EAAE,SAAS,mBAAmB,CAAC,IAAI,EAAE,gBAAgB,EAAE;EACvD,IAAI,KAAK,CAAC,WAAW,EAAE,CAAC;EACxB,IAAI,IAAI,MAAM,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,MAAM,KAAK,CAAC,EAAE;EACpD,MAAM,MAAM,IAAI,WAAW,CAAC,wBAAwB,CAAC,CAAC;EACtD,KAAK;EACL,IAAI,IAAI,KAAK,CAAC,yBAAyB,EAAE;EACzC,MAAM,sBAAsB,CAAC,gBAAgB,CAAC,CAAC;EAC/C,KAAK;AACL;EACA;EACA,IAAI,MAAM,YAAY,GAAG,EAAE,CAAC;EAC5B,IAAI,MAAM,iBAAiB,GAAG,EAAE,CAAC;EACjC,IAAI,MAAM,aAAa,GAAG,EAAE,CAAC;EAC7B,IAAI,MAAM,qBAAqB,GAAG,EAAE,CAAC;EACrC,IAAI,IAAI,SAAS,CAAC;EAClB,IAAI,KAAK,SAAS,IAAI,gBAAgB,EAAE;EACxC;EACA,MAAM,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,gBAAgB,EAAE,SAAS,CAAC,EAAE;EAC9E,QAAQ,SAAS;EACjB,OAAO;EACP;EACA,MAAM,MAAM,MAAM,GAAG,cAAc,CAAC,SAAS,CAAC,CAAC;EAC/C,MAAM,IAAI,CAAC,MAAM,EAAE,SAAS;EAC5B;EACA,MAAM,YAAY,CAAC,OAAO,CAAC,UAAU,EAAE,EAAE;EACzC,QAAQ,IAAI,WAAW,CAAC,EAAE,EAAE,MAAM,CAAC,EAAE;EACrC,UAAU,MAAM,IAAI,SAAS,CAAC,0BAA0B,GAAG,eAAe,CAAC,EAAE,CAAC,GAAG,SAAS,GAAG,eAAe,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC,CAAC;EAC7H,SAAS;EACT,OAAO,CAAC,CAAC;EACT,MAAM,YAAY,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;EAChC;EACA,MAAM,MAAM,aAAa,GAAG,iBAAiB,CAAC,MAAM,CAAC;EACrD,MAAM,iBAAiB,CAAC,IAAI,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAC,CAAC;EAC1D,MAAM,MAAM,gBAAgB,GAAG,MAAM,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;EACvD;EACA,MAAM,IAAI,EAAE,CAAC;EACb,MAAM,KAAK,EAAE,IAAI,WAAW,CAAC,gBAAgB,CAAC,EAAE;EAChD,QAAQ,MAAM,MAAM,GAAG,eAAe,CAAC,EAAE,CAAC,CAAC;EAC3C,QAAQ,qBAAqB,CAAC,IAAI,CAAC;EACnC,UAAU,MAAM,EAAE,EAAE;EACpB,UAAU,IAAI,EAAE,MAAM;EACtB,UAAU,EAAE,EAAE,aAAa;EAC3B,SAAS,CAAC,CAAC;EACX,QAAQ,IAAI,EAAE,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,aAAa,CAAC,EAAE;EAC7C,UAAU,aAAa,CAAC,MAAM,CAAC,GAAG,aAAa,CAAC;EAChD,SAAS;EACT,OAAO;EACP,KAAK;EACL,IAAI,qBAAqB,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;AAClD;EACA;EACA,IAAI,MAAM,iBAAiB,GAAG,iBAAiB,CAAC,iBAAiB,EAAE,aAAa,EAAE,UAAU,CAAC,CAAC;AAC9F;EACA;EACA,IAAI,IAAI,CAAC,CAAC;EACV,IAAI,KAAK,CAAC,IAAI,aAAa,EAAE;EAC7B,MAAM,IAAI,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC,CAAC,EAAE;EAClE,QAAQ,aAAa,CAAC,CAAC,CAAC,GAAG,iBAAiB,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC;EAC/D,OAAO;EACP,KAAK;EACL,IAAI,MAAM,UAAU,GAAG,EAAE,CAAC;EAC1B,IAAI,MAAM,oBAAoB,GAAG,IAAI,GAAG,EAAE,CAAC;EAC3C,IAAI,KAAK,CAAC,IAAI,qBAAqB,EAAE;EACrC;EACA;EACA;EACA,MAAM,IAAI,CAAC,oBAAoB,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE;EAC7C,QAAQ,CAAC,CAAC,EAAE,GAAG,iBAAiB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;EACvC,QAAQ,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;EAC3B,QAAQ,oBAAoB,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;EAC5C,OAAO;EACP,KAAK;AACL;EACA;EACA,IAAI,MAAM,GAAG,GAAG,UAAU,CAAC,CAAC,CAAC,IAAI,UAAU,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC;EACzG,IAAI,MAAM,GAAG,GAAG,UAAU,CAAC,CAAC,CAAC,IAAI,UAAU,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC;EACzG,IAAI,MAAM,GAAG,GAAG,UAAU,CAAC,CAAC,CAAC,IAAI,UAAU,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC;EACzG,IAAI,MAAM,GAAG,GAAG,UAAU,CAAC,CAAC,CAAC,IAAI,UAAU,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC;EACzG,IAAI,MAAM,GAAG,GAAG,UAAU,CAAC,CAAC,CAAC,IAAI,UAAU,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC;EACzG,IAAI,MAAM,GAAG,GAAG,UAAU,CAAC,CAAC,CAAC,IAAI,UAAU,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC;EACzG,IAAI,MAAM,KAAK,GAAG,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,CAAC;AACzD;EACA;EACA,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE;EAChD,MAAM,UAAU,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,YAAY,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC;EAC9D,KAAK;EACL,IAAI,MAAM,MAAM,GAAG,GAAG,GAAG,WAAW,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC;EACtE,IAAI,MAAM,MAAM,GAAG,GAAG,GAAG,WAAW,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC;EACtE,IAAI,MAAM,MAAM,GAAG,GAAG,GAAG,WAAW,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC;EACtE,IAAI,MAAM,MAAM,GAAG,GAAG,GAAG,WAAW,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC;EACtE,IAAI,MAAM,MAAM,GAAG,GAAG,GAAG,WAAW,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC;EACtE,IAAI,MAAM,MAAM,GAAG,GAAG,GAAG,WAAW,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC;EACtE,IAAI,MAAM,MAAM,GAAG,GAAG,GAAG,WAAW,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC;EACtE,IAAI,MAAM,MAAM,GAAG,GAAG,GAAG,WAAW,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC;EACtE,IAAI,MAAM,MAAM,GAAG,GAAG,GAAG,WAAW,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC;EACtE,IAAI,MAAM,MAAM,GAAG,GAAG,GAAG,WAAW,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC;EACtE,IAAI,MAAM,MAAM,GAAG,GAAG,GAAG,WAAW,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC;EACtE,IAAI,MAAM,MAAM,GAAG,GAAG,GAAG,WAAW,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC;AACtE;EACA;EACA,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE;EAChD,MAAM,UAAU,CAAC,CAAC,CAAC,CAAC,cAAc,GAAG,wBAAwB,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;EACtG,KAAK;EACL,IAAI,MAAM,GAAG,GAAG,GAAG,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC,cAAc,GAAG,KAAK,CAAC;EAC3D,IAAI,MAAM,GAAG,GAAG,GAAG,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC,cAAc,GAAG,KAAK,CAAC;EAC3D,IAAI,MAAM,GAAG,GAAG,GAAG,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC,cAAc,GAAG,KAAK,CAAC;EAC3D,IAAI,MAAM,GAAG,GAAG,GAAG,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC,cAAc,GAAG,KAAK,CAAC;EAC3D,IAAI,MAAM,GAAG,GAAG,GAAG,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC,cAAc,GAAG,KAAK,CAAC;EAC3D,IAAI,MAAM,GAAG,GAAG,GAAG,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC,cAAc,GAAG,KAAK,CAAC;EAC3D,IAAI,MAAM,IAAI,GAAG,GAAG,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;EACxD,IAAI,MAAM,IAAI,GAAG,GAAG,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;EACxD,IAAI,MAAM,IAAI,GAAG,GAAG,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;EACxD,IAAI,MAAM,IAAI,GAAG,GAAG,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;EACxD,IAAI,MAAM,IAAI,GAAG,GAAG,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;EACxD,IAAI,MAAM,IAAI,GAAG,GAAG,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;AACxD;EACA;EACA,IAAI,MAAM,MAAM,GAAG,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC;EACjC,IAAI,MAAM,IAAI,GAAG,UAAU,CAAC,MAAM,CAAC;EACnC;EACA,IAAI,MAAM,KAAK,GAAG,UAAU,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC;EAC9C,IAAI,MAAM,GAAG,GAAG,UAAU,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,CAAC;EACtD,IAAI,MAAM,OAAO,GAAG,SAAS,OAAO,GAAG;AAEvC;EACA,MAAM,KAAK,IAAI,CAAC,GAAG,MAAM,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,EAAE,EAAE;EAC1C,QAAQ,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,EAAE;EACjC,UAAU,OAAO,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;EAC/C,SAAS;EACT,OAAO;EACP,MAAM,OAAO,KAAK,CAAC,UAAU,CAAC,IAAI,EAAE,SAAS,EAAE,UAAU,CAAC,CAAC;EAC3D,KAAK,CAAC;AACN;EACA;EACA;EACA,IAAI,SAAS,UAAU,CAAC,IAAI,EAAE,IAAI,EAAE;AAEpC;EACA,MAAM,IAAI,SAAS,CAAC,MAAM,KAAK,IAAI,IAAI,MAAM,CAAC,IAAI,CAAC,IAAI,MAAM,CAAC,IAAI,CAAC,EAAE;EACrE,QAAQ,OAAO,GAAG,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;EAC1C,OAAO;EACP,MAAM,IAAI,SAAS,CAAC,MAAM,KAAK,IAAI,IAAI,MAAM,CAAC,IAAI,CAAC,IAAI,MAAM,CAAC,IAAI,CAAC,EAAE;EACrE,QAAQ,OAAO,GAAG,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;EAC1C,OAAO;EACP,MAAM,IAAI,SAAS,CAAC,MAAM,KAAK,IAAI,IAAI,MAAM,CAAC,IAAI,CAAC,IAAI,MAAM,CAAC,IAAI,CAAC,EAAE;EACrE,QAAQ,OAAO,GAAG,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;EAC1C,OAAO;EACP,MAAM,IAAI,SAAS,CAAC,MAAM,KAAK,IAAI,IAAI,MAAM,CAAC,IAAI,CAAC,IAAI,MAAM,CAAC,IAAI,CAAC,EAAE;EACrE,QAAQ,OAAO,GAAG,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;EAC1C,OAAO;EACP,MAAM,IAAI,SAAS,CAAC,MAAM,KAAK,IAAI,IAAI,MAAM,CAAC,IAAI,CAAC,IAAI,MAAM,CAAC,IAAI,CAAC,EAAE;EACrE,QAAQ,OAAO,GAAG,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;EAC1C,OAAO;EACP,MAAM,IAAI,SAAS,CAAC,MAAM,KAAK,IAAI,IAAI,MAAM,CAAC,IAAI,CAAC,IAAI,MAAM,CAAC,IAAI,CAAC,EAAE;EACrE,QAAQ,OAAO,GAAG,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;EAC1C,OAAO;EACP,MAAM,OAAO,OAAO,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;EAC5C,KAAK;AACL;EACA;EACA,IAAI,IAAI;EACR,MAAM,MAAM,CAAC,cAAc,CAAC,UAAU,EAAE,MAAM,EAAE;EAChD,QAAQ,KAAK,EAAE,IAAI;EACnB,OAAO,CAAC,CAAC;EACT,KAAK,CAAC,OAAO,GAAG,EAAE;EAClB;EACA;EACA;EACA,KAAK;AACL;EACA;EACA;EACA;EACA,IAAI,UAAU,CAAC,UAAU,GAAG,aAAa,CAAC;AAC1C;EACA;EACA;EACA,IAAI,UAAU,CAAC,kBAAkB,GAAG;EACpC,MAAM,UAAU;EAChB,MAAM,YAAY,EAAE,oBAAoB;EACxC,KAAK,CAAC;EACN,IAAI,OAAO,UAAU,CAAC;EACtB,GAAG;AACH;EACA;EACA;EACA;EACA;EACA;EACA;EACA,EAAE,SAAS,WAAW,CAAC,IAAI,EAAE,IAAI,EAAE,UAAU,EAAE;EAC/C,IAAI,MAAM,WAAW,CAAC,IAAI,EAAE,IAAI,EAAE,UAAU,CAAC,CAAC;EAC9C,GAAG;AACH;EACA;EACA;EACA;EACA;EACA;EACA,EAAE,SAAS,OAAO,CAAC,GAAG,EAAE;EACxB,IAAI,OAAO,KAAK,CAAC,GAAG,EAAE,CAAC,EAAE,GAAG,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;EACzC,GAAG;AACH;EACA;EACA;EACA;EACA;EACA;EACA,EAAE,SAAS,IAAI,CAAC,GAAG,EAAE;EACrB,IAAI,OAAO,GAAG,CAAC,GAAG,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;EAC/B,GAAG;AACH;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,EAAE,SAAS,KAAK,CAAC,GAAG,EAAE,KAAK,EAAE,GAAG,EAAE;EAClC,IAAI,OAAO,KAAK,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,EAAE,KAAK,EAAE,GAAG,CAAC,CAAC;EACvD,GAAG;AACH;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,EAAE,SAAS,WAAW,CAAC,GAAG,EAAE,IAAI,EAAE;EAClC,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;EACzC,MAAM,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE;EACxB,QAAQ,OAAO,GAAG,CAAC,CAAC,CAAC,CAAC;EACtB,OAAO;EACP,KAAK;EACL,IAAI,OAAO,SAAS,CAAC;EACrB,GAAG;AACH;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,EAAE,SAAS,OAAO,CAAC,GAAG,EAAE,QAAQ,EAAE;EAClC,IAAI,OAAO,KAAK,CAAC,SAAS,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,EAAE,GAAG,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC;EAC/D,GAAG;AACH;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,EAAE,SAAS,OAAO,GAAG;EACrB,IAAI,MAAM,UAAU,GAAG,OAAO,CAAC,SAAS,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,eAAe,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACvF,IAAI,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC;EACrC,IAAI,IAAI,OAAO,QAAQ,KAAK,UAAU,EAAE;EACxC,MAAM,MAAM,IAAI,SAAS,CAAC,6CAA6C,CAAC,CAAC;EACzE,KAAK;EACL,IAAI,OAAO,WAAW,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;EAC7C,GAAG;EACH,EAAE,SAAS,WAAW,CAAC,UAAU,EAAE,QAAQ,EAAE;EAC7C,IAAI,OAAO;EACX,MAAM,OAAO,EAAE;EACf,QAAQ,UAAU;EAClB,QAAQ,QAAQ;EAChB,OAAO;EACP,KAAK,CAAC;EACN,GAAG;AACH;EACA;EACA;EACA;EACA;EACA;EACA;EACA,EAAE,SAAS,WAAW,CAAC,QAAQ,EAAE;EACjC,IAAI,IAAI,OAAO,QAAQ,KAAK,UAAU,EAAE;EACxC,MAAM,MAAM,IAAI,SAAS,CAAC,8CAA8C,CAAC,CAAC;EAC1E,KAAK;EACL,IAAI,OAAO;EACX,MAAM,WAAW,EAAE;EACnB,QAAQ,QAAQ;EAChB,OAAO;EACP,KAAK,CAAC;EACN,GAAG;AACH;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,EAAE,SAAS,SAAS,CAAC,UAAU,EAAE;EACjC,IAAI,OAAO,UAAU,IAAI,OAAO,UAAU,CAAC,OAAO,KAAK,QAAQ,IAAI,KAAK,CAAC,OAAO,CAAC,UAAU,CAAC,OAAO,CAAC,UAAU,CAAC,IAAI,OAAO,UAAU,CAAC,OAAO,CAAC,QAAQ,KAAK,UAAU,CAAC;EACrK,GAAG;AACH;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,EAAE,SAAS,aAAa,CAAC,UAAU,EAAE;EACrC,IAAI,OAAO,UAAU,IAAI,OAAO,UAAU,CAAC,WAAW,KAAK,QAAQ,IAAI,OAAO,UAAU,CAAC,WAAW,CAAC,QAAQ,KAAK,UAAU,CAAC;EAC7H,GAAG;AACH;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,EAAE,SAAS,SAAS,CAAC,SAAS,EAAE,OAAO,EAAE;EACzC,IAAI,IAAI,CAAC,SAAS,EAAE;EACpB,MAAM,OAAO,OAAO,CAAC;EACrB,KAAK;EACL,IAAI,IAAI,OAAO,IAAI,OAAO,KAAK,SAAS,EAAE;EAC1C,MAAM,MAAM,GAAG,GAAG,IAAI,KAAK,CAAC,yCAAyC,GAAG,SAAS,GAAG,YAAY,GAAG,OAAO,GAAG,GAAG,CAAC,CAAC;EAClH,MAAM,GAAG,CAAC,IAAI,GAAG;EACjB,QAAQ,MAAM,EAAE,OAAO;EACvB,QAAQ,QAAQ,EAAE,SAAS;EAC3B,OAAO,CAAC;EACR,MAAM,MAAM,GAAG,CAAC;EAChB,KAAK;EACL,IAAI,OAAO,SAAS,CAAC;EACrB,GAAG;AACH;EACA;EACA;EACA;EACA;EACA;EACA;EACA,EAAE,SAAS,aAAa,CAAC,GAAG,EAAE;EAC9B,IAAI,IAAI,IAAI,CAAC;EACb,IAAI,KAAK,MAAM,GAAG,IAAI,GAAG,EAAE;EAC3B;EACA;EACA,MAAM,IAAI,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,KAAK,eAAe,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,IAAI,OAAO,GAAG,CAAC,GAAG,CAAC,CAAC,SAAS,KAAK,QAAQ,CAAC,EAAE;EACnI,QAAQ,IAAI,GAAG,SAAS,CAAC,IAAI,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC;EAC9C,OAAO;EACP,KAAK;EACL,IAAI,OAAO,IAAI,CAAC;EAChB,GAAG;AACH;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,EAAE,SAAS,eAAe,CAAC,IAAI,EAAE,MAAM,EAAE;EACzC,IAAI,IAAI,GAAG,CAAC;EACZ,IAAI,KAAK,GAAG,IAAI,MAAM,EAAE;EACxB,MAAM,IAAI,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,EAAE;EAC7D,QAAQ,IAAI,GAAG,IAAI,IAAI,EAAE;EACzB,UAAU,IAAI,MAAM,CAAC,GAAG,CAAC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE;EACzC,YAAY,MAAM,GAAG,GAAG,IAAI,KAAK,CAAC,aAAa,GAAG,GAAG,GAAG,oBAAoB,CAAC,CAAC;EAC9E,YAAY,GAAG,CAAC,IAAI,GAAG;EACvB,cAAc,SAAS,EAAE,GAAG;EAC5B,cAAc,cAAc,EAAE,MAAM,CAAC,GAAG,CAAC;EACzC,cAAc,YAAY,EAAE,IAAI,CAAC,GAAG,CAAC;EACrC,aAAa,CAAC;EACd,YAAY,MAAM,GAAG,CAAC;EACtB,WAAW;EACX;EACA,SAAS;EACT,QAAQ,IAAI,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC;EAChC,OAAO;EACP,KAAK;EACL,GAAG;EACH,EAAE,MAAM,SAAS,GAAG,KAAK,CAAC;AAC1B;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,EAAE,KAAK,GAAG,UAAU,SAAS,EAAE;EAC/B,IAAI,MAAM,KAAK,GAAG,OAAO,SAAS,KAAK,QAAQ,CAAC;EAChD,IAAI,MAAM,KAAK,GAAG,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC;EAChC,IAAI,IAAI,IAAI,GAAG,KAAK,GAAG,SAAS,GAAG,EAAE,CAAC;EACtC,IAAI,MAAM,aAAa,GAAG,EAAE,CAAC;EAC7B,IAAI,KAAK,IAAI,CAAC,GAAG,KAAK,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE;EACnD,MAAM,MAAM,IAAI,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;EAChC,MAAM,IAAI,eAAe,GAAG,EAAE,CAAC;EAC/B,MAAM,IAAI,QAAQ,CAAC;EACnB,MAAM,IAAI,OAAO,IAAI,KAAK,UAAU,EAAE;EACtC,QAAQ,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC;EAC7B,QAAQ,IAAI,OAAO,IAAI,CAAC,SAAS,KAAK,QAAQ,EAAE;EAChD;EACA,UAAU,eAAe,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,IAAI,CAAC;EACjD,SAAS,MAAM,IAAI,eAAe,CAAC,IAAI,CAAC,EAAE;EAC1C;EACA,UAAU,eAAe,GAAG,IAAI,CAAC,UAAU,CAAC;EAC5C,SAAS;EACT,OAAO,MAAM,IAAI,aAAa,CAAC,IAAI,CAAC,EAAE;EACtC;EACA,QAAQ,eAAe,GAAG,IAAI,CAAC;EAC/B,QAAQ,IAAI,CAAC,KAAK,EAAE;EACpB,UAAU,QAAQ,GAAG,aAAa,CAAC,IAAI,CAAC,CAAC;EACzC,SAAS;EACT,OAAO;EACP,MAAM,IAAI,MAAM,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,MAAM,KAAK,CAAC,EAAE;EACrD,QAAQ,MAAM,GAAG,GAAG,IAAI,SAAS,CAAC,iCAAiC,GAAG,CAAC,GAAG,8BAA8B,GAAG,gEAAgE,CAAC,CAAC;EAC7K,QAAQ,GAAG,CAAC,IAAI,GAAG;EACnB,UAAU,KAAK,EAAE,CAAC;EAClB,UAAU,QAAQ,EAAE,IAAI;EACxB,SAAS,CAAC;EACV,QAAQ,MAAM,GAAG,CAAC;EAClB,OAAO;EACP,MAAM,IAAI,CAAC,KAAK,EAAE;EAClB,QAAQ,IAAI,GAAG,SAAS,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;EACzC,OAAO;EACP,MAAM,eAAe,CAAC,aAAa,EAAE,eAAe,CAAC,CAAC;EACtD,KAAK;EACL,IAAI,OAAO,mBAAmB,CAAC,IAAI,IAAI,EAAE,EAAE,aAAa,CAAC,CAAC;EAC1D,GAAG,CAAC;EACJ,EAAE,KAAK,CAAC,MAAM,GAAG,MAAM,CAAC;EACxB,EAAE,KAAK,CAAC,WAAW,GAAG,SAAS,CAAC,WAAW,CAAC;EAC5C,EAAE,KAAK,CAAC,UAAU,GAAG,WAAW,CAAC;EACjC,EAAE,KAAK,CAAC,kBAAkB,GAAG,WAAW,CAAC;EACzC,EAAE,KAAK,CAAC,WAAW,GAAG,WAAW,CAAC;EAClC,EAAE,KAAK,CAAC,KAAK,GAAG,KAAK,CAAC;EACtB,EAAE,KAAK,CAAC,gBAAgB,GAAG,gBAAgB,CAAC;EAC5C,EAAE,KAAK,CAAC,QAAQ,GAAG,QAAQ,CAAC;EAC5B,EAAE,KAAK,CAAC,SAAS,GAAG,QAAQ,CAAC;EAC7B,EAAE,KAAK,CAAC,OAAO,GAAG,OAAO,CAAC;EAC1B,EAAE,KAAK,CAAC,WAAW,GAAG,WAAW,CAAC;EAClC,EAAE,KAAK,CAAC,OAAO,GAAG,OAAO,CAAC;EAC1B,EAAE,KAAK,CAAC,aAAa,GAAG,aAAa,CAAC;EACtC,EAAE,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC;EACpB,EAAE,KAAK,CAAC,eAAe,GAAG,eAAe,CAAC;EAC1C,EAAE,KAAK,CAAC,yBAAyB,GAAG,IAAI,CAAC;AACzC;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,EAAE,KAAK,CAAC,OAAO,GAAG,UAAU,IAAI,EAAE,gBAAgB,EAAE;EACpD,IAAI,IAAI,MAAM,GAAG,KAAK,CAAC;EACvB,IAAI,IAAI,gBAAgB,KAAK,KAAK,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE;EAC7D,MAAM,MAAM,GAAG,QAAQ,CAAC;EACxB,KAAK;EACL,IAAI,KAAK,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,EAAE,MAAM,CAAC,CAAC;EACnC,GAAG,CAAC;AACJ;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,EAAE,SAAS,mBAAmB,CAAC,UAAU,EAAE;EAC3C,IAAI,IAAI,CAAC,UAAU,IAAI,OAAO,UAAU,CAAC,IAAI,KAAK,QAAQ,IAAI,OAAO,UAAU,CAAC,EAAE,KAAK,QAAQ,IAAI,OAAO,UAAU,CAAC,OAAO,KAAK,UAAU,EAAE;EAC7I,MAAM,MAAM,IAAI,SAAS,CAAC,+EAA+E,CAAC,CAAC;EAC3G,KAAK;EACL,IAAI,IAAI,UAAU,CAAC,EAAE,KAAK,UAAU,CAAC,IAAI,EAAE;EAC3C,MAAM,MAAM,IAAI,WAAW,CAAC,qCAAqC,GAAG,UAAU,CAAC,IAAI,GAAG,cAAc,CAAC,CAAC;EACtG,KAAK;EACL,GAAG;AACH;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,EAAE,KAAK,CAAC,aAAa,GAAG,UAAU,UAAU,EAAE;EAC9C,IAAI,IAAI,OAAO,GAAG,SAAS,CAAC,MAAM,GAAG,CAAC,IAAI,SAAS,CAAC,CAAC,CAAC,KAAK,SAAS,GAAG,SAAS,CAAC,CAAC,CAAC,GAAG;EACtF,MAAM,QAAQ,EAAE,KAAK;EACrB,KAAK,CAAC;EACN,IAAI,mBAAmB,CAAC,UAAU,CAAC,CAAC;EACpC,IAAI,MAAM,EAAE,GAAG,QAAQ,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC;EACvC,IAAI,MAAM,QAAQ,GAAG,EAAE,CAAC,aAAa,CAAC,IAAI,CAAC,KAAK,IAAI,KAAK,CAAC,IAAI,KAAK,UAAU,CAAC,IAAI,CAAC,CAAC;EACpF,IAAI,IAAI,QAAQ,EAAE;EAClB,MAAM,IAAI,OAAO,IAAI,OAAO,CAAC,QAAQ,EAAE;EACvC,QAAQ,KAAK,CAAC,gBAAgB,CAAC;EAC/B,UAAU,IAAI,EAAE,QAAQ,CAAC,IAAI;EAC7B,UAAU,EAAE,EAAE,UAAU,CAAC,EAAE;EAC3B,UAAU,OAAO,EAAE,QAAQ,CAAC,OAAO;EACnC,SAAS,CAAC,CAAC;EACX,OAAO,MAAM;EACb,QAAQ,MAAM,IAAI,KAAK,CAAC,sCAAsC,GAAG,UAAU,CAAC,IAAI,GAAG,QAAQ,GAAG,EAAE,CAAC,IAAI,GAAG,GAAG,CAAC,CAAC;EAC7G,OAAO;EACP,KAAK;EACL,IAAI,EAAE,CAAC,aAAa,CAAC,IAAI,CAAC;EAC1B,MAAM,IAAI,EAAE,UAAU,CAAC,IAAI;EAC3B,MAAM,OAAO,EAAE,UAAU,CAAC,OAAO;EACjC,MAAM,KAAK,EAAE,YAAY,EAAE;EAC3B,KAAK,CAAC,CAAC;EACP,GAAG,CAAC;AACJ;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,EAAE,KAAK,CAAC,cAAc,GAAG,UAAU,WAAW,EAAE,OAAO,EAAE;EACzD,IAAI,WAAW,CAAC,OAAO,CAAC,UAAU,IAAI,KAAK,CAAC,aAAa,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC,CAAC;EAChF,GAAG,CAAC;AACJ;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,EAAE,KAAK,CAAC,gBAAgB,GAAG,UAAU,UAAU,EAAE;EACjD,IAAI,mBAAmB,CAAC,UAAU,CAAC,CAAC;EACpC,IAAI,MAAM,EAAE,GAAG,QAAQ,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC;EACvC,IAAI,MAAM,kBAAkB,GAAG,WAAW,CAAC,EAAE,CAAC,aAAa,EAAE,CAAC,IAAI,CAAC,CAAC,IAAI,KAAK,UAAU,CAAC,IAAI,CAAC,CAAC;EAC9F,IAAI,IAAI,CAAC,kBAAkB,EAAE;EAC7B,MAAM,MAAM,IAAI,KAAK,CAAC,gDAAgD,GAAG,UAAU,CAAC,IAAI,GAAG,MAAM,GAAG,UAAU,CAAC,EAAE,CAAC,CAAC;EACnH,KAAK;EACL,IAAI,IAAI,kBAAkB,CAAC,OAAO,KAAK,UAAU,CAAC,OAAO,EAAE;EAC3D,MAAM,MAAM,IAAI,KAAK,CAAC,yDAAyD,CAAC,CAAC;EACjF,KAAK;EACL,IAAI,MAAM,KAAK,GAAG,EAAE,CAAC,aAAa,CAAC,OAAO,CAAC,kBAAkB,CAAC,CAAC;EAC/D,IAAI,EAAE,CAAC,aAAa,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;EACtC,GAAG,CAAC;AACJ;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,EAAE,KAAK,CAAC,OAAO,GAAG,UAAU,EAAE,EAAE,OAAO,EAAE;EACzC,IAAI,IAAI,CAAC,eAAe,CAAC,EAAE,CAAC,EAAE;EAC9B,MAAM,MAAM,IAAI,SAAS,CAAC,kBAAkB,CAAC,CAAC;EAC9C,KAAK;EACL,IAAI,MAAM,IAAI,GAAG,EAAE,CAAC,kBAAkB,CAAC,UAAU,CAAC;EAClD,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE;EAC1C,MAAM,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE;EACjC,QAAQ,OAAO,IAAI,CAAC,CAAC,CAAC,CAAC;EACvB,OAAO;EACP,KAAK;EACL,IAAI,OAAO,IAAI,CAAC;EAChB,GAAG,CAAC;EACJ,EAAE,OAAO,KAAK,CAAC;EACf,CAAC;AACD,sBAAe,MAAM,EAAE;;;;;;;;"}