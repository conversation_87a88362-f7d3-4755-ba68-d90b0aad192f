{"name": "complex.js", "title": "Complex.js", "version": "2.4.2", "homepage": "https://raw.org/article/complex-numbers-in-javascript/", "bugs": "https://github.com/rawify/Complex.js/issues", "description": "A complex numbers library", "keywords": ["complex numbers", "math", "complex", "number", "calculus", "parser", "arithmetic"], "private": false, "main": "./dist/complex.js", "module": "./dist/complex.mjs", "types": "./complex.d.ts", "browser": "./dist/complex.min.js", "unpkg": "./dist/complex.min.js", "readmeFilename": "README.md", "exports": {".": {"types": "./complex.d.ts", "require": "./dist/complex.js", "import": "./dist/complex.mjs"}}, "repository": {"type": "git", "url": "**************:rawify/Complex.js.git"}, "funding": {"type": "github", "url": "https://github.com/sponsors/rawify"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://raw.org/"}, "license": "MIT", "engines": {"node": "*"}, "directories": {"example": "examples", "test": "tests"}, "scripts": {"build": "crude-build Complex", "test": "mocha tests/*.js"}, "devDependencies": {"crude-build": "^0.1.2", "mocha": "*"}}