"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.quantileSeqDependencies = void 0;
var _dependenciesAddGenerated = require("./dependenciesAdd.generated.js");
var _dependenciesCompareGenerated = require("./dependenciesCompare.generated.js");
var _dependenciesDivideGenerated = require("./dependenciesDivide.generated.js");
var _dependenciesIsIntegerGenerated = require("./dependenciesIsInteger.generated.js");
var _dependenciesLargerGenerated = require("./dependenciesLarger.generated.js");
var _dependenciesMapSlicesGenerated = require("./dependenciesMapSlices.generated.js");
var _dependenciesMultiplyGenerated = require("./dependenciesMultiply.generated.js");
var _dependenciesPartitionSelectGenerated = require("./dependenciesPartitionSelect.generated.js");
var _dependenciesSmallerGenerated = require("./dependenciesSmaller.generated.js");
var _dependenciesSmallerEqGenerated = require("./dependenciesSmallerEq.generated.js");
var _dependenciesSubtractGenerated = require("./dependenciesSubtract.generated.js");
var _dependenciesTypedGenerated = require("./dependenciesTyped.generated.js");
var _factoriesNumber = require("../../factoriesNumber.js");
/**
 * THIS FILE IS AUTO-GENERATED
 * DON'T MAKE CHANGES HERE
 */

const quantileSeqDependencies = exports.quantileSeqDependencies = {
  addDependencies: _dependenciesAddGenerated.addDependencies,
  compareDependencies: _dependenciesCompareGenerated.compareDependencies,
  divideDependencies: _dependenciesDivideGenerated.divideDependencies,
  isIntegerDependencies: _dependenciesIsIntegerGenerated.isIntegerDependencies,
  largerDependencies: _dependenciesLargerGenerated.largerDependencies,
  mapSlicesDependencies: _dependenciesMapSlicesGenerated.mapSlicesDependencies,
  multiplyDependencies: _dependenciesMultiplyGenerated.multiplyDependencies,
  partitionSelectDependencies: _dependenciesPartitionSelectGenerated.partitionSelectDependencies,
  smallerDependencies: _dependenciesSmallerGenerated.smallerDependencies,
  smallerEqDependencies: _dependenciesSmallerEqGenerated.smallerEqDependencies,
  subtractDependencies: _dependenciesSubtractGenerated.subtractDependencies,
  typedDependencies: _dependenciesTypedGenerated.typedDependencies,
  createQuantileSeq: _factoriesNumber.createQuantileSeq
};