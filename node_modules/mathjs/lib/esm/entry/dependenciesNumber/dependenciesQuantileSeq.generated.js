/**
 * THIS FILE IS AUTO-GENERATED
 * DON'T MAKE CHANGES HERE
 */
import { addDependencies } from './dependenciesAdd.generated.js';
import { compareDependencies } from './dependenciesCompare.generated.js';
import { divideDependencies } from './dependenciesDivide.generated.js';
import { isIntegerDependencies } from './dependenciesIsInteger.generated.js';
import { largerDependencies } from './dependenciesLarger.generated.js';
import { mapSlicesDependencies } from './dependenciesMapSlices.generated.js';
import { multiplyDependencies } from './dependenciesMultiply.generated.js';
import { partitionSelectDependencies } from './dependenciesPartitionSelect.generated.js';
import { smallerDependencies } from './dependenciesSmaller.generated.js';
import { smallerEqDependencies } from './dependenciesSmallerEq.generated.js';
import { subtractDependencies } from './dependenciesSubtract.generated.js';
import { typedDependencies } from './dependenciesTyped.generated.js';
import { createQuantileSeq } from '../../factoriesNumber.js';
export var quantileSeqDependencies = {
  addDependencies,
  compareDependencies,
  divideDependencies,
  isIntegerDependencies,
  largerDependencies,
  mapSlicesDependencies,
  multiplyDependencies,
  partitionSelectDependencies,
  smallerDependencies,
  smallerEqDependencies,
  subtractDependencies,
  typedDependencies,
  createQuantileSeq
};