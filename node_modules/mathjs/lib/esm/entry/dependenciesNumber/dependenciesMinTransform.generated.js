/**
 * THIS FILE IS AUTO-GENERATED
 * DON'T MAKE CHANGES HERE
 */
import { isNaNDependencies } from './dependenciesIsNaN.generated.js';
import { numericDependencies } from './dependenciesNumeric.generated.js';
import { smallerDependencies } from './dependenciesSmaller.generated.js';
import { typedDependencies } from './dependenciesTyped.generated.js';
import { createMinTransform } from '../../factoriesNumber.js';
export var minTransformDependencies = {
  isNaNDependencies,
  numericDependencies,
  smallerDependencies,
  typedDependencies,
  createMinTransform
};