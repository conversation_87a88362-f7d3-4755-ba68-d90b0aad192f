/**
 * THIS FILE IS AUTO-GENERATED
 * DON'T MAKE CHANGES HERE
 */
import { AccessorNodeDependencies } from './dependenciesAccessorNode.generated.js';
import { ArrayNodeDependencies } from './dependenciesArrayNode.generated.js';
import { ConstantNodeDependencies } from './dependenciesConstantNode.generated.js';
import { FunctionNodeDependencies } from './dependenciesFunctionNode.generated.js';
import { IndexNodeDependencies } from './dependenciesIndexNode.generated.js';
import { ObjectNodeDependencies } from './dependenciesObjectNode.generated.js';
import { OperatorNodeDependencies } from './dependenciesOperatorNode.generated.js';
import { ParenthesisNodeDependencies } from './dependenciesParenthesisNode.generated.js';
import { SymbolNodeDependencies } from './dependenciesSymbolNode.generated.js';
import { equalDependencies } from './dependenciesEqual.generated.js';
import { parseDependencies } from './dependenciesParse.generated.js';
import { replacerDependencies } from './dependenciesReplacer.generated.js';
import { resolveDependencies } from './dependenciesResolve.generated.js';
import { simplifyConstantDependencies } from './dependenciesSimplifyConstant.generated.js';
import { simplifyCoreDependencies } from './dependenciesSimplifyCore.generated.js';
import { typedDependencies } from './dependenciesTyped.generated.js';
import { createSimplify } from '../../factoriesNumber.js';
export var simplifyDependencies = {
  AccessorNodeDependencies,
  ArrayNodeDependencies,
  ConstantNodeDependencies,
  FunctionNodeDependencies,
  IndexNodeDependencies,
  ObjectNodeDependencies,
  OperatorNodeDependencies,
  ParenthesisNodeDependencies,
  SymbolNodeDependencies,
  equalDependencies,
  parseDependencies,
  replacerDependencies,
  resolveDependencies,
  simplifyConstantDependencies,
  simplifyCoreDependencies,
  typedDependencies,
  createSimplify
};