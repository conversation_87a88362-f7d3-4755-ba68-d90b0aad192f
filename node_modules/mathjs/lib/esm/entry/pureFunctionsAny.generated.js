/**
 * THIS FILE IS AUTO-GENERATED
 * DON'T MAKE CHANGES HERE
 */
import { config } from './configReadonly.js';
import { createBigNumberClass, createComplexClass, createE, createFalse, createFineStructure, createFractionClass, createI, createInfinity, createLN10, createLOG10E, createMatrixClass, createNaN, createNull, createPhi, createRangeClass, createResultSet, createSQRT1_2,
// eslint-disable-line camelcase
createSackurTetrode, createTau, createTrue, createVersion, createDenseMatrixClass, createEfimovFactor, createLN2, createP<PERSON>, createReplacer, createSQRT2, createTyped, createWeakMixingAngle, createAbs, createAcos, createAcot, createAcs<PERSON>, createAddScalar, createArg, createAsech, create<PERSON>inh, createAtan, createAtanh, createBigint, createBitNot, createB<PERSON>ean, create<PERSON>lone, createCombinations, createComplex, createConj, createCos, createCot, createCsc, createCube, createEqualScalar, createErf, createExp, createExpm1, create<PERSON><PERSON><PERSON>, create<PERSON><PERSON><PERSON>, createForEach, createFormat, createGetMatrixDataType, createHex, createIm, createIsInteger, createIsNegative, createIsPositive, createIsZero, createLOG2E, createLgamma, createLog10, createLog2, createMap, createMultiplyScalar, createNot, createNumber, createOct, createPickRandom, createPrint, createRandom, createRe, createSec, createSign, createSin, createSparseMatrixClass, createSplitUnit, createSquare, createString, createSubtractScalar, createTan, createToBest, createTypeOf, createAcosh, createAcsch, createAsec, createBignumber, createCombinationsWithRep, createCosh, createCsch, createIsNaN, createIsPrime, createMapSlices, createMatrix, createMatrixFromFunction, createOnes, createRandomInt, createReshape, createSech, createSinh, createSparse, createSqrt, createSqueeze, createTanh, createTranspose, createXgcd, createZeros, createAcoth, createAsin, createBin, createConcat, createCoth, createCtranspose, createDiag, createDotMultiply, createEqual, createFraction, createIdentity, createIsNumeric, createKron, createLargerEq, createLeftShift, createMode, createNthRoot, createNumeric, createProd, createResize, createRightArithShift, createRound, createSize, createSmaller, createTo, createUnaryMinus, createUnequal, createXor, createAdd, createAtan2, createBitAnd, createBitOr, createBitXor, createCbrt, createCompare, createCompareText, createCount, createDeepEqual, createDivideScalar, createDotDivide, createEqualText, createFloor, createGcd, createHasNumericValue, createHypot, createImmutableDenseMatrixClass, createIndexClass, createLarger, createLog, createLsolve, createMatrixFromColumns, createMax, createMin, createMod, createNthRoots, createOr, createPartitionSelect, createQr, createRightLogShift, createSmallerEq, createSubset, createSubtract, createTrace, createUsolve, createCatalan, createCompareNatural, createComposition, createDiff, createDistance, createDot, createFibonacciHeapClass, createIndex, createInvmod, createLcm, createLog1p, createLsolveAll, createMatrixFromRows, createMultiply, createRange, createRow, createSetCartesian, createSetDistinct, createSetIsSubset, createSetPowerset, createSlu, createSort, createUnaryPlus, createUsolveAll, createZpk2tf, createAnd, createCeil, createColumn, createCross, createDet, createFix, createInv, createPinv, createPow, createSetDifference, createSetMultiplicity, createSetSymDifference, createSpaClass, createSqrtm, createSum, createUnitClass, createVacuumImpedance, createWienDisplacement, createAtomicMass, createBohrMagneton, createBoltzmann, createConductanceQuantum, createCoulomb, createCreateUnit, createDeuteronMass, createDotPow, createElectricConstant, createElementaryCharge, createExpm, createFaraday, createFft, createGamma, createGravitationConstant, createHartreeEnergy, createIfft, createInverseConductanceQuantum, createKlitzing, createLoschmidt, createMagneticConstant, createMolarMass, createMolarPlanckConstant, createNeutronMass, createNuclearMagneton, createPlanckCharge, createPlanckLength, createPlanckTemperature, createProtonMass, createQuantumOfCirculation, createReducedPlanckConstant, createRydberg, createSecondRadiation, createSetSize, createSpeedOfLight, createStefanBoltzmann, createThomsonCrossSection, createAvogadro, createBohrRadius, createCoulombConstant, createDivide, createElectronMass, createFactorial, createFirstRadiation, createGravity, createIntersect, createLup, createMagneticFluxQuantum, createMolarMassC12, createMultinomial, createPermutations, createPlanckMass, createPolynomialRoot, createSetIntersect, createSolveODE, createStirlingS2, createUnitFunction, createBellNumbers, createCumSum, createEigs, createFermiCoupling, createGasConstant, createKldivergence, createLusolve, createMean, createMolarVolume, createPlanckConstant, createQuantileSeq, createSetUnion, createVariance, createClassicalElectronRadius, createMedian, createCorr, createFreqz, createMad, createStd, createZeta, createNorm, createRotationMatrix, createPlanckTime, createSchur, createRotate, createSylvester, createLyap } from '../factoriesAny.js';
export var BigNumber = /* #__PURE__ */createBigNumberClass({
  config
});
export var Complex = /* #__PURE__ */createComplexClass({});
export var e = /* #__PURE__ */createE({
  BigNumber,
  config
});
export var _false = /* #__PURE__ */createFalse({});
export var fineStructure = /* #__PURE__ */createFineStructure({
  BigNumber,
  config
});
export var Fraction = /* #__PURE__ */createFractionClass({});
export var i = /* #__PURE__ */createI({
  Complex
});
export var _Infinity = /* #__PURE__ */createInfinity({
  BigNumber,
  config
});
export var LN10 = /* #__PURE__ */createLN10({
  BigNumber,
  config
});
export var LOG10E = /* #__PURE__ */createLOG10E({
  BigNumber,
  config
});
export var Matrix = /* #__PURE__ */createMatrixClass({});
export var _NaN = /* #__PURE__ */createNaN({
  BigNumber,
  config
});
export var _null = /* #__PURE__ */createNull({});
export var phi = /* #__PURE__ */createPhi({
  BigNumber,
  config
});
export var Range = /* #__PURE__ */createRangeClass({});
export var ResultSet = /* #__PURE__ */createResultSet({});
export var SQRT1_2 = /* #__PURE__ */createSQRT1_2({
  BigNumber,
  config
});
export var sackurTetrode = /* #__PURE__ */createSackurTetrode({
  BigNumber,
  config
});
export var tau = /* #__PURE__ */createTau({
  BigNumber,
  config
});
export var _true = /* #__PURE__ */createTrue({});
export var version = /* #__PURE__ */createVersion({});
export var DenseMatrix = /* #__PURE__ */createDenseMatrixClass({
  Matrix
});
export var efimovFactor = /* #__PURE__ */createEfimovFactor({
  BigNumber,
  config
});
export var LN2 = /* #__PURE__ */createLN2({
  BigNumber,
  config
});
export var pi = /* #__PURE__ */createPi({
  BigNumber,
  config
});
export var replacer = /* #__PURE__ */createReplacer({});
export var SQRT2 = /* #__PURE__ */createSQRT2({
  BigNumber,
  config
});
export var typed = /* #__PURE__ */createTyped({
  BigNumber,
  Complex,
  DenseMatrix,
  Fraction
});
export var weakMixingAngle = /* #__PURE__ */createWeakMixingAngle({
  BigNumber,
  config
});
export var abs = /* #__PURE__ */createAbs({
  typed
});
export var acos = /* #__PURE__ */createAcos({
  Complex,
  config,
  typed
});
export var acot = /* #__PURE__ */createAcot({
  BigNumber,
  typed
});
export var acsc = /* #__PURE__ */createAcsc({
  BigNumber,
  Complex,
  config,
  typed
});
export var addScalar = /* #__PURE__ */createAddScalar({
  typed
});
export var arg = /* #__PURE__ */createArg({
  typed
});
export var asech = /* #__PURE__ */createAsech({
  BigNumber,
  Complex,
  config,
  typed
});
export var asinh = /* #__PURE__ */createAsinh({
  typed
});
export var atan = /* #__PURE__ */createAtan({
  typed
});
export var atanh = /* #__PURE__ */createAtanh({
  Complex,
  config,
  typed
});
export var bigint = /* #__PURE__ */createBigint({
  typed
});
export var bitNot = /* #__PURE__ */createBitNot({
  typed
});
export var boolean = /* #__PURE__ */createBoolean({
  typed
});
export var clone = /* #__PURE__ */createClone({
  typed
});
export var combinations = /* #__PURE__ */createCombinations({
  typed
});
export var complex = /* #__PURE__ */createComplex({
  Complex,
  typed
});
export var conj = /* #__PURE__ */createConj({
  typed
});
export var cos = /* #__PURE__ */createCos({
  typed
});
export var cot = /* #__PURE__ */createCot({
  BigNumber,
  typed
});
export var csc = /* #__PURE__ */createCsc({
  BigNumber,
  typed
});
export var cube = /* #__PURE__ */createCube({
  typed
});
export var equalScalar = /* #__PURE__ */createEqualScalar({
  config,
  typed
});
export var erf = /* #__PURE__ */createErf({
  typed
});
export var exp = /* #__PURE__ */createExp({
  typed
});
export var expm1 = /* #__PURE__ */createExpm1({
  Complex,
  typed
});
export var filter = /* #__PURE__ */createFilter({
  typed
});
export var flatten = /* #__PURE__ */createFlatten({
  typed
});
export var forEach = /* #__PURE__ */createForEach({
  typed
});
export var format = /* #__PURE__ */createFormat({
  typed
});
export var getMatrixDataType = /* #__PURE__ */createGetMatrixDataType({
  typed
});
export var hex = /* #__PURE__ */createHex({
  format,
  typed
});
export var im = /* #__PURE__ */createIm({
  typed
});
export var isInteger = /* #__PURE__ */createIsInteger({
  typed
});
export var isNegative = /* #__PURE__ */createIsNegative({
  config,
  typed
});
export var isPositive = /* #__PURE__ */createIsPositive({
  config,
  typed
});
export var isZero = /* #__PURE__ */createIsZero({
  equalScalar,
  typed
});
export var LOG2E = /* #__PURE__ */createLOG2E({
  BigNumber,
  config
});
export var lgamma = /* #__PURE__ */createLgamma({
  Complex,
  typed
});
export var log10 = /* #__PURE__ */createLog10({
  Complex,
  config,
  typed
});
export var log2 = /* #__PURE__ */createLog2({
  Complex,
  config,
  typed
});
export var map = /* #__PURE__ */createMap({
  typed
});
export var multiplyScalar = /* #__PURE__ */createMultiplyScalar({
  typed
});
export var not = /* #__PURE__ */createNot({
  typed
});
export var number = /* #__PURE__ */createNumber({
  typed
});
export var oct = /* #__PURE__ */createOct({
  format,
  typed
});
export var pickRandom = /* #__PURE__ */createPickRandom({
  config,
  typed
});
export var print = /* #__PURE__ */createPrint({
  typed
});
export var random = /* #__PURE__ */createRandom({
  config,
  typed
});
export var re = /* #__PURE__ */createRe({
  typed
});
export var sec = /* #__PURE__ */createSec({
  BigNumber,
  typed
});
export var sign = /* #__PURE__ */createSign({
  BigNumber,
  Fraction,
  complex,
  typed
});
export var sin = /* #__PURE__ */createSin({
  typed
});
export var SparseMatrix = /* #__PURE__ */createSparseMatrixClass({
  Matrix,
  equalScalar,
  typed
});
export var splitUnit = /* #__PURE__ */createSplitUnit({
  typed
});
export var square = /* #__PURE__ */createSquare({
  typed
});
export var string = /* #__PURE__ */createString({
  typed
});
export var subtractScalar = /* #__PURE__ */createSubtractScalar({
  typed
});
export var tan = /* #__PURE__ */createTan({
  typed
});
export var toBest = /* #__PURE__ */createToBest({
  typed
});
export var typeOf = /* #__PURE__ */createTypeOf({
  typed
});
export var acosh = /* #__PURE__ */createAcosh({
  Complex,
  config,
  typed
});
export var acsch = /* #__PURE__ */createAcsch({
  BigNumber,
  typed
});
export var asec = /* #__PURE__ */createAsec({
  BigNumber,
  Complex,
  config,
  typed
});
export var bignumber = /* #__PURE__ */createBignumber({
  BigNumber,
  typed
});
export var combinationsWithRep = /* #__PURE__ */createCombinationsWithRep({
  typed
});
export var cosh = /* #__PURE__ */createCosh({
  typed
});
export var csch = /* #__PURE__ */createCsch({
  BigNumber,
  typed
});
export var isNaN = /* #__PURE__ */createIsNaN({
  typed
});
export var isPrime = /* #__PURE__ */createIsPrime({
  typed
});
export var mapSlices = /* #__PURE__ */createMapSlices({
  isInteger,
  typed
});
export var apply = mapSlices;
export var matrix = /* #__PURE__ */createMatrix({
  DenseMatrix,
  Matrix,
  SparseMatrix,
  typed
});
export var matrixFromFunction = /* #__PURE__ */createMatrixFromFunction({
  isZero,
  matrix,
  typed
});
export var ones = /* #__PURE__ */createOnes({
  BigNumber,
  config,
  matrix,
  typed
});
export var randomInt = /* #__PURE__ */createRandomInt({
  config,
  log2,
  typed
});
export var reshape = /* #__PURE__ */createReshape({
  isInteger,
  matrix,
  typed
});
export var sech = /* #__PURE__ */createSech({
  BigNumber,
  typed
});
export var sinh = /* #__PURE__ */createSinh({
  typed
});
export var sparse = /* #__PURE__ */createSparse({
  SparseMatrix,
  typed
});
export var sqrt = /* #__PURE__ */createSqrt({
  Complex,
  config,
  typed
});
export var squeeze = /* #__PURE__ */createSqueeze({
  typed
});
export var tanh = /* #__PURE__ */createTanh({
  typed
});
export var transpose = /* #__PURE__ */createTranspose({
  matrix,
  typed
});
export var xgcd = /* #__PURE__ */createXgcd({
  BigNumber,
  config,
  matrix,
  typed
});
export var zeros = /* #__PURE__ */createZeros({
  BigNumber,
  config,
  matrix,
  typed
});
export var acoth = /* #__PURE__ */createAcoth({
  BigNumber,
  Complex,
  config,
  typed
});
export var asin = /* #__PURE__ */createAsin({
  Complex,
  config,
  typed
});
export var bin = /* #__PURE__ */createBin({
  format,
  typed
});
export var concat = /* #__PURE__ */createConcat({
  isInteger,
  matrix,
  typed
});
export var coth = /* #__PURE__ */createCoth({
  BigNumber,
  typed
});
export var ctranspose = /* #__PURE__ */createCtranspose({
  conj,
  transpose,
  typed
});
export var diag = /* #__PURE__ */createDiag({
  DenseMatrix,
  SparseMatrix,
  matrix,
  typed
});
export var dotMultiply = /* #__PURE__ */createDotMultiply({
  concat,
  equalScalar,
  matrix,
  multiplyScalar,
  typed
});
export var equal = /* #__PURE__ */createEqual({
  DenseMatrix,
  SparseMatrix,
  concat,
  equalScalar,
  matrix,
  typed
});
export var fraction = /* #__PURE__ */createFraction({
  Fraction,
  typed
});
export var identity = /* #__PURE__ */createIdentity({
  BigNumber,
  DenseMatrix,
  SparseMatrix,
  config,
  matrix,
  typed
});
export var isNumeric = /* #__PURE__ */createIsNumeric({
  typed
});
export var kron = /* #__PURE__ */createKron({
  matrix,
  multiplyScalar,
  typed
});
export var largerEq = /* #__PURE__ */createLargerEq({
  DenseMatrix,
  SparseMatrix,
  concat,
  config,
  matrix,
  typed
});
export var leftShift = /* #__PURE__ */createLeftShift({
  DenseMatrix,
  concat,
  equalScalar,
  matrix,
  typed,
  zeros
});
export var mode = /* #__PURE__ */createMode({
  isNaN,
  isNumeric,
  typed
});
export var nthRoot = /* #__PURE__ */createNthRoot({
  BigNumber,
  concat,
  equalScalar,
  matrix,
  typed
});
export var numeric = /* #__PURE__ */createNumeric({
  bignumber,
  fraction,
  number
});
export var prod = /* #__PURE__ */createProd({
  config,
  multiplyScalar,
  numeric,
  typed
});
export var resize = /* #__PURE__ */createResize({
  config,
  matrix
});
export var rightArithShift = /* #__PURE__ */createRightArithShift({
  DenseMatrix,
  concat,
  equalScalar,
  matrix,
  typed,
  zeros
});
export var round = /* #__PURE__ */createRound({
  BigNumber,
  DenseMatrix,
  config,
  equalScalar,
  matrix,
  typed,
  zeros
});
export var size = /* #__PURE__ */createSize({
  matrix,
  config,
  typed
});
export var smaller = /* #__PURE__ */createSmaller({
  DenseMatrix,
  SparseMatrix,
  bignumber,
  concat,
  config,
  matrix,
  typed
});
export var to = /* #__PURE__ */createTo({
  concat,
  matrix,
  typed
});
export var unaryMinus = /* #__PURE__ */createUnaryMinus({
  typed
});
export var unequal = /* #__PURE__ */createUnequal({
  DenseMatrix,
  SparseMatrix,
  concat,
  config,
  equalScalar,
  matrix,
  typed
});
export var xor = /* #__PURE__ */createXor({
  DenseMatrix,
  SparseMatrix,
  concat,
  matrix,
  typed
});
export var add = /* #__PURE__ */createAdd({
  DenseMatrix,
  SparseMatrix,
  addScalar,
  concat,
  equalScalar,
  matrix,
  typed
});
export var atan2 = /* #__PURE__ */createAtan2({
  BigNumber,
  DenseMatrix,
  concat,
  equalScalar,
  matrix,
  typed
});
export var bitAnd = /* #__PURE__ */createBitAnd({
  concat,
  equalScalar,
  matrix,
  typed
});
export var bitOr = /* #__PURE__ */createBitOr({
  DenseMatrix,
  concat,
  equalScalar,
  matrix,
  typed
});
export var bitXor = /* #__PURE__ */createBitXor({
  DenseMatrix,
  SparseMatrix,
  concat,
  matrix,
  typed
});
export var cbrt = /* #__PURE__ */createCbrt({
  BigNumber,
  Complex,
  Fraction,
  config,
  isNegative,
  matrix,
  typed,
  unaryMinus
});
export var compare = /* #__PURE__ */createCompare({
  BigNumber,
  DenseMatrix,
  Fraction,
  concat,
  config,
  equalScalar,
  matrix,
  typed
});
export var compareText = /* #__PURE__ */createCompareText({
  concat,
  matrix,
  typed
});
export var count = /* #__PURE__ */createCount({
  prod,
  size,
  typed
});
export var deepEqual = /* #__PURE__ */createDeepEqual({
  equal,
  typed
});
export var divideScalar = /* #__PURE__ */createDivideScalar({
  numeric,
  typed
});
export var dotDivide = /* #__PURE__ */createDotDivide({
  DenseMatrix,
  SparseMatrix,
  concat,
  divideScalar,
  equalScalar,
  matrix,
  typed
});
export var equalText = /* #__PURE__ */createEqualText({
  compareText,
  isZero,
  typed
});
export var floor = /* #__PURE__ */createFloor({
  DenseMatrix,
  config,
  equalScalar,
  matrix,
  round,
  typed,
  zeros
});
export var gcd = /* #__PURE__ */createGcd({
  BigNumber,
  DenseMatrix,
  concat,
  config,
  equalScalar,
  matrix,
  round,
  typed,
  zeros
});
export var hasNumericValue = /* #__PURE__ */createHasNumericValue({
  isNumeric,
  typed
});
export var hypot = /* #__PURE__ */createHypot({
  abs,
  addScalar,
  divideScalar,
  isPositive,
  multiplyScalar,
  smaller,
  sqrt,
  typed
});
export var ImmutableDenseMatrix = /* #__PURE__ */createImmutableDenseMatrixClass({
  DenseMatrix,
  smaller
});
export var Index = /* #__PURE__ */createIndexClass({
  ImmutableDenseMatrix,
  getMatrixDataType
});
export var larger = /* #__PURE__ */createLarger({
  DenseMatrix,
  SparseMatrix,
  bignumber,
  concat,
  config,
  matrix,
  typed
});
export var log = /* #__PURE__ */createLog({
  Complex,
  config,
  divideScalar,
  typeOf,
  typed
});
export var lsolve = /* #__PURE__ */createLsolve({
  DenseMatrix,
  divideScalar,
  equalScalar,
  matrix,
  multiplyScalar,
  subtractScalar,
  typed
});
export var matrixFromColumns = /* #__PURE__ */createMatrixFromColumns({
  flatten,
  matrix,
  size,
  typed
});
export var max = /* #__PURE__ */createMax({
  config,
  isNaN,
  larger,
  numeric,
  typed
});
export var min = /* #__PURE__ */createMin({
  config,
  isNaN,
  numeric,
  smaller,
  typed
});
export var mod = /* #__PURE__ */createMod({
  DenseMatrix,
  concat,
  config,
  equalScalar,
  matrix,
  round,
  typed,
  zeros
});
export var nthRoots = /* #__PURE__ */createNthRoots({
  Complex,
  config,
  divideScalar,
  typed
});
export var or = /* #__PURE__ */createOr({
  DenseMatrix,
  concat,
  equalScalar,
  matrix,
  typed
});
export var partitionSelect = /* #__PURE__ */createPartitionSelect({
  compare,
  isNaN,
  isNumeric,
  typed
});
export var qr = /* #__PURE__ */createQr({
  addScalar,
  complex,
  conj,
  divideScalar,
  equal,
  identity,
  isZero,
  matrix,
  multiplyScalar,
  sign,
  sqrt,
  subtractScalar,
  typed,
  unaryMinus,
  zeros
});
export var rightLogShift = /* #__PURE__ */createRightLogShift({
  DenseMatrix,
  concat,
  equalScalar,
  matrix,
  typed,
  zeros
});
export var smallerEq = /* #__PURE__ */createSmallerEq({
  DenseMatrix,
  SparseMatrix,
  concat,
  config,
  matrix,
  typed
});
export var subset = /* #__PURE__ */createSubset({
  add,
  matrix,
  typed,
  zeros
});
export var subtract = /* #__PURE__ */createSubtract({
  DenseMatrix,
  concat,
  equalScalar,
  matrix,
  subtractScalar,
  typed,
  unaryMinus
});
export var trace = /* #__PURE__ */createTrace({
  add,
  matrix,
  typed
});
export var usolve = /* #__PURE__ */createUsolve({
  DenseMatrix,
  divideScalar,
  equalScalar,
  matrix,
  multiplyScalar,
  subtractScalar,
  typed
});
export var catalan = /* #__PURE__ */createCatalan({
  addScalar,
  combinations,
  divideScalar,
  isInteger,
  isNegative,
  multiplyScalar,
  typed
});
export var compareNatural = /* #__PURE__ */createCompareNatural({
  compare,
  typed
});
export var composition = /* #__PURE__ */createComposition({
  addScalar,
  combinations,
  isInteger,
  isNegative,
  isPositive,
  larger,
  typed
});
export var diff = /* #__PURE__ */createDiff({
  matrix,
  number,
  subtract,
  typed
});
export var distance = /* #__PURE__ */createDistance({
  abs,
  addScalar,
  deepEqual,
  divideScalar,
  multiplyScalar,
  sqrt,
  subtractScalar,
  typed
});
export var dot = /* #__PURE__ */createDot({
  addScalar,
  conj,
  multiplyScalar,
  size,
  typed
});
export var FibonacciHeap = /* #__PURE__ */createFibonacciHeapClass({
  larger,
  smaller
});
export var index = /* #__PURE__ */createIndex({
  Index,
  typed
});
export var invmod = /* #__PURE__ */createInvmod({
  BigNumber,
  add,
  config,
  equal,
  isInteger,
  mod,
  smaller,
  typed,
  xgcd
});
export var lcm = /* #__PURE__ */createLcm({
  concat,
  equalScalar,
  matrix,
  typed
});
export var log1p = /* #__PURE__ */createLog1p({
  Complex,
  config,
  divideScalar,
  log,
  typed
});
export var lsolveAll = /* #__PURE__ */createLsolveAll({
  DenseMatrix,
  divideScalar,
  equalScalar,
  matrix,
  multiplyScalar,
  subtractScalar,
  typed
});
export var matrixFromRows = /* #__PURE__ */createMatrixFromRows({
  flatten,
  matrix,
  size,
  typed
});
export var multiply = /* #__PURE__ */createMultiply({
  addScalar,
  dot,
  equalScalar,
  matrix,
  multiplyScalar,
  typed
});
export var range = /* #__PURE__ */createRange({
  bignumber,
  matrix,
  add,
  config,
  isPositive,
  larger,
  largerEq,
  smaller,
  smallerEq,
  typed
});
export var row = /* #__PURE__ */createRow({
  Index,
  matrix,
  range,
  typed
});
export var setCartesian = /* #__PURE__ */createSetCartesian({
  DenseMatrix,
  Index,
  compareNatural,
  size,
  subset,
  typed
});
export var setDistinct = /* #__PURE__ */createSetDistinct({
  DenseMatrix,
  Index,
  compareNatural,
  size,
  subset,
  typed
});
export var setIsSubset = /* #__PURE__ */createSetIsSubset({
  Index,
  compareNatural,
  size,
  subset,
  typed
});
export var setPowerset = /* #__PURE__ */createSetPowerset({
  Index,
  compareNatural,
  size,
  subset,
  typed
});
export var slu = /* #__PURE__ */createSlu({
  SparseMatrix,
  abs,
  add,
  divideScalar,
  larger,
  largerEq,
  multiply,
  subtract,
  transpose,
  typed
});
export var sort = /* #__PURE__ */createSort({
  compare,
  compareNatural,
  matrix,
  typed
});
export var unaryPlus = /* #__PURE__ */createUnaryPlus({
  config,
  numeric,
  typed
});
export var usolveAll = /* #__PURE__ */createUsolveAll({
  DenseMatrix,
  divideScalar,
  equalScalar,
  matrix,
  multiplyScalar,
  subtractScalar,
  typed
});
export var zpk2tf = /* #__PURE__ */createZpk2tf({
  Complex,
  add,
  multiply,
  number,
  typed
});
export var and = /* #__PURE__ */createAnd({
  concat,
  equalScalar,
  matrix,
  not,
  typed,
  zeros
});
export var ceil = /* #__PURE__ */createCeil({
  DenseMatrix,
  config,
  equalScalar,
  matrix,
  round,
  typed,
  zeros
});
export var column = /* #__PURE__ */createColumn({
  Index,
  matrix,
  range,
  typed
});
export var cross = /* #__PURE__ */createCross({
  matrix,
  multiply,
  subtract,
  typed
});
export var det = /* #__PURE__ */createDet({
  divideScalar,
  isZero,
  matrix,
  multiply,
  subtractScalar,
  typed,
  unaryMinus
});
export var fix = /* #__PURE__ */createFix({
  Complex,
  DenseMatrix,
  ceil,
  equalScalar,
  floor,
  matrix,
  typed,
  zeros
});
export var inv = /* #__PURE__ */createInv({
  abs,
  addScalar,
  det,
  divideScalar,
  identity,
  matrix,
  multiply,
  typed,
  unaryMinus
});
export var pinv = /* #__PURE__ */createPinv({
  Complex,
  add,
  ctranspose,
  deepEqual,
  divideScalar,
  dot,
  dotDivide,
  equal,
  inv,
  matrix,
  multiply,
  typed
});
export var pow = /* #__PURE__ */createPow({
  Complex,
  config,
  fraction,
  identity,
  inv,
  matrix,
  multiply,
  number,
  typed
});
export var setDifference = /* #__PURE__ */createSetDifference({
  DenseMatrix,
  Index,
  compareNatural,
  size,
  subset,
  typed
});
export var setMultiplicity = /* #__PURE__ */createSetMultiplicity({
  Index,
  compareNatural,
  size,
  subset,
  typed
});
export var setSymDifference = /* #__PURE__ */createSetSymDifference({
  Index,
  concat,
  setDifference,
  size,
  subset,
  typed
});
export var Spa = /* #__PURE__ */createSpaClass({
  FibonacciHeap,
  addScalar,
  equalScalar
});
export var sqrtm = /* #__PURE__ */createSqrtm({
  abs,
  add,
  identity,
  inv,
  map,
  max,
  multiply,
  size,
  sqrt,
  subtract,
  typed
});
export var sum = /* #__PURE__ */createSum({
  add,
  config,
  numeric,
  typed
});
export var Unit = /* #__PURE__ */createUnitClass({
  BigNumber,
  Complex,
  Fraction,
  abs,
  addScalar,
  config,
  divideScalar,
  equal,
  fix,
  format,
  isNumeric,
  multiplyScalar,
  number,
  pow,
  round,
  subtractScalar,
  toBest
});
export var vacuumImpedance = /* #__PURE__ */createVacuumImpedance({
  BigNumber,
  Unit,
  config
});
export var wienDisplacement = /* #__PURE__ */createWienDisplacement({
  BigNumber,
  Unit,
  config
});
export var atomicMass = /* #__PURE__ */createAtomicMass({
  BigNumber,
  Unit,
  config
});
export var bohrMagneton = /* #__PURE__ */createBohrMagneton({
  BigNumber,
  Unit,
  config
});
export var boltzmann = /* #__PURE__ */createBoltzmann({
  BigNumber,
  Unit,
  config
});
export var conductanceQuantum = /* #__PURE__ */createConductanceQuantum({
  BigNumber,
  Unit,
  config
});
export var coulomb = /* #__PURE__ */createCoulomb({
  BigNumber,
  Unit,
  config
});
export var createUnit = /* #__PURE__ */createCreateUnit({
  Unit,
  typed
});
export var deuteronMass = /* #__PURE__ */createDeuteronMass({
  BigNumber,
  Unit,
  config
});
export var dotPow = /* #__PURE__ */createDotPow({
  DenseMatrix,
  SparseMatrix,
  concat,
  equalScalar,
  matrix,
  pow,
  typed
});
export var electricConstant = /* #__PURE__ */createElectricConstant({
  BigNumber,
  Unit,
  config
});
export var elementaryCharge = /* #__PURE__ */createElementaryCharge({
  BigNumber,
  Unit,
  config
});
export var expm = /* #__PURE__ */createExpm({
  abs,
  add,
  identity,
  inv,
  multiply,
  typed
});
export var faraday = /* #__PURE__ */createFaraday({
  BigNumber,
  Unit,
  config
});
export var fft = /* #__PURE__ */createFft({
  addScalar,
  ceil,
  conj,
  divideScalar,
  dotDivide,
  exp,
  i,
  log2,
  matrix,
  multiplyScalar,
  pow,
  tau,
  typed
});
export var gamma = /* #__PURE__ */createGamma({
  BigNumber,
  Complex,
  config,
  multiplyScalar,
  pow,
  typed
});
export var gravitationConstant = /* #__PURE__ */createGravitationConstant({
  BigNumber,
  Unit,
  config
});
export var hartreeEnergy = /* #__PURE__ */createHartreeEnergy({
  BigNumber,
  Unit,
  config
});
export var ifft = /* #__PURE__ */createIfft({
  conj,
  dotDivide,
  fft,
  typed
});
export var inverseConductanceQuantum = /* #__PURE__ */createInverseConductanceQuantum({
  BigNumber,
  Unit,
  config
});
export var klitzing = /* #__PURE__ */createKlitzing({
  BigNumber,
  Unit,
  config
});
export var loschmidt = /* #__PURE__ */createLoschmidt({
  BigNumber,
  Unit,
  config
});
export var magneticConstant = /* #__PURE__ */createMagneticConstant({
  BigNumber,
  Unit,
  config
});
export var molarMass = /* #__PURE__ */createMolarMass({
  BigNumber,
  Unit,
  config
});
export var molarPlanckConstant = /* #__PURE__ */createMolarPlanckConstant({
  BigNumber,
  Unit,
  config
});
export var neutronMass = /* #__PURE__ */createNeutronMass({
  BigNumber,
  Unit,
  config
});
export var nuclearMagneton = /* #__PURE__ */createNuclearMagneton({
  BigNumber,
  Unit,
  config
});
export var planckCharge = /* #__PURE__ */createPlanckCharge({
  BigNumber,
  Unit,
  config
});
export var planckLength = /* #__PURE__ */createPlanckLength({
  BigNumber,
  Unit,
  config
});
export var planckTemperature = /* #__PURE__ */createPlanckTemperature({
  BigNumber,
  Unit,
  config
});
export var protonMass = /* #__PURE__ */createProtonMass({
  BigNumber,
  Unit,
  config
});
export var quantumOfCirculation = /* #__PURE__ */createQuantumOfCirculation({
  BigNumber,
  Unit,
  config
});
export var reducedPlanckConstant = /* #__PURE__ */createReducedPlanckConstant({
  BigNumber,
  Unit,
  config
});
export var rydberg = /* #__PURE__ */createRydberg({
  BigNumber,
  Unit,
  config
});
export var secondRadiation = /* #__PURE__ */createSecondRadiation({
  BigNumber,
  Unit,
  config
});
export var setSize = /* #__PURE__ */createSetSize({
  compareNatural,
  typed
});
export var speedOfLight = /* #__PURE__ */createSpeedOfLight({
  BigNumber,
  Unit,
  config
});
export var stefanBoltzmann = /* #__PURE__ */createStefanBoltzmann({
  BigNumber,
  Unit,
  config
});
export var thomsonCrossSection = /* #__PURE__ */createThomsonCrossSection({
  BigNumber,
  Unit,
  config
});
export var avogadro = /* #__PURE__ */createAvogadro({
  BigNumber,
  Unit,
  config
});
export var bohrRadius = /* #__PURE__ */createBohrRadius({
  BigNumber,
  Unit,
  config
});
export var coulombConstant = /* #__PURE__ */createCoulombConstant({
  BigNumber,
  Unit,
  config
});
export var divide = /* #__PURE__ */createDivide({
  divideScalar,
  equalScalar,
  inv,
  matrix,
  multiply,
  typed
});
export var electronMass = /* #__PURE__ */createElectronMass({
  BigNumber,
  Unit,
  config
});
export var factorial = /* #__PURE__ */createFactorial({
  gamma,
  typed
});
export var firstRadiation = /* #__PURE__ */createFirstRadiation({
  BigNumber,
  Unit,
  config
});
export var gravity = /* #__PURE__ */createGravity({
  BigNumber,
  Unit,
  config
});
export var intersect = /* #__PURE__ */createIntersect({
  abs,
  add,
  addScalar,
  config,
  divideScalar,
  equalScalar,
  flatten,
  isNumeric,
  isZero,
  matrix,
  multiply,
  multiplyScalar,
  smaller,
  subtract,
  typed
});
export var lup = /* #__PURE__ */createLup({
  DenseMatrix,
  Spa,
  SparseMatrix,
  abs,
  addScalar,
  divideScalar,
  equalScalar,
  larger,
  matrix,
  multiplyScalar,
  subtractScalar,
  typed,
  unaryMinus
});
export var magneticFluxQuantum = /* #__PURE__ */createMagneticFluxQuantum({
  BigNumber,
  Unit,
  config
});
export var molarMassC12 = /* #__PURE__ */createMolarMassC12({
  BigNumber,
  Unit,
  config
});
export var multinomial = /* #__PURE__ */createMultinomial({
  add,
  divide,
  factorial,
  isInteger,
  isPositive,
  multiply,
  typed
});
export var permutations = /* #__PURE__ */createPermutations({
  factorial,
  typed
});
export var planckMass = /* #__PURE__ */createPlanckMass({
  BigNumber,
  Unit,
  config
});
export var polynomialRoot = /* #__PURE__ */createPolynomialRoot({
  add,
  cbrt,
  divide,
  equalScalar,
  im,
  isZero,
  multiply,
  re,
  sqrt,
  subtract,
  typeOf,
  typed,
  unaryMinus
});
export var setIntersect = /* #__PURE__ */createSetIntersect({
  DenseMatrix,
  Index,
  compareNatural,
  size,
  subset,
  typed
});
export var solveODE = /* #__PURE__ */createSolveODE({
  abs,
  add,
  bignumber,
  divide,
  isNegative,
  isPositive,
  larger,
  map,
  matrix,
  max,
  multiply,
  smaller,
  subtract,
  typed,
  unaryMinus
});
export var stirlingS2 = /* #__PURE__ */createStirlingS2({
  bignumber,
  addScalar,
  combinations,
  divideScalar,
  factorial,
  isInteger,
  isNegative,
  larger,
  multiplyScalar,
  number,
  pow,
  subtractScalar,
  typed
});
export var unit = /* #__PURE__ */createUnitFunction({
  Unit,
  typed
});
export var bellNumbers = /* #__PURE__ */createBellNumbers({
  addScalar,
  isInteger,
  isNegative,
  stirlingS2,
  typed
});
export var cumsum = /* #__PURE__ */createCumSum({
  add,
  typed,
  unaryPlus
});
export var eigs = /* #__PURE__ */createEigs({
  abs,
  add,
  addScalar,
  atan,
  bignumber,
  column,
  complex,
  config,
  cos,
  diag,
  divideScalar,
  dot,
  equal,
  flatten,
  im,
  inv,
  larger,
  matrix,
  matrixFromColumns,
  multiply,
  multiplyScalar,
  number,
  qr,
  re,
  reshape,
  sin,
  size,
  smaller,
  sqrt,
  subtract,
  typed,
  usolve,
  usolveAll
});
export var fermiCoupling = /* #__PURE__ */createFermiCoupling({
  BigNumber,
  Unit,
  config
});
export var gasConstant = /* #__PURE__ */createGasConstant({
  BigNumber,
  Unit,
  config
});
export var kldivergence = /* #__PURE__ */createKldivergence({
  divide,
  dotDivide,
  isNumeric,
  log,
  map,
  matrix,
  multiply,
  sum,
  typed
});
export var lusolve = /* #__PURE__ */createLusolve({
  DenseMatrix,
  lsolve,
  lup,
  matrix,
  slu,
  typed,
  usolve
});
export var mean = /* #__PURE__ */createMean({
  add,
  divide,
  typed
});
export var molarVolume = /* #__PURE__ */createMolarVolume({
  BigNumber,
  Unit,
  config
});
export var planckConstant = /* #__PURE__ */createPlanckConstant({
  BigNumber,
  Unit,
  config
});
export var quantileSeq = /* #__PURE__ */createQuantileSeq({
  bignumber,
  add,
  compare,
  divide,
  isInteger,
  larger,
  mapSlices,
  multiply,
  partitionSelect,
  smaller,
  smallerEq,
  subtract,
  typed
});
export var setUnion = /* #__PURE__ */createSetUnion({
  Index,
  concat,
  setIntersect,
  setSymDifference,
  size,
  subset,
  typed
});
export var variance = /* #__PURE__ */createVariance({
  add,
  divide,
  isNaN,
  mapSlices,
  multiply,
  subtract,
  typed
});
export var classicalElectronRadius = /* #__PURE__ */createClassicalElectronRadius({
  BigNumber,
  Unit,
  config
});
export var median = /* #__PURE__ */createMedian({
  add,
  compare,
  divide,
  partitionSelect,
  typed
});
export var corr = /* #__PURE__ */createCorr({
  add,
  divide,
  matrix,
  mean,
  multiply,
  pow,
  sqrt,
  subtract,
  sum,
  typed
});
export var freqz = /* #__PURE__ */createFreqz({
  Complex,
  add,
  divide,
  matrix,
  multiply,
  typed
});
export var mad = /* #__PURE__ */createMad({
  abs,
  map,
  median,
  subtract,
  typed
});
export var std = /* #__PURE__ */createStd({
  map,
  sqrt,
  typed,
  variance
});
export var zeta = /* #__PURE__ */createZeta({
  BigNumber,
  Complex,
  add,
  config,
  divide,
  equal,
  factorial,
  gamma,
  isNegative,
  multiply,
  pi,
  pow,
  sin,
  smallerEq,
  subtract,
  typed
});
export var norm = /* #__PURE__ */createNorm({
  abs,
  add,
  conj,
  ctranspose,
  eigs,
  equalScalar,
  larger,
  matrix,
  multiply,
  pow,
  smaller,
  sqrt,
  typed
});
export var rotationMatrix = /* #__PURE__ */createRotationMatrix({
  BigNumber,
  DenseMatrix,
  SparseMatrix,
  addScalar,
  config,
  cos,
  matrix,
  multiplyScalar,
  norm,
  sin,
  typed,
  unaryMinus
});
export var planckTime = /* #__PURE__ */createPlanckTime({
  BigNumber,
  Unit,
  config
});
export var schur = /* #__PURE__ */createSchur({
  identity,
  matrix,
  multiply,
  norm,
  qr,
  subtract,
  typed
});
export var rotate = /* #__PURE__ */createRotate({
  multiply,
  rotationMatrix,
  typed
});
export var sylvester = /* #__PURE__ */createSylvester({
  abs,
  add,
  concat,
  identity,
  index,
  lusolve,
  matrix,
  matrixFromColumns,
  multiply,
  range,
  schur,
  subset,
  subtract,
  transpose,
  typed
});
export var lyap = /* #__PURE__ */createLyap({
  matrix,
  multiply,
  sylvester,
  transpose,
  typed
});