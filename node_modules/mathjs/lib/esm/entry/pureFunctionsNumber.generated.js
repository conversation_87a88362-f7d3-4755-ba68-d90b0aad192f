/**
 * THIS FILE IS AUTO-GENERATED
 * DON'T MAKE CHANGES HERE
 */
import { config } from './configReadonly.js';
import { createE, createFalse, createIndex, createInfinity, createLN10, createLOG10E, createMatrix, createNaN, createNull, createP<PERSON>, createRangeClass, createReplacer, createResultSet, createSQRT1_2,
// eslint-disable-line camelcase
createSubset, createTau, createTyped, createUnaryPlus, createVersion, createXor, createAbs, create<PERSON>cos, createAcot, createAcsc, createAdd, createAnd, createAsec, createAs<PERSON>, create<PERSON><PERSON>, createAtanh, createBigint, createBitNot, createBitXor, createBoolean, createCbrt, createCombinations, createCompare, createCompareText, createCos, createCot, createCsc, createCube, createDivide, createEqualScalar, createErf, createExp, createFilter, createForEach, createFormat, create<PERSON>am<PERSON>, createIsInteger, createIsNegative, createIsPositive, createIsZ<PERSON>, createLOG2E, createLargerEq, createLeftShift, createLog, createLog1p, createMap, createMean, createMod, createMultiply, createNot, createNumber, createOr, createPi, createPow, createRandom, createRightLogShift, createSQRT2, createSech, createSin, createSize, createSmallerEq, createSquare, createString, createSubtract, createTanh, createTypeOf, createUnequal, createXgcd, createAcoth, createAddScalar, createAsech, createBitAnd, createCombinationsWithRep, createCosh, createCsch, createDivideScalar, createEqualText, createExpm1, createIsNumeric, createLN2, createLcm, createLog10, createMapSlices, createMultiplyScalar, createNthRoot, createPickRandom, createRightArithShift, createSec, createSinh, createSqrt, createTan, createUnaryMinus, createAcosh, createAtan2, createBitOr, createClone, createCoth, createEqual, createFactorial, createHasNumericValue, createIsNaN, createLarger, createLog2, createMode, createNorm, createPartitionSelect, createPrint, createRandomInt, createRound, createSmaller, createSubtractScalar, createTrue, createVariance, createZeta, createAcsch, createCatalan, createCompareNatural, createComposition, createCumSum, createFloor, createHypot, createLgamma, createMedian, createMultinomial, createPermutations, createQuantileSeq, createSign, createStd, createStirlingS2, createAsinh, createCeil, createDeepEqual, createFix, createIsPrime, createNumeric, createProd, createBellNumbers, createGcd, createMad, createRange, createSum, createCorr, createMax, createMin } from '../factoriesNumber.js';
export var e = /* #__PURE__ */createE({
  config
});
export var _false = /* #__PURE__ */createFalse({});
export var index = /* #__PURE__ */createIndex({});
export var _Infinity = /* #__PURE__ */createInfinity({
  config
});
export var LN10 = /* #__PURE__ */createLN10({
  config
});
export var LOG10E = /* #__PURE__ */createLOG10E({
  config
});
export var matrix = /* #__PURE__ */createMatrix({});
export var _NaN = /* #__PURE__ */createNaN({
  config
});
export var _null = /* #__PURE__ */createNull({});
export var phi = /* #__PURE__ */createPhi({
  config
});
export var Range = /* #__PURE__ */createRangeClass({});
export var replacer = /* #__PURE__ */createReplacer({});
export var ResultSet = /* #__PURE__ */createResultSet({});
export var SQRT1_2 = /* #__PURE__ */createSQRT1_2({
  config
});
export var subset = /* #__PURE__ */createSubset({});
export var tau = /* #__PURE__ */createTau({
  config
});
export var typed = /* #__PURE__ */createTyped({});
export var unaryPlus = /* #__PURE__ */createUnaryPlus({
  typed
});
export var version = /* #__PURE__ */createVersion({});
export var xor = /* #__PURE__ */createXor({
  typed
});
export var abs = /* #__PURE__ */createAbs({
  typed
});
export var acos = /* #__PURE__ */createAcos({
  typed
});
export var acot = /* #__PURE__ */createAcot({
  typed
});
export var acsc = /* #__PURE__ */createAcsc({
  typed
});
export var add = /* #__PURE__ */createAdd({
  typed
});
export var and = /* #__PURE__ */createAnd({
  typed
});
export var asec = /* #__PURE__ */createAsec({
  typed
});
export var asin = /* #__PURE__ */createAsin({
  typed
});
export var atan = /* #__PURE__ */createAtan({
  typed
});
export var atanh = /* #__PURE__ */createAtanh({
  typed
});
export var bigint = /* #__PURE__ */createBigint({
  typed
});
export var bitNot = /* #__PURE__ */createBitNot({
  typed
});
export var bitXor = /* #__PURE__ */createBitXor({
  typed
});
export var boolean = /* #__PURE__ */createBoolean({
  typed
});
export var cbrt = /* #__PURE__ */createCbrt({
  typed
});
export var combinations = /* #__PURE__ */createCombinations({
  typed
});
export var compare = /* #__PURE__ */createCompare({
  config,
  typed
});
export var compareText = /* #__PURE__ */createCompareText({
  typed
});
export var cos = /* #__PURE__ */createCos({
  typed
});
export var cot = /* #__PURE__ */createCot({
  typed
});
export var csc = /* #__PURE__ */createCsc({
  typed
});
export var cube = /* #__PURE__ */createCube({
  typed
});
export var divide = /* #__PURE__ */createDivide({
  typed
});
export var equalScalar = /* #__PURE__ */createEqualScalar({
  config,
  typed
});
export var erf = /* #__PURE__ */createErf({
  typed
});
export var exp = /* #__PURE__ */createExp({
  typed
});
export var filter = /* #__PURE__ */createFilter({
  typed
});
export var forEach = /* #__PURE__ */createForEach({
  typed
});
export var format = /* #__PURE__ */createFormat({
  typed
});
export var gamma = /* #__PURE__ */createGamma({
  typed
});
export var isInteger = /* #__PURE__ */createIsInteger({
  typed
});
export var isNegative = /* #__PURE__ */createIsNegative({
  typed
});
export var isPositive = /* #__PURE__ */createIsPositive({
  typed
});
export var isZero = /* #__PURE__ */createIsZero({
  typed
});
export var LOG2E = /* #__PURE__ */createLOG2E({
  config
});
export var largerEq = /* #__PURE__ */createLargerEq({
  config,
  typed
});
export var leftShift = /* #__PURE__ */createLeftShift({
  typed
});
export var log = /* #__PURE__ */createLog({
  typed
});
export var log1p = /* #__PURE__ */createLog1p({
  typed
});
export var map = /* #__PURE__ */createMap({
  typed
});
export var mean = /* #__PURE__ */createMean({
  add,
  divide,
  typed
});
export var mod = /* #__PURE__ */createMod({
  typed
});
export var multiply = /* #__PURE__ */createMultiply({
  typed
});
export var not = /* #__PURE__ */createNot({
  typed
});
export var number = /* #__PURE__ */createNumber({
  typed
});
export var or = /* #__PURE__ */createOr({
  typed
});
export var pi = /* #__PURE__ */createPi({
  config
});
export var pow = /* #__PURE__ */createPow({
  typed
});
export var random = /* #__PURE__ */createRandom({
  config,
  typed
});
export var rightLogShift = /* #__PURE__ */createRightLogShift({
  typed
});
export var SQRT2 = /* #__PURE__ */createSQRT2({
  config
});
export var sech = /* #__PURE__ */createSech({
  typed
});
export var sin = /* #__PURE__ */createSin({
  typed
});
export var size = /* #__PURE__ */createSize({
  matrix,
  config,
  typed
});
export var smallerEq = /* #__PURE__ */createSmallerEq({
  config,
  typed
});
export var square = /* #__PURE__ */createSquare({
  typed
});
export var string = /* #__PURE__ */createString({
  typed
});
export var subtract = /* #__PURE__ */createSubtract({
  typed
});
export var tanh = /* #__PURE__ */createTanh({
  typed
});
export var typeOf = /* #__PURE__ */createTypeOf({
  typed
});
export var unequal = /* #__PURE__ */createUnequal({
  equalScalar,
  typed
});
export var xgcd = /* #__PURE__ */createXgcd({
  typed
});
export var acoth = /* #__PURE__ */createAcoth({
  typed
});
export var addScalar = /* #__PURE__ */createAddScalar({
  typed
});
export var asech = /* #__PURE__ */createAsech({
  typed
});
export var bitAnd = /* #__PURE__ */createBitAnd({
  typed
});
export var combinationsWithRep = /* #__PURE__ */createCombinationsWithRep({
  typed
});
export var cosh = /* #__PURE__ */createCosh({
  typed
});
export var csch = /* #__PURE__ */createCsch({
  typed
});
export var divideScalar = /* #__PURE__ */createDivideScalar({
  typed
});
export var equalText = /* #__PURE__ */createEqualText({
  compareText,
  isZero,
  typed
});
export var expm1 = /* #__PURE__ */createExpm1({
  typed
});
export var isNumeric = /* #__PURE__ */createIsNumeric({
  typed
});
export var LN2 = /* #__PURE__ */createLN2({
  config
});
export var lcm = /* #__PURE__ */createLcm({
  typed
});
export var log10 = /* #__PURE__ */createLog10({
  typed
});
export var mapSlices = /* #__PURE__ */createMapSlices({
  isInteger,
  typed
});
export var apply = mapSlices;
export var multiplyScalar = /* #__PURE__ */createMultiplyScalar({
  typed
});
export var nthRoot = /* #__PURE__ */createNthRoot({
  typed
});
export var pickRandom = /* #__PURE__ */createPickRandom({
  config,
  typed
});
export var rightArithShift = /* #__PURE__ */createRightArithShift({
  typed
});
export var sec = /* #__PURE__ */createSec({
  typed
});
export var sinh = /* #__PURE__ */createSinh({
  typed
});
export var sqrt = /* #__PURE__ */createSqrt({
  typed
});
export var tan = /* #__PURE__ */createTan({
  typed
});
export var unaryMinus = /* #__PURE__ */createUnaryMinus({
  typed
});
export var acosh = /* #__PURE__ */createAcosh({
  typed
});
export var atan2 = /* #__PURE__ */createAtan2({
  typed
});
export var bitOr = /* #__PURE__ */createBitOr({
  typed
});
export var clone = /* #__PURE__ */createClone({
  typed
});
export var coth = /* #__PURE__ */createCoth({
  typed
});
export var equal = /* #__PURE__ */createEqual({
  equalScalar,
  typed
});
export var factorial = /* #__PURE__ */createFactorial({
  gamma,
  typed
});
export var hasNumericValue = /* #__PURE__ */createHasNumericValue({
  isNumeric,
  typed
});
export var isNaN = /* #__PURE__ */createIsNaN({
  typed
});
export var larger = /* #__PURE__ */createLarger({
  config,
  typed
});
export var log2 = /* #__PURE__ */createLog2({
  typed
});
export var mode = /* #__PURE__ */createMode({
  isNaN,
  isNumeric,
  typed
});
export var norm = /* #__PURE__ */createNorm({
  typed
});
export var partitionSelect = /* #__PURE__ */createPartitionSelect({
  compare,
  isNaN,
  isNumeric,
  typed
});
export var print = /* #__PURE__ */createPrint({
  typed
});
export var randomInt = /* #__PURE__ */createRandomInt({
  config,
  log2,
  typed
});
export var round = /* #__PURE__ */createRound({
  typed
});
export var smaller = /* #__PURE__ */createSmaller({
  config,
  typed
});
export var subtractScalar = /* #__PURE__ */createSubtractScalar({
  typed
});
export var _true = /* #__PURE__ */createTrue({});
export var variance = /* #__PURE__ */createVariance({
  add,
  divide,
  isNaN,
  mapSlices,
  multiply,
  subtract,
  typed
});
export var zeta = /* #__PURE__ */createZeta({
  add,
  config,
  divide,
  equal,
  factorial,
  gamma,
  isNegative,
  multiply,
  pi,
  pow,
  sin,
  smallerEq,
  subtract,
  typed
});
export var acsch = /* #__PURE__ */createAcsch({
  typed
});
export var catalan = /* #__PURE__ */createCatalan({
  addScalar,
  combinations,
  divideScalar,
  isInteger,
  isNegative,
  multiplyScalar,
  typed
});
export var compareNatural = /* #__PURE__ */createCompareNatural({
  compare,
  typed
});
export var composition = /* #__PURE__ */createComposition({
  addScalar,
  combinations,
  isInteger,
  isNegative,
  isPositive,
  larger,
  typed
});
export var cumsum = /* #__PURE__ */createCumSum({
  add,
  typed,
  unaryPlus
});
export var floor = /* #__PURE__ */createFloor({
  config,
  round,
  typed
});
export var hypot = /* #__PURE__ */createHypot({
  abs,
  addScalar,
  divideScalar,
  isPositive,
  multiplyScalar,
  smaller,
  sqrt,
  typed
});
export var lgamma = /* #__PURE__ */createLgamma({
  typed
});
export var median = /* #__PURE__ */createMedian({
  add,
  compare,
  divide,
  partitionSelect,
  typed
});
export var multinomial = /* #__PURE__ */createMultinomial({
  add,
  divide,
  factorial,
  isInteger,
  isPositive,
  multiply,
  typed
});
export var permutations = /* #__PURE__ */createPermutations({
  factorial,
  typed
});
export var quantileSeq = /* #__PURE__ */createQuantileSeq({
  add,
  compare,
  divide,
  isInteger,
  larger,
  mapSlices,
  multiply,
  partitionSelect,
  smaller,
  smallerEq,
  subtract,
  typed
});
export var sign = /* #__PURE__ */createSign({
  typed
});
export var std = /* #__PURE__ */createStd({
  map,
  sqrt,
  typed,
  variance
});
export var stirlingS2 = /* #__PURE__ */createStirlingS2({
  addScalar,
  combinations,
  divideScalar,
  factorial,
  isInteger,
  isNegative,
  larger,
  multiplyScalar,
  number,
  pow,
  subtractScalar,
  typed
});
export var asinh = /* #__PURE__ */createAsinh({
  typed
});
export var ceil = /* #__PURE__ */createCeil({
  config,
  round,
  typed
});
export var deepEqual = /* #__PURE__ */createDeepEqual({
  equal,
  typed
});
export var fix = /* #__PURE__ */createFix({
  ceil,
  floor,
  typed
});
export var isPrime = /* #__PURE__ */createIsPrime({
  typed
});
export var numeric = /* #__PURE__ */createNumeric({
  number
});
export var prod = /* #__PURE__ */createProd({
  config,
  multiplyScalar,
  numeric,
  typed
});
export var bellNumbers = /* #__PURE__ */createBellNumbers({
  addScalar,
  isInteger,
  isNegative,
  stirlingS2,
  typed
});
export var gcd = /* #__PURE__ */createGcd({
  typed
});
export var mad = /* #__PURE__ */createMad({
  abs,
  map,
  median,
  subtract,
  typed
});
export var range = /* #__PURE__ */createRange({
  matrix,
  add,
  config,
  isPositive,
  larger,
  largerEq,
  smaller,
  smallerEq,
  typed
});
export var sum = /* #__PURE__ */createSum({
  add,
  config,
  numeric,
  typed
});
export var corr = /* #__PURE__ */createCorr({
  add,
  divide,
  matrix,
  mean,
  multiply,
  pow,
  sqrt,
  subtract,
  sum,
  typed
});
export var max = /* #__PURE__ */createMax({
  config,
  isNaN,
  larger,
  numeric,
  typed
});
export var min = /* #__PURE__ */createMin({
  config,
  isNaN,
  numeric,
  smaller,
  typed
});