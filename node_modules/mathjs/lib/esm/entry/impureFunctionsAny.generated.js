import _extends from "@babel/runtime/helpers/extends";
/**
 * THIS FILE IS AUTO-GENERATED
 * DON'T MAKE CHANGES HERE
 */
import { config } from './configReadonly.js';
import { createNode, createObjectNode, createOperator<PERSON><PERSON>, createParenthesisNode, createRelationalNode, createArrayNode, createBlockNode, createConditionalNode, createConstantNode, createRangeNode, createReviver, create<PERSON>hainClass, createFunctionAssignmentNode, create<PERSON>hai<PERSON>, createAccessorNode, createAssignmentNode, createIndexNode, createSymbolNode, createFunctionNode, createParse, createResolve, createSimplifyConstant, createCompile, createSimplifyC<PERSON>, createEvaluate, createHelpClass, createParserClass, createSimplify, createSymbolicEqual, createLeafCount, createParser, createRationalize, createDerivative, create<PERSON>el<PERSON>, createMapSlicesTransform, createFilterTransform, createForEachTransform, createMapTransform, createOrTransform, createAndTransform, createConcatTransform, createIndexTransform, createPrintTransform, createSumTransform, createBitAndTransform, createMinTransform, createSubsetTransform, createBitOrTransform, createCumSumTransform, createDiffTransform, createMaxTransform, createRangeTransform, createRowTransform, createColumnTransform, createMeanTransform, createQuantileSeqTransform, createVarianceTransform, createStdTransform } from '../factoriesAny.js';
import { BigNumber, Complex, e, _false, fineStructure, Fraction, i, _Infinity, LN10, LOG10E, Matrix, _NaN, _null, phi, Range, ResultSet, SQRT1_2,
// eslint-disable-line camelcase
sackurTetrode, tau, _true, version, DenseMatrix, efimovFactor, LN2, pi, replacer, SQRT2, typed, weakMixingAngle, abs, acos, acot, acsc, addScalar, arg, asech, asinh, atan, atanh, bigint, bitNot, boolean, clone, combinations, complex, conj, cos, cot, csc, cube, equalScalar, erf, exp, expm1, filter, flatten, forEach, format, getMatrixDataType, hex, im, isInteger, isNegative, isPositive, isZero, LOG2E, lgamma, log10, log2, map, multiplyScalar, not, number, oct, pickRandom, print, random, re, sec, sign, sin, SparseMatrix, splitUnit, square, string, subtractScalar, tan, toBest, typeOf, acosh, acsch, asec, bignumber, combinationsWithRep, cosh, csch, isNaN, isPrime, mapSlices, matrix, matrixFromFunction, ones, randomInt, reshape, sech, sinh, sparse, sqrt, squeeze, tanh, transpose, xgcd, zeros, acoth, asin, bin, concat, coth, ctranspose, diag, dotMultiply, equal, fraction, identity, isNumeric, kron, largerEq, leftShift, mode, nthRoot, numeric, prod, resize, rightArithShift, round, size, smaller, to, unaryMinus, unequal, xor, add, atan2, bitAnd, bitOr, bitXor, cbrt, compare, compareText, count, deepEqual, divideScalar, dotDivide, equalText, floor, gcd, hasNumericValue, hypot, ImmutableDenseMatrix, Index, larger, log, lsolve, matrixFromColumns, max, min, mod, nthRoots, or, partitionSelect, qr, rightLogShift, smallerEq, subset, subtract, trace, usolve, catalan, compareNatural, composition, diff, distance, dot, FibonacciHeap, index, invmod, lcm, log1p, lsolveAll, matrixFromRows, multiply, range, row, setCartesian, setDistinct, setIsSubset, setPowerset, slu, sort, unaryPlus, usolveAll, zpk2tf, and, ceil, column, cross, det, fix, inv, pinv, pow, setDifference, setMultiplicity, setSymDifference, Spa, sqrtm, sum, Unit, vacuumImpedance, wienDisplacement, atomicMass, bohrMagneton, boltzmann, conductanceQuantum, coulomb, createUnit, deuteronMass, dotPow, electricConstant, elementaryCharge, expm, faraday, fft, gamma, gravitationConstant, hartreeEnergy, ifft, inverseConductanceQuantum, klitzing, loschmidt, magneticConstant, molarMass, molarPlanckConstant, neutronMass, nuclearMagneton, planckCharge, planckLength, planckTemperature, protonMass, quantumOfCirculation, reducedPlanckConstant, rydberg, secondRadiation, setSize, speedOfLight, stefanBoltzmann, thomsonCrossSection, avogadro, bohrRadius, coulombConstant, divide, electronMass, factorial, firstRadiation, gravity, intersect, lup, magneticFluxQuantum, molarMassC12, multinomial, permutations, planckMass, polynomialRoot, setIntersect, solveODE, stirlingS2, unit, bellNumbers, cumsum, eigs, fermiCoupling, gasConstant, kldivergence, lusolve, mean, molarVolume, planckConstant, quantileSeq, setUnion, variance, classicalElectronRadius, median, corr, freqz, mad, std, zeta, norm, rotationMatrix, planckTime, schur, rotate, sylvester, lyap } from './pureFunctionsAny.generated.js';
var math = {}; // NOT pure!
var mathWithTransform = {}; // NOT pure!
var classes = {}; // NOT pure!

export var Node = createNode({
  mathWithTransform
});
export var ObjectNode = createObjectNode({
  Node
});
export var OperatorNode = createOperatorNode({
  Node
});
export var ParenthesisNode = createParenthesisNode({
  Node
});
export var RelationalNode = createRelationalNode({
  Node
});
export var ArrayNode = createArrayNode({
  Node
});
export var BlockNode = createBlockNode({
  Node,
  ResultSet
});
export var ConditionalNode = createConditionalNode({
  Node
});
export var ConstantNode = createConstantNode({
  Node
});
export var RangeNode = createRangeNode({
  Node
});
export var reviver = createReviver({
  classes
});
export var Chain = createChainClass({
  math,
  typed
});
export var FunctionAssignmentNode = createFunctionAssignmentNode({
  Node,
  typed
});
export var chain = createChain({
  Chain,
  typed
});
export var AccessorNode = createAccessorNode({
  Node,
  subset
});
export var AssignmentNode = createAssignmentNode({
  matrix,
  Node,
  subset
});
export var IndexNode = createIndexNode({
  Node,
  size
});
export var SymbolNode = createSymbolNode({
  Unit,
  Node,
  math
});
export var FunctionNode = createFunctionNode({
  Node,
  SymbolNode,
  math
});
export var parse = createParse({
  AccessorNode,
  ArrayNode,
  AssignmentNode,
  BlockNode,
  ConditionalNode,
  ConstantNode,
  FunctionAssignmentNode,
  FunctionNode,
  IndexNode,
  ObjectNode,
  OperatorNode,
  ParenthesisNode,
  RangeNode,
  RelationalNode,
  SymbolNode,
  config,
  numeric,
  typed
});
export var resolve = createResolve({
  ConstantNode,
  FunctionNode,
  OperatorNode,
  ParenthesisNode,
  parse,
  typed
});
export var simplifyConstant = createSimplifyConstant({
  bignumber,
  fraction,
  AccessorNode,
  ArrayNode,
  ConstantNode,
  FunctionNode,
  IndexNode,
  ObjectNode,
  OperatorNode,
  SymbolNode,
  config,
  mathWithTransform,
  matrix,
  typed
});
export var compile = createCompile({
  parse,
  typed
});
export var simplifyCore = createSimplifyCore({
  AccessorNode,
  ArrayNode,
  ConstantNode,
  FunctionNode,
  IndexNode,
  ObjectNode,
  OperatorNode,
  ParenthesisNode,
  SymbolNode,
  add,
  divide,
  equal,
  isZero,
  multiply,
  parse,
  pow,
  subtract,
  typed
});
export var evaluate = createEvaluate({
  parse,
  typed
});
export var Help = createHelpClass({
  evaluate
});
export var Parser = createParserClass({
  evaluate,
  parse
});
export var simplify = createSimplify({
  AccessorNode,
  ArrayNode,
  ConstantNode,
  FunctionNode,
  IndexNode,
  ObjectNode,
  OperatorNode,
  ParenthesisNode,
  SymbolNode,
  equal,
  parse,
  replacer,
  resolve,
  simplifyConstant,
  simplifyCore,
  typed
});
export var symbolicEqual = createSymbolicEqual({
  OperatorNode,
  parse,
  simplify,
  typed
});
export var leafCount = createLeafCount({
  parse,
  typed
});
export var parser = createParser({
  Parser,
  typed
});
export var rationalize = createRationalize({
  bignumber,
  fraction,
  AccessorNode,
  ArrayNode,
  ConstantNode,
  FunctionNode,
  IndexNode,
  ObjectNode,
  OperatorNode,
  ParenthesisNode,
  SymbolNode,
  add,
  config,
  divide,
  equal,
  isZero,
  mathWithTransform,
  matrix,
  multiply,
  parse,
  pow,
  simplify,
  simplifyConstant,
  simplifyCore,
  subtract,
  typed
});
export var derivative = createDerivative({
  ConstantNode,
  FunctionNode,
  OperatorNode,
  ParenthesisNode,
  SymbolNode,
  config,
  equal,
  isZero,
  numeric,
  parse,
  simplify,
  typed
});
export var help = createHelp({
  Help,
  mathWithTransform,
  typed
});
_extends(math, {
  e,
  false: _false,
  fineStructure,
  i,
  Infinity: _Infinity,
  LN10,
  LOG10E,
  NaN: _NaN,
  null: _null,
  phi,
  SQRT1_2,
  sackurTetrode,
  tau,
  true: _true,
  'E': e,
  version,
  efimovFactor,
  LN2,
  pi,
  replacer,
  reviver,
  SQRT2,
  typed,
  'PI': pi,
  weakMixingAngle,
  abs,
  acos,
  acot,
  acsc,
  addScalar,
  arg,
  asech,
  asinh,
  atan,
  atanh,
  bigint,
  bitNot,
  boolean,
  clone,
  combinations,
  complex,
  conj,
  cos,
  cot,
  csc,
  cube,
  equalScalar,
  erf,
  exp,
  expm1,
  filter,
  flatten,
  forEach,
  format,
  getMatrixDataType,
  hex,
  im,
  isInteger,
  isNegative,
  isPositive,
  isZero,
  LOG2E,
  lgamma,
  log10,
  log2,
  map,
  multiplyScalar,
  not,
  number,
  oct,
  pickRandom,
  print,
  random,
  re,
  sec,
  sign,
  sin,
  splitUnit,
  square,
  string,
  subtractScalar,
  tan,
  toBest,
  typeOf,
  acosh,
  acsch,
  asec,
  bignumber,
  chain,
  combinationsWithRep,
  cosh,
  csch,
  isNaN,
  isPrime,
  mapSlices,
  matrix,
  matrixFromFunction,
  ones,
  randomInt,
  reshape,
  sech,
  sinh,
  sparse,
  sqrt,
  squeeze,
  tanh,
  transpose,
  xgcd,
  zeros,
  acoth,
  asin,
  bin,
  concat,
  coth,
  ctranspose,
  diag,
  dotMultiply,
  equal,
  fraction,
  identity,
  isNumeric,
  kron,
  largerEq,
  leftShift,
  mode,
  nthRoot,
  numeric,
  prod,
  resize,
  rightArithShift,
  round,
  size,
  smaller,
  to,
  unaryMinus,
  unequal,
  xor,
  add,
  atan2,
  bitAnd,
  bitOr,
  bitXor,
  cbrt,
  compare,
  compareText,
  count,
  deepEqual,
  divideScalar,
  dotDivide,
  equalText,
  floor,
  gcd,
  hasNumericValue,
  hypot,
  larger,
  log,
  lsolve,
  matrixFromColumns,
  max,
  min,
  mod,
  nthRoots,
  or,
  partitionSelect,
  qr,
  rightLogShift,
  smallerEq,
  subset,
  subtract,
  trace,
  usolve,
  catalan,
  compareNatural,
  composition,
  diff,
  distance,
  dot,
  index,
  invmod,
  lcm,
  log1p,
  lsolveAll,
  matrixFromRows,
  multiply,
  range,
  row,
  setCartesian,
  setDistinct,
  setIsSubset,
  setPowerset,
  slu,
  sort,
  unaryPlus,
  usolveAll,
  zpk2tf,
  and,
  ceil,
  column,
  cross,
  det,
  fix,
  inv,
  pinv,
  pow,
  setDifference,
  setMultiplicity,
  setSymDifference,
  sqrtm,
  sum,
  vacuumImpedance,
  wienDisplacement,
  atomicMass,
  bohrMagneton,
  boltzmann,
  conductanceQuantum,
  coulomb,
  createUnit,
  deuteronMass,
  dotPow,
  electricConstant,
  elementaryCharge,
  expm,
  faraday,
  fft,
  gamma,
  gravitationConstant,
  hartreeEnergy,
  ifft,
  inverseConductanceQuantum,
  klitzing,
  loschmidt,
  magneticConstant,
  molarMass,
  molarPlanckConstant,
  neutronMass,
  nuclearMagneton,
  planckCharge,
  planckLength,
  planckTemperature,
  protonMass,
  quantumOfCirculation,
  reducedPlanckConstant,
  rydberg,
  secondRadiation,
  setSize,
  speedOfLight,
  stefanBoltzmann,
  thomsonCrossSection,
  avogadro,
  bohrRadius,
  coulombConstant,
  divide,
  electronMass,
  factorial,
  firstRadiation,
  gravity,
  intersect,
  lup,
  magneticFluxQuantum,
  molarMassC12,
  multinomial,
  parse,
  permutations,
  planckMass,
  polynomialRoot,
  resolve,
  setIntersect,
  simplifyConstant,
  solveODE,
  stirlingS2,
  unit,
  bellNumbers,
  compile,
  cumsum,
  eigs,
  fermiCoupling,
  gasConstant,
  kldivergence,
  lusolve,
  mean,
  molarVolume,
  planckConstant,
  quantileSeq,
  setUnion,
  simplifyCore,
  variance,
  classicalElectronRadius,
  evaluate,
  median,
  simplify,
  symbolicEqual,
  corr,
  freqz,
  leafCount,
  mad,
  parser,
  rationalize,
  std,
  zeta,
  derivative,
  norm,
  rotationMatrix,
  help,
  planckTime,
  schur,
  rotate,
  sylvester,
  lyap,
  config
});
_extends(mathWithTransform, math, {
  mapSlices: createMapSlicesTransform({
    isInteger,
    typed
  }),
  filter: createFilterTransform({
    typed
  }),
  forEach: createForEachTransform({
    typed
  }),
  map: createMapTransform({
    typed
  }),
  or: createOrTransform({
    DenseMatrix,
    concat,
    equalScalar,
    matrix,
    typed
  }),
  and: createAndTransform({
    add,
    concat,
    equalScalar,
    matrix,
    not,
    typed,
    zeros
  }),
  concat: createConcatTransform({
    isInteger,
    matrix,
    typed
  }),
  index: createIndexTransform({
    Index,
    getMatrixDataType
  }),
  print: createPrintTransform({
    add,
    matrix,
    typed,
    zeros
  }),
  sum: createSumTransform({
    add,
    config,
    numeric,
    typed
  }),
  bitAnd: createBitAndTransform({
    add,
    concat,
    equalScalar,
    matrix,
    not,
    typed,
    zeros
  }),
  min: createMinTransform({
    config,
    isNaN,
    numeric,
    smaller,
    typed
  }),
  subset: createSubsetTransform({
    add,
    matrix,
    typed,
    zeros
  }),
  bitOr: createBitOrTransform({
    DenseMatrix,
    concat,
    equalScalar,
    matrix,
    typed
  }),
  cumsum: createCumSumTransform({
    add,
    typed,
    unaryPlus
  }),
  diff: createDiffTransform({
    bignumber,
    matrix,
    number,
    subtract,
    typed
  }),
  max: createMaxTransform({
    config,
    isNaN,
    larger,
    numeric,
    typed
  }),
  range: createRangeTransform({
    bignumber,
    matrix,
    add,
    config,
    isPositive,
    larger,
    largerEq,
    smaller,
    smallerEq,
    typed
  }),
  row: createRowTransform({
    Index,
    matrix,
    range,
    typed
  }),
  column: createColumnTransform({
    Index,
    matrix,
    range,
    typed
  }),
  mean: createMeanTransform({
    add,
    divide,
    typed
  }),
  quantileSeq: createQuantileSeqTransform({
    add,
    bignumber,
    compare,
    divide,
    isInteger,
    larger,
    mapSlices,
    multiply,
    partitionSelect,
    smaller,
    smallerEq,
    subtract,
    typed
  }),
  variance: createVarianceTransform({
    add,
    divide,
    isNaN,
    mapSlices,
    multiply,
    subtract,
    typed
  }),
  std: createStdTransform({
    map,
    sqrt,
    typed,
    variance
  })
});
_extends(classes, {
  BigNumber,
  Complex,
  Fraction,
  Matrix,
  Node,
  ObjectNode,
  OperatorNode,
  ParenthesisNode,
  Range,
  RelationalNode,
  ResultSet,
  ArrayNode,
  BlockNode,
  ConditionalNode,
  ConstantNode,
  DenseMatrix,
  RangeNode,
  Chain,
  FunctionAssignmentNode,
  SparseMatrix,
  ImmutableDenseMatrix,
  Index,
  AccessorNode,
  AssignmentNode,
  FibonacciHeap,
  IndexNode,
  Spa,
  Unit,
  SymbolNode,
  FunctionNode,
  Help,
  Parser
});
Chain.createProxy(math);
export { embeddedDocs as docs } from '../expression/embeddedDocs/embeddedDocs.js';