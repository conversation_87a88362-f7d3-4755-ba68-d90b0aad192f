/**
 * THIS FILE IS AUTO-GENERATED
 * DON'T MAKE CHANGES HERE
 */
import { DenseMatrixDependencies } from './dependenciesDenseMatrixClass.generated.js';
import { SparseMatrixDependencies } from './dependenciesSparseMatrixClass.generated.js';
import { bignumberDependencies } from './dependenciesBignumber.generated.js';
import { concatDependencies } from './dependenciesConcat.generated.js';
import { matrixDependencies } from './dependenciesMatrix.generated.js';
import { typedDependencies } from './dependenciesTyped.generated.js';
import { createLarger } from '../../factoriesAny.js';
export var largerDependencies = {
  DenseMatrixDependencies,
  SparseMatrixDependencies,
  bignumberDependencies,
  concatDependencies,
  matrixDependencies,
  typedDependencies,
  createLarger
};