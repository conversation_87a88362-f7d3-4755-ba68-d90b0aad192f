/**
 * THIS FILE IS AUTO-GENERATED
 * DON'T MAKE CHANGES HERE
 */
import { DenseMatrixDependencies } from './dependenciesDenseMatrixClass.generated.js';
import { SparseMatrixDependencies } from './dependenciesSparseMatrixClass.generated.js';
import { concatDependencies } from './dependenciesConcat.generated.js';
import { divideScalarDependencies } from './dependenciesDivideScalar.generated.js';
import { equalScalarDependencies } from './dependenciesEqualScalar.generated.js';
import { matrixDependencies } from './dependenciesMatrix.generated.js';
import { typedDependencies } from './dependenciesTyped.generated.js';
import { createDotDivide } from '../../factoriesAny.js';
export var dotDivideDependencies = {
  DenseMatrixDependencies,
  SparseMatrixDependencies,
  concatDependencies,
  divideScalarDependencies,
  equalScalarDependencies,
  matrixDependencies,
  typedDependencies,
  createDotDivide
};