/**
 * THIS FILE IS AUTO-GENERATED
 * DON'T MAKE CHANGES HERE
 */
import { BigNumberDependencies } from './dependenciesBigNumberClass.generated.js';
import { ComplexDependencies } from './dependenciesComplexClass.generated.js';
import { FractionDependencies } from './dependenciesFractionClass.generated.js';
import { absDependencies } from './dependenciesAbs.generated.js';
import { addScalarDependencies } from './dependenciesAddScalar.generated.js';
import { divideScalarDependencies } from './dependenciesDivideScalar.generated.js';
import { equalDependencies } from './dependenciesEqual.generated.js';
import { fixDependencies } from './dependenciesFix.generated.js';
import { formatDependencies } from './dependenciesFormat.generated.js';
import { isNumericDependencies } from './dependenciesIsNumeric.generated.js';
import { multiplyScalarDependencies } from './dependenciesMultiplyScalar.generated.js';
import { numberDependencies } from './dependenciesNumber.generated.js';
import { powDependencies } from './dependenciesPow.generated.js';
import { roundDependencies } from './dependenciesRound.generated.js';
import { subtractScalarDependencies } from './dependenciesSubtractScalar.generated.js';
import { toBestDependencies } from './dependenciesToBest.generated.js';
import { createUnitClass } from '../../factoriesAny.js';
export var UnitDependencies = {
  BigNumberDependencies,
  ComplexDependencies,
  FractionDependencies,
  absDependencies,
  addScalarDependencies,
  divideScalarDependencies,
  equalDependencies,
  fixDependencies,
  formatDependencies,
  isNumericDependencies,
  multiplyScalarDependencies,
  numberDependencies,
  powDependencies,
  roundDependencies,
  subtractScalarDependencies,
  toBestDependencies,
  createUnitClass
};