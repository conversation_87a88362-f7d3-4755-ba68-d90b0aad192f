{"name": "mathjs", "version": "14.6.0", "description": "Math.js is an extensive math library for JavaScript and Node.js. It features a flexible expression parser with support for symbolic computation, comes with a large set of built-in functions and constants, and offers an integrated solution to work with different data types like numbers, big numbers, complex numbers, fractions, units, and matrices.", "author": "<PERSON><PERSON> <<EMAIL>> (https://github.com/josdejong)", "homepage": "https://mathjs.org", "repository": {"type": "git", "url": "https://github.com/josdejong/mathjs.git"}, "license": "Apache-2.0", "keywords": ["math", "mathematics", "functions", "numeric", "algebra", "parser", "expression", "number", "bignumber", "complex", "fraction", "matrix", "unit"], "dependencies": {"@babel/runtime": "^7.26.10", "complex.js": "^2.2.5", "decimal.js": "^10.4.3", "escape-latex": "^1.2.0", "fraction.js": "^5.2.1", "javascript-natural-sort": "^0.7.1", "seedrandom": "^3.0.5", "tiny-emitter": "^2.1.0", "typed-function": "^4.2.1"}, "devDependencies": {"@babel/core": "7.28.0", "@babel/plugin-transform-object-assign": "7.27.1", "@babel/plugin-transform-optional-catch-binding": "7.27.1", "@babel/plugin-transform-runtime": "7.28.0", "@babel/preset-env": "7.28.0", "@babel/register": "7.27.1", "@types/assert": "1.5.11", "@types/mocha": "10.0.10", "@typescript-eslint/eslint-plugin": "8.38.0", "@typescript-eslint/parser": "8.38.0", "assert": "2.1.0", "babel-loader": "10.0.0", "c8": "10.1.3", "codecov": "3.8.3", "core-js": "3.44.0", "del": "8.0.0", "dtslint": "4.2.1", "eigen": "0.2.2", "eslint": "8.57.1", "eslint-config-prettier": "9.1.0", "eslint-config-standard": "17.1.0", "eslint-plugin-import": "2.32.0", "eslint-plugin-mocha": "10.5.0", "eslint-plugin-n": "16.6.2", "eslint-plugin-prettier": "5.5.3", "eslint-plugin-promise": "6.6.0", "expect-type": "1.2.2", "expr-eval": "2.0.2", "fancy-log": "2.0.0", "glob": "11.0.3", "gulp": "5.0.1", "gulp-babel": "8.0.0", "handlebars": "4.7.8", "jsep": "1.4.0", "karma": "6.4.4", "karma-firefox-launcher": "2.1.3", "karma-mocha": "2.0.1", "karma-mocha-reporter": "2.2.5", "karma-spec-reporter": "0.0.36", "karma-webdriver-launcher": "1.0.8", "karma-webpack": "5.0.1", "mkdirp": "3.0.1", "mocha": "11.7.1", "mocha-junit-reporter": "2.2.1", "ndarray": "1.0.19", "ndarray-determinant": "1.0.0", "ndarray-gemm": "1.0.0", "ndarray-ops": "1.2.2", "ndarray-pack": "1.2.1", "numericjs": "1.2.6", "prettier": "3.6.2", "process": "0.11.10", "sinon": "21.0.0", "sylvester": "0.0.21", "tinybench": "4.0.1", "ts-node": "10.9.2", "typescript": "5.8.3", "webpack": "5.100.2", "zeros": "1.0.0"}, "type": "module", "main": "./lib/cjs", "types": "./types/index.d.ts", "module": "./lib/esm", "unpkg": "./lib/browser/math.js", "jsdelivr": "./lib/browser/math.js", "exports": {".": {"types": "./types/index.d.ts", "import": "./lib/esm/index.js", "require": "./lib/cjs/index.js"}, "./number": {"types": "./types/index.d.ts", "import": "./lib/esm/number.js", "require": "./lib/cjs/number.js"}, "./lib/esm/number": {"types": "./types/index.d.ts", "import": "./lib/esm/number.js", "require": "./lib/cjs/number.js"}, "./package.json": "./package.json", "./lib/browser/math.js": "./lib/browser/math.js", "./lib/browser/math.js.map": "./lib/browser/math.js.map"}, "files": ["bin", "lib", "types", "LICENSE", "NOTICE", "README.md", "HISTORY.md", "CONTRIBUTING.md"], "directories": {"bin": "./bin", "lib": "./lib"}, "scripts": {"build": "gulp && npm run update-authors", "build-and-test": "npm run build && npm run test:all && npm run lint", "build:clean": "gulp clean", "build:docs": "gulp docs", "compile": "gulp compile", "watch": "gulp watch", "lint": "eslint --cache --max-warnings 0 src/ test/ types/", "format": "npm run lint -- --fix", "validate:ascii": "gulp validate:ascii", "test": "npm run test:src && npm run lint", "test:src": "mocha test/unit-tests", "test:generated": "mocha test/generated-code-tests", "test:node": "mocha test/node-tests/*.test.js test/node-tests/**/*.test.js", "test:all": "npm run test:src && npm run test:generated && npm run test:node && npm run test:types", "test:browser": "karma start test/browser-test-config/local-karma.js", "test:lambdatest": "karma start test/browser-test-config/lambdatest-karma.js", "test:types": " tsc -p ./tsconfig.json && node --loader ts-node/esm ./test/typescript-tests/testTypes.ts", "coverage": "c8 --reporter=lcov --reporter=text-summary mocha test/unit-tests && echo \"\nDetailed coverage report is available at ./coverage/lcov-report/index.html\"", "prepublishOnly": "npm run test:all && npm run lint", "update-authors": "node ./tools/update-authors.js"}, "bin": {"mathjs": "./bin/cli.js"}, "engines": {"node": ">= 18"}, "bugs": {"url": "https://github.com/josdejong/mathjs/issues"}, "sideEffects": false}