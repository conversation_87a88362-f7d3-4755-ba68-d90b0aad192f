

> @jimp/plugin-blur@1.1.2 test /Users/<USER>/Documents/jimp/plugins/plugin-blur
> vitest "--watch=false"


[7m[1m[36m RUN [39m[22m[27m [36mv2.0.5[39m [90m/Users/<USER>/Documents/jimp/plugins/plugin-blur[39m

[?25l [90m·[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [90m·[39m hasAlpha[2m (1)[22m
     [90m·[39m image with alpha
[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m[33m 2656[2mms[22m[39m
   [32m✓[39m hasAlpha[2m (1)[22m[33m 2656[2mms[22m[39m
     [32m✓[39m image with alpha[33m 2656[2mms[22m[39m
[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m[33m 2656[2mms[22m[39m
   [32m✓[39m hasAlpha[2m (1)[22m[33m 2656[2mms[22m[39m
     [32m✓[39m image with alpha[33m 2656[2mms[22m[39m

[2m Test Files [22m [1m[32m1 passed[39m[22m[90m (1)[39m
[2m      Tests [22m [1m[32m1 passed[39m[22m[90m (1)[39m
[2m   Start at [22m 01:33:52
[2m   Duration [22m 4.88s[2m (transform 1.16s, setup 0ms, collect 1.40s, tests 2.66s, environment 0ms, prepare 511ms)[22m

[?25h[?25h
