{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../src/index.ts"], "names": [], "mappings": ";;;AACA,qCAA4D;AAC5D,uCAAoC;AACpC,uDAA+E;AAC/E,mDAA2D;AAC3D,6BAAwB;AAExB,MAAM,oBAAoB,GAAG,OAAC,CAAC,MAAM,CAAC;IACpC,uCAAuC;IACvC,CAAC,EAAE,OAAC,CAAC,MAAM,EAAE;IACb,wCAAwC;IACxC,CAAC,EAAE,OAAC,CAAC,MAAM,EAAE;IACb,sDAAsD;IACtD,KAAK,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;IAC5B,iDAAiD;IACjD,IAAI,EAAE,OAAC,CAAC,UAAU,CAAC,8BAAc,CAAC,CAAC,QAAQ,EAAE;CAC9C,CAAC,CAAC;AAIU,QAAA,OAAO,GAAG;IACrB;;;;;;;;;;;;;;OAcG;IACH,OAAO,CAAsB,KAAQ,EAAE,OAAuB;QAC5D,MAAM,EACJ,CAAC,EACD,CAAC,EACD,KAAK,GAAG,sBAAe,CAAC,MAAM,GAAG,oBAAa,CAAC,MAAM,EACrD,IAAI,GACL,GAAG,oBAAoB,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;QAExC,MAAM,KAAK,GAAG,KAAK,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;QACrC,MAAM,KAAK,GAAG,KAAK,IAAI,CAAC,CAAC;QAEzB,kDAAkD;QAClD,IACE,CAAC,CACC,CAAC,KAAK,KAAK,CAAC,IAAI,CAAC,CAAC,KAAK,GAAG,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC;YACvC,CAAC,KAAK,KAAK,CAAC,IAAI,CAAC,CAAC,KAAK,GAAG,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,CACxC,EACD,CAAC;YACD,MAAM,IAAI,KAAK,CAAC,2CAA2C,CAAC,CAAC;QAC/D,CAAC;QAED,MAAM,MAAM,GAAG,KAAK,IAAI,CAAC,CAAC,CAAC,UAAU;QACrC,MAAM,MAAM,GAAG,KAAK,IAAI,CAAC,CAAC,CAAC,UAAU;QAErC,MAAM,CAAC,GACL,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC,MAAM,CAAC,KAAK,GAAG,KAAK,CAAC,MAAM,CAAC,MAAM;YAC9C,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,MAAM,CAAC,MAAM;YACzB,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC;QAE7B,MAAM,CAAC,GAAG,uBAAa,CAAC,KAAK,CAAC,IAAA,aAAK,EAAC,KAAK,CAAC,EAAE,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC;QAEzD,KAAK,GAAG,uBAAa,CAAC,MAAM,CAAC,KAAK,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC;QAEpD,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE;YACxB,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,UAAU,EAAE,GAAG,CAAC,CAAC;QACzD,CAAC,CAAC,CAAC;QAEH,KAAK,GAAG,qBAAW,CAAC,IAAI,CAAC,KAAK,EAAE;YAC9B,GAAG,EAAE,CAAC;YACN,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,GAAG,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM;YACvD,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM;SAC1D,CAAC,CAAC;QAEH,OAAO,KAAK,CAAC;IACf,CAAC;CACF,CAAC"}