

> @jimp/plugin-circle@1.1.2 test /Users/<USER>/Documents/jimp/plugins/plugin-circle
> vitest "--watch=false"


[7m[1m[36m RUN [39m[22m[27m [36mv2.0.5[39m [90m/Users/<USER>/Documents/jimp/plugins/plugin-circle[39m

[?25l [90m·[39m [2msrc/[22mindex[2m.test.ts[22m[2m (3)[22m
   [90m·[39m Circle[2m (3)[22m
     [90m·[39m makes a circle based on image height and width
     [90m·[39m makes a circle using provided radius
     [90m·[39m should 
[?25l[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (3)[22m
   [32m✓[39m Circle[2m (3)[22m
     [32m✓[39m makes a circle based on image height and width
     [32m✓[39m makes a circle using provided radius
     [32m✓[39m should 
[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (3)[22m
   [32m✓[39m Circle[2m (3)[22m
     [32m✓[39m makes a circle based on image height and width
     [32m✓[39m makes a circle using provided radius
     [32m✓[39m should 

[2m Test Files [22m [1m[32m1 passed[39m[22m[90m (1)[39m
[2m      Tests [22m [1m[32m3 passed[39m[22m[90m (3)[39m
[2m   Start at [22m 01:33:39
[2m   Duration [22m 2.52s[2m (transform 449ms, setup 0ms, collect 761ms, tests 27ms, environment 0ms, prepare 440ms)[22m

[?25h[?25h
