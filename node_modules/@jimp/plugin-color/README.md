<div align="center">
  <img width="200" height="200"
    src="https://s3.amazonaws.com/pix.iemoji.com/images/emoji/apple/ios-11/256/crayon.png">
  <h1>@jimp/utils</h1>
  <p>Utils for jimp extensions.</p>
</div>

- [color](http://jimp-dev.github.io/jimp/api/jimp/classes/jimp#color)
- [brightness](http://jimp-dev.github.io/jimp/api/jimp/classes/jimp#brightness)
- [contrast](http://jimp-dev.github.io/jimp/api/jimp/classes/jimp#contrast)
- [posterize](http://jimp-dev.github.io/jimp/api/jimp/classes/jimp#posterize)
- [opacity](http://jimp-dev.github.io/jimp/api/jimp/classes/jimp#opacity)
- [sepia](http://jimp-dev.github.io/jimp/api/jimp/classes/jimp#sepia)
- [fade](http://jimp-dev.github.io/jimp/api/jimp/classes/jimp#fade)
- [convolution](http://jimp-dev.github.io/jimp/api/jimp/classes/jimp#convolution)
- [opaque](http://jimp-dev.github.io/jimp/api/jimp/classes/jimp#opaque)
- [pixelate](http://jimp-dev.github.io/jimp/api/jimp/classes/jimp#pixelate)
- [convolute](http://jimp-dev.github.io/jimp/api/jimp/classes/jimp#convolute)
- [normalize](http://jimp-dev.github.io/jimp/api/jimp/classes/jimp#normalize)
- [invert](http://jimp-dev.github.io/jimp/api/jimp/classes/jimp#invert)
