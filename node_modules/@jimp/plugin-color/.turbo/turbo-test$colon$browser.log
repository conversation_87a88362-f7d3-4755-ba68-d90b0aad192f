
> @jimp/plugin-color@1.1.1 test:browser /Users/<USER>/Documents/jimp/plugins/plugin-color
> vitest --config vitest.config.browser.mjs "--watch=false" "--u"


[7m[1m[36m RUN [39m[22m[27m [36mv1.4.0[39m [90m/Users/<USER>/Documents/jimp/plugins/plugin-color[39m
[2m[32m      Browser runner started at http://localhost:5173/[39m[22m

[?25l [90m·[39m [2msrc/[22mnormalize[2m.test.ts[22m[2m (2)[22m
 [90m·[39m [2msrc/[22mconvolution[2m.test.ts[22m[2m (3)[22m
[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[2K[1A[2K[1A[2K[G [90m·[39m [2msrc/[22mnormalize[2m.test.ts[22m[2m (2)[22m
 [90m·[39m [2msrc/[22mconvolution[2m.test.ts[22m[2m (3)[22m
 [90m·[39m [2msrc/[22mindex[2m.test.ts[22m[2m (7)[22m
[?25l[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mnormalize[2m.test.ts[22m[2m (2)[22m
 [90m·[39m [2msrc/[22mconvolution[2m.test.ts[22m[2m (3)[22m
 [90m·[39m [2msrc/[22mindex[2m.test.ts[22m[2m (7)[22m
[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mnormalize[2m.test.ts[22m[2m (2)[22m
 [32m✓[39m [2msrc/[22mconvolution[2m.test.ts[22m[2m (3)[22m
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (7)[22m

[2m Test Files [22m [1m[32m3 passed[39m[22m[90m (3)[39m
[2m      Tests [22m [1m[32m12 passed[39m[22m[90m (12)[39m
[2m   Start at [22m 00:40:22
[2m   Duration [22m 3.94s[2m (transform 0ms, setup 0ms, collect 1.07s, tests 13ms, environment 0ms, prepare 0ms)[22m

[?25h[?25h
