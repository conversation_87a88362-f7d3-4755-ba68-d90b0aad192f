

> @jimp/plugin-mask@1.1.2 test:browser /Users/<USER>/Documents/jimp/plugins/plugin-mask
> vitest --config vitest.config.browser.mjs "--watch=false"

Re-optimizing dependencies because lockfile has changed
Port 5173 is in use, trying another one...
Port 5174 is in use, trying another one...
Port 5175 is in use, trying another one...
Port 5176 is in use, trying another one...
Port 5177 is in use, trying another one...

[7m[1m[36m RUN [39m[22m[27m [36mv2.0.5[39m [90m/Users/<USER>/Documents/jimp/plugins/plugin-mask[39m
[2m[32m      Browser runner started at http://localhost:5178/[39m[22m


[31m⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯[1m[7m Unhandled Rejection [27m[22m⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯[39m
[31m[1mError[22m: The service was stopped: write EPIPE[39m
[90m [2m❯[22m ../../node_modules/.pnpm/esbuild@0.21.5/node_modules/esbuild/lib/main.js:[2m993:26[22m[39m
[90m [2m❯[22m responseCallbacks.<computed> ../../node_modules/.pnpm/esbuild@0.21.5/node_modules/esbuild/lib/main.js:[2m622:9[22m[39m
[90m [2m❯[22m afterClose ../../node_modules/.pnpm/esbuild@0.21.5/node_modules/esbuild/lib/main.js:[2m613:28[22m[39m
[90m [2m❯[22m ../../node_modules/.pnpm/esbuild@0.21.5/node_modules/esbuild/lib/main.js:[2m1983:18[22m[39m
[90m [2m❯[22m onwriteError node:internal/streams/writable:[2m418:3[22m[39m
[90m [2m❯[22m process.processTicksAndRejections node:internal/process/task_queues:[2m84:21[22m[39m




[41m[30m ELIFECYCLE [39m[49m [31mCommand failed with exit code 1.[39m
