{"version": 3, "file": "composite-modes.d.ts", "sourceRoot": "", "sources": ["../../../src/utils/composite-modes.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,SAAS,EAAE,MAAM,aAAa,CAAC;AAExC,wBAAgB,OAAO,CAAC,GAAG,EAAE,SAAS,EAAE,GAAG,EAAE,SAAS,EAAE,GAAG,SAAI;;;;;EAU9D;AAED,wBAAgB,OAAO,CAAC,GAAG,EAAE,SAAS,EAAE,GAAG,EAAE,SAAS,EAAE,GAAG,SAAI;;;;;EAU9D;AAED,wBAAgB,QAAQ,CAAC,GAAG,EAAE,SAAS,EAAE,GAAG,EAAE,SAAS,EAAE,GAAG,SAAI;;;;;EAkB/D;AAED,wBAAgB,GAAG,CAAC,GAAG,EAAE,SAAS,EAAE,GAAG,EAAE,SAAS,EAAE,GAAG,SAAI;;;;;EAkB1D;AAED,wBAAgB,MAAM,CAAC,GAAG,EAAE,SAAS,EAAE,GAAG,EAAE,SAAS,EAAE,GAAG,SAAI;;;;;EAoC7D;AAED,wBAAgB,OAAO,CAAC,GAAG,EAAE,SAAS,EAAE,GAAG,EAAE,SAAS,EAAE,GAAG,SAAI;;;;;EAgC9D;AAED,wBAAgB,MAAM,CAAC,GAAG,EAAE,SAAS,EAAE,GAAG,EAAE,SAAS,EAAE,GAAG,SAAI;;;;;EA8B7D;AAED,wBAAgB,OAAO,CAAC,GAAG,EAAE,SAAS,EAAE,GAAG,EAAE,SAAS,EAAE,GAAG,SAAI;;;;;EA8B9D;AAED,wBAAgB,SAAS,CAAC,GAAG,EAAE,SAAS,EAAE,GAAG,EAAE,SAAS,EAAE,GAAG,SAAI;;;;;EAgChE;AAED,wBAAgB,UAAU,CAAC,GAAG,EAAE,SAAS,EAAE,GAAG,EAAE,SAAS,EAAE,GAAG,SAAI;;;;;EAkBjE;AAED,wBAAgB,SAAS,CAAC,GAAG,EAAE,SAAS,EAAE,GAAG,EAAE,SAAS,EAAE,GAAG,SAAI;;;;;EAoChE;AAED,eAAO,MAAM,KAAK,6LAYR,CAAC;AACX,MAAM,MAAM,aAAa,GAAG,CAAC,OAAO,KAAK,CAAC,CAAC,MAAM,CAAC,CAAC"}