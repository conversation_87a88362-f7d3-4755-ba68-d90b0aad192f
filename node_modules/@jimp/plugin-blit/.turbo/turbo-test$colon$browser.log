
> @jimp/plugin-blit@1.1.1 test:browser /Users/<USER>/Documents/jimp/plugins/plugin-blit
> vitest --config vitest.config.browser.mjs "--watch=false" "--u"


[7m[1m[36m RUN [39m[22m[27m [36mv1.4.0[39m [90m/Users/<USER>/Documents/jimp/plugins/plugin-blit[39m
[2m[32m      Browser runner started at http://localhost:5176/[39m[22m

[?25l [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
   [32m✓[39m Blit over image[2m (6)[22m
     [32m✓[39m blit on top, with no crop
     [32m✓[39m blit on middle, with no crop
     [32m✓[39m blit on middle, with x,y crop
     [32m✓[39m blit on middle, with x,y,w,h crop
     [32m✓[39m blit partially out, on top-left
     [32m✓[39m blit partially out, on bottom-right
[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (6)[22m
   [32m✓[39m Blit over image[2m (6)[22m
     [32m✓[39m blit on top, with no crop
     [32m✓[39m blit on middle, with no crop
     [32m✓[39m blit on middle, with x,y crop
     [32m✓[39m blit on middle, with x,y,w,h crop
     [32m✓[39m blit partially out, on top-left
     [32m✓[39m blit partially out, on bottom-right

[2m Test Files [22m [1m[32m1 passed[39m[22m[90m (1)[39m
[2m      Tests [22m [1m[32m6 passed[39m[22m[90m (6)[39m
[2m   Start at [22m 00:42:10
[2m   Duration [22m 6.50s[2m (transform 0ms, setup 0ms, collect 308ms, tests 4ms, environment 0ms, prepare 0ms)[22m

[?25h[?25h
