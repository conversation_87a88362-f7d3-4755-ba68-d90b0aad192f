{"name": "escape-latex", "version": "1.2.0", "description": "Escape LaTeX special characters with Javascript", "main": "./dist/index.js", "files": ["dist"], "scripts": {"test": "mocha --require babel-core/register -u tdd ./src/**/*.test.js", "preversion": "npm test && npm run build", "postversion": "git push && git push --tags", "precommit": "npm run lint && lint-staged", "prettier": "prettier --write ./src/**/*.js", "lint": "eslint ./src", "init": "mkdir dist", "clean": "rm -rf dist", "prebuild": "npm run clean && npm run init", "build": "babel ./src -d ./dist --ignore index.test.js"}, "lint-staged": {"*.{js,json,css,md}": ["npm run prettier", "git add"]}, "eslintConfig": {"parserOptions": {"ecmaVersion": 8}, "extends": ["eslint:recommended", "google", "prettier"], "env": {"node": "true"}}, "prettier": {"trailingComma": "all"}, "repository": {"type": "git", "url": "https://github.com/dangmai/escape-latex"}, "keywords": ["latex", "escape"], "devDependencies": {"babel-cli": "^6.26.0", "babel-core": "^6.26.0", "babel-plugin-transform-object-assign": "^6.22.0", "babel-preset-env": "^1.6.1", "chai": "^4.1.2", "eslint": "^5.0.1", "eslint-config-google": "^0.9.1", "eslint-config-prettier": "^3.0.1", "husky": "^0.14.3", "lint-staged": "^7.0.5", "mocha": "^5.0.0", "prettier": "^1.9.2"}, "author": "<PERSON><PERSON>", "license": "MIT"}