{"name": "encoding", "version": "0.1.12", "description": "Convert encodings, uses iconv by default and fallbacks to iconv-lite if needed", "main": "lib/encoding.js", "scripts": {"test": "nodeunit test"}, "repository": "https://github.com/andris9/encoding.git", "author": "<PERSON><PERSON>", "license": "MIT", "dependencies": {"iconv-lite": "~0.4.13"}, "devDependencies": {"iconv": "~2.1.11", "nodeunit": "~0.9.1"}}