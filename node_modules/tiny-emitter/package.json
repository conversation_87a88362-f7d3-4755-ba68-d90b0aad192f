{"name": "tiny-emitter", "version": "2.1.0", "description": "A tiny (less than 1k) event emitter library", "main": "index.js", "scripts": {"test-node": "tape test/index.js | tap-format-spec", "test": "testling | tap-format-spec", "bundle": "node_modules/.bin/browserify index.js > dist/tinyemitter.js -s TinyEmitter && echo 'Bundled'", "minify": "node_modules/.bin/uglifyjs dist/tinyemitter.js -o dist/tinyemitter.min.js -m && echo 'Minified'", "build": "npm test && npm run bundle && npm run minify", "size": "node_modules/.bin/uglifyjs index.js -o minified.js -m && ls -l && rm minified.js"}, "repository": {"type": "git", "url": "https://github.com/scottcorgan/tiny-emitter.git"}, "keywords": ["event", "emitter", "pubsub", "tiny", "events", "bind"], "author": "<PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/scottcorgan/tiny-emitter/issues"}, "devDependencies": {"@tap-format/spec": "0.2.0", "browserify": "11.2.0", "tape": "4.2.1", "testling": "1.7.1", "uglify-js": "2.5.0"}, "testling": {"files": ["test/index.js"], "browsers": ["iexplore/10.0", "iexplore/9.0", "firefox/16..latest", "chrome/22..latest", "safari/5.1..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2..latest"]}}