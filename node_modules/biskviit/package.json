{"name": "biskviit", "version": "1.0.1", "description": "Yet another module for http cookie handling", "main": "lib/biskviit.js", "scripts": {"test": "grunt"}, "repository": {"type": "git", "url": "git+https://github.com/andris9/biskviit.git"}, "keywords": ["HTTP", "cookie", "cookies"], "author": "<PERSON><PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/andris9/biskviit/issues"}, "homepage": "https://github.com/andris9/biskviit#readme", "devDependencies": {"chai": "^3.2.0", "grunt": "^0.4.5", "grunt-contrib-jshint": "^0.11.3", "grunt-mocha-test": "^0.12.7", "mocha": "^2.3.2"}, "dependencies": {"psl": "^1.1.7"}, "engines": {"node": ">=1.0.0"}}