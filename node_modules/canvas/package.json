{"name": "canvas", "description": "Canvas graphics API backed by Cairo", "version": "3.1.2", "author": "<PERSON><PERSON> <<EMAIL>>", "main": "index.js", "browser": "browser.js", "types": "index.d.ts", "contributors": ["<PERSON> <<EMAIL>>", "<PERSON>agg <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>"], "keywords": ["canvas", "graphic", "graphics", "pixman", "cairo", "image", "images", "pdf"], "homepage": "https://github.com/Automattic/node-canvas", "repository": "git://github.com/Automattic/node-canvas.git", "scripts": {"prebenchmark": "node-gyp build", "benchmark": "node benchmarks/run.js", "lint": "standard examples/*.js test/server.js test/public/*.js benchmarks/run.js lib/context2d.js util/has_lib.js browser.js index.js", "test": "mocha test/*.test.js", "pretest-server": "node-gyp build", "test-server": "node test/server.js", "generate-wpt": "node ./test/wpt/generate.js", "test-wpt": "mocha test/wpt/generated/*.js", "install": "prebuild-install -r napi || node-gyp rebuild", "tsd": "tsd"}, "files": ["binding.gyp", "browser.js", "index.d.ts", "index.js", "lib/", "src/", "util/"], "dependencies": {"node-addon-api": "^7.0.0", "prebuild-install": "^7.1.3"}, "devDependencies": {"@types/node": "^10.12.18", "assert-rejects": "^1.0.0", "express": "^4.16.3", "js-yaml": "^4.1.0", "mocha": "^5.2.0", "pixelmatch": "^4.0.2", "standard": "^12.0.1", "tsd": "^0.29.0", "typescript": "^4.2.2"}, "engines": {"node": "^18.12.0 || >= 20.9.0"}, "binary": {"napi_versions": [7]}, "license": "MIT"}