-- 钉建机器人数据库表结构
-- 创建时间: 2025-07-26

-- 创建数据库（如果不存在）
CREATE DATABASE IF NOT EXISTS dingjian_bot 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;

USE dingjian_bot;

-- 1. 账单记录表（统一入款和下发）
CREATE TABLE IF NOT EXISTS transactions (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    type ENUM('income', 'expense') NOT NULL COMMENT '类型：income=入款，expense=下发',
    user_id BIGINT NOT NULL COMMENT 'Telegram用户ID',
    username VARCHAR(255) DEFAULT NULL COMMENT 'Telegram用户名',
    display_name VARCHAR(255) DEFAULT NULL COMMENT '显示名称',
    group_id BIGINT NOT NULL COMMENT '群组ID',
    group_name VARCHAR(255) DEFAULT NULL COMMENT '群组名称',
    
    -- 金额相关
    original_amount DECIMAL(18,2) NOT NULL COMMENT '原始金额',
    original_currency VARCHAR(10) NOT NULL DEFAULT 'CNY' COMMENT '原始币种',
    converted_amount DECIMAL(18,2) NOT NULL COMMENT '转换后金额（统一为CNY）',
    exchange_rate DECIMAL(10,4) DEFAULT NULL COMMENT '汇率',
    
    -- 时间戳
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    transaction_date DATE NOT NULL COMMENT '交易日期',
    
    -- 元数据
    message_id BIGINT DEFAULT NULL COMMENT '消息ID',
    is_deleted BOOLEAN DEFAULT FALSE COMMENT '是否已删除',
    deleted_at TIMESTAMP NULL COMMENT '删除时间',
    deleted_by BIGINT DEFAULT NULL COMMENT '删除操作员ID',
    
    -- 索引
    INDEX idx_group_date (group_id, transaction_date),
    INDEX idx_user_id (user_id),
    INDEX idx_type_date (type, transaction_date),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='交易记录表';

-- 2. 权限管理表
CREATE TABLE IF NOT EXISTS permissions (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT NOT NULL COMMENT 'Telegram用户ID',
    username VARCHAR(255) DEFAULT NULL COMMENT 'Telegram用户名',
    group_id BIGINT DEFAULT NULL COMMENT '群组ID（NULL表示全局权限）',
    role ENUM('owner', 'super_admin', 'operator', 'group_operator') NOT NULL COMMENT '角色',
    
    -- 时间戳
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    created_by BIGINT DEFAULT NULL COMMENT '创建者ID',
    
    -- 状态
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否激活',
    
    -- 唯一约束
    UNIQUE KEY uk_user_group_role (user_id, group_id, role),
    INDEX idx_user_id (user_id),
    INDEX idx_group_id (group_id),
    INDEX idx_role (role)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='权限管理表';

-- 3. 群组设置表
CREATE TABLE IF NOT EXISTS group_settings (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    group_id BIGINT NOT NULL UNIQUE COMMENT '群组ID',
    group_name VARCHAR(255) DEFAULT NULL COMMENT '群组名称',
    group_type VARCHAR(50) DEFAULT NULL COMMENT '群组类型',
    
    -- 设置项
    exchange_rate DECIMAL(10,4) DEFAULT 57.0000 COMMENT '默认汇率',
    fee_rate DECIMAL(10,4) DEFAULT 0.038 COMMENT '默认费率',
    daily_cut_time TIME DEFAULT '00:00:00' COMMENT '日切时间',
    timezone VARCHAR(50) DEFAULT 'Asia/Shanghai' COMMENT '时区',
    
    -- 功能开关
    accounting_enabled BOOLEAN DEFAULT TRUE COMMENT '记账功能是否启用',
    real_time_rate BOOLEAN DEFAULT FALSE COMMENT '实时汇率是否启用',
    auto_bill BOOLEAN DEFAULT TRUE COMMENT '自动发送账单',
    
    -- 时间戳
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_group_id (group_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='群组设置表';

-- 4. 系统配置表
CREATE TABLE IF NOT EXISTS system_settings (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    setting_key VARCHAR(100) NOT NULL UNIQUE COMMENT '配置键',
    setting_value TEXT COMMENT '配置值',
    setting_type ENUM('string', 'number', 'boolean', 'json') DEFAULT 'string' COMMENT '配置类型',
    description VARCHAR(500) DEFAULT NULL COMMENT '配置描述',
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_setting_key (setting_key)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统配置表';

-- 5. 操作日志表
CREATE TABLE IF NOT EXISTS operation_logs (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT NOT NULL COMMENT '操作用户ID',
    username VARCHAR(255) DEFAULT NULL COMMENT '操作用户名',
    group_id BIGINT DEFAULT NULL COMMENT '群组ID',
    
    operation_type VARCHAR(50) NOT NULL COMMENT '操作类型',
    operation_data JSON DEFAULT NULL COMMENT '操作数据',
    result ENUM('success', 'failed') NOT NULL COMMENT '操作结果',
    error_message TEXT DEFAULT NULL COMMENT '错误信息',
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '操作时间',
    
    INDEX idx_user_id (user_id),
    INDEX idx_group_id (group_id),
    INDEX idx_operation_type (operation_type),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='操作日志表';

-- 插入默认系统配置
INSERT INTO system_settings (setting_key, setting_value, setting_type, description) VALUES
('default_exchange_rate', '57.0000', 'number', '默认USDT汇率'),
('default_fee_rate', '0.038', 'number', '默认手续费率'),
('bot_version', '2.0.0', 'string', '机器人版本'),
('maintenance_mode', 'false', 'boolean', '维护模式'),
('backup_enabled', 'true', 'boolean', '自动备份是否启用')
ON DUPLICATE KEY UPDATE updated_at = CURRENT_TIMESTAMP;
